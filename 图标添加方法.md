1. https://www.iconfont.cn/ 注册账号进入团队 (找凯奇加入)

2. 加入想要的图标后下载至本地

3. 解压并替换 @/src/iconfont 下内容 (注意备份一个)

4. iconfont.css 添加两行代码

   ```js
   @font-face {
     font-family: "iconfont"; /* Project id 3912254 */
     src: url('iconfont.woff2?t=1680846569467') format('woff2'),
          url('iconfont.woff?t=1680846569467') format('woff'),
          url('iconfont.ttf?t=1680846569467') format('truetype');
   }
   
   //添加下面两行代码,其他内容均已存在
   [class^="el-icon-ali"],
   [class*="el-icon-ali"]
   
   .iconfont {
     font-family: "iconfont" !important;
     font-size: 16px;
     font-style: normal;
     -webkit-font-smoothing: antialiased;
     -moz-osx-font-smoothing: grayscale;
   }
   ```

   