<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>犯罪态势分析表格测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }

        .table-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .table-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .title-decoration {
            width: 4px;
            height: 24px;
            background: #409EFF;
            margin-right: 10px;
        }

        h2 {
            margin: 0;
            color: #303133;
            font-size: 18px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            border: 1px solid #EBEEF5;
            padding: 12px 8px;
            text-align: center;
            font-size: 14px;
        }

        th {
            background-color: #409EFF;
            color: white;
            font-weight: bold;
        }

        .crime-name {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: left;
            padding-left: 15px;
        }

        .growth-positive {
            color: #67C23A;
            font-weight: bold;
        }

        .growth-negative {
            color: #F56C6C;
            font-weight: bold;
        }

        .cell-content {
            cursor: pointer;
        }

        .cell-content:hover {
            color: #409EFF;
        }

        .toggle-btn {
            background: #E6A23C;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .toggle-btn:hover {
            background: #CF9236;
        }
    </style>
</head>
<body>
    <div class="table-container">
        <div class="table-title">
            <div class="title-decoration"></div>
            <h2>犯罪态势分析</h2>
        </div>

        <button class="toggle-btn" onclick="toggleDataSource()">
            切换到API数据
        </button>

        <table id="crimeTable">
            <thead id="tableHeader">
                <!-- 表头将通过JavaScript动态生成 -->
            </thead>
            <tbody id="tableBody">
                <!-- 数据将通过JavaScript填充 -->
            </tbody>
        </table>
    </div>

    <script>
        // 固定的犯罪数据
        const crimeStatisticsData = [
            {
                "罪名": "危险驾驶罪",
                "2023年数量": 307,
                "2024年数量": 233,
                "2024年1-5月数量": 92,
                "2025年1-5月数量": 102,
                "2024年-2023年增长数": -74,
                "2024年-2023年增长率": -24.10423,
                "2025年1-5月增长数": 10,
                "2025年1-5月增长率": 10.86957
            },
            {
                "罪名": "盗窃罪",
                "2023年数量": 137,
                "2024年数量": 164,
                "2024年1-5月数量": 80,
                "2025年1-5月数量": 66,
                "2024年-2023年增长数": 27,
                "2024年-2023年增长率": 19.70803,
                "2025年1-5月增长数": -14,
                "2025年1-5月增长率": -17.5
            },
            {
                "罪名": "诈骗罪",
                "2023年数量": 112,
                "2024年数量": 120,
                "2024年1-5月数量": 61,
                "2025年1-5月数量": 49,
                "2024年-2023年增长数": 8,
                "2024年-2023年增长率": 7.14286,
                "2025年1-5月增长数": -12,
                "2025年1-5月增长率": -19.67213
            }
        ];

        let useFixedData = true;
        let dynamicColumns = [];

        // 格式化百分比
        function formatPercentage(value) {
            if (value === null || value === undefined || value === '') return '-';
            return value.toFixed(2) + '%';
        }

        // 获取增长数据的样式类
        function getGrowthClass(value) {
            if (value === null || value === undefined || value === '') return '';
            const numValue = Number(value);
            if (numValue > 0) return 'growth-positive';
            if (numValue < 0) return 'growth-negative';
            return '';
        }

        // 根据列名和值获取样式类
        function getColumnClass(columnName, value) {
            if (columnName.includes('增长')) {
                return getGrowthClass(value);
            }
            return '';
        }

        // 格式化单元格值
        function formatCellValue(columnName, value) {
            if (value === null || value === undefined || value === '') return '-';

            if (columnName.includes('增长率')) {
                return formatPercentage(value);
            }

            return value;
        }

        // 生成动态列配置
        function generateDynamicColumns(data) {
            if (!data || data.length === 0) {
                dynamicColumns = [];
                return;
            }

            const firstRow = data[0];
            dynamicColumns = Object.keys(firstRow).map((key, index) => ({
                prop: key,
                label: key,
                fixed: index === 0 // 第一列固定
            }));
        }

        // 渲染表头
        function renderTableHeader() {
            const thead = document.getElementById('tableHeader');
            thead.innerHTML = '';

            const tr = document.createElement('tr');
            dynamicColumns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column.label;
                if (column.fixed) {
                    th.classList.add('crime-name');
                }
                tr.appendChild(th);
            });
            thead.appendChild(tr);
        }

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            let currentData = [];
            if (useFixedData) {
                currentData = crimeStatisticsData;
            } else {
                // 模拟不同时间段的数据结构
                currentData = [
                    {
                        "罪名": "危险驾驶罪",
                        "2022年数量": 280,
                        "2023年数量": 307,
                        "2022年-2023年增长数": 27,
                        "2022年-2023年增长率": 9.64286
                    },
                    {
                        "罪名": "盗窃罪",
                        "2022年数量": 120,
                        "2023年数量": 137,
                        "2022年-2023年增长数": 17,
                        "2022年-2023年增长率": 14.16667
                    }
                ];
            }

            // 生成动态列
            generateDynamicColumns(currentData);
            renderTableHeader();

            if (currentData.length === 0) {
                const tr = document.createElement('tr');
                tr.innerHTML = `<td colspan="${dynamicColumns.length}" style="text-align: center; color: #909399;">暂无数据</td>`;
                tbody.appendChild(tr);
                return;
            }

            currentData.forEach(row => {
                const tr = document.createElement('tr');
                dynamicColumns.forEach(column => {
                    const td = document.createElement('td');
                    const value = row[column.prop];
                    const formattedValue = formatCellValue(column.prop, value);

                    td.innerHTML = `<span class="cell-content ${getColumnClass(column.prop, value)}" onclick="handleCellClick('${row[dynamicColumns[0].prop]}', '${column.prop}', '${value}')">${formattedValue}</span>`;

                    if (column.fixed) {
                        td.classList.add('crime-name');
                    }

                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
        }

        // 切换数据源
        function toggleDataSource() {
            useFixedData = !useFixedData;
            const btn = document.querySelector('.toggle-btn');
            btn.textContent = useFixedData ? '切换到API数据' : '切换到固定数据';
            renderTable();

            // 显示当前使用的列结构
            console.log('当前动态列配置:', dynamicColumns);
        }

        // 处理单元格点击
        function handleCellClick(crimeName, column, value) {
            alert(`点击了 ${crimeName} 的 ${column} 数据: ${value}`);
        }

        // 初始化表格
        renderTable();
    </script>
</body>
</html>
