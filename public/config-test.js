// 不同客户后台后缀不同，打包后配置此文件url
const unityLogin = 'http://*************/login/#/login?ak=84711E46451'
// const unityLogin = 'http://*************/login/#/login?ak=1865052523C'
const PreviewFilePath = 'http://*************:8012/onlinePreview?url='
const MinioFileHost = 'http://*************:9000'
const API = {
  domain: '*************',
  // domain: 'localhost',
  unityAPI: 'http://*************:9000/jdpc-sso',
  chatAPI: 'http://*************',
  baseAPI: 'http://*************:19071/fuyang/data/center',
  instanceAPI: 'http://*************:9005/dev-dc-api-portal',
  ak:'1865052523C',
  sk:'318gam4637n'
}

window.globalConfig = {
  catalogueId: '682eeb20fab6df3304492b8f',
  unityLogin: {
    API: 'http://*************:9000/dev-dc-api-portal/api/sso/getTicket',
    name: '15088520941',
    pass: '7cc6990928964a6829f94f739628f89a',
    AK:'5E59177673BBD0E',
    SK:'87ih9ghjl78'
  },
  //资源库
  resourceSearch: {
    ak: "A5C814A0AA7",
    sk: "bna5af73ga6",
    defaultCode: '10',
    type: [
      { name:'全部资源',code:'10' },
      { name:'专项资源',code:'17' },
      { name:'案件资源',code:'18' }
    ],
    url: "http://*************/search/#/index"
  },
  qualityEvaluation: {
    url: "http://*************:9001/szag-lcjk-api/openapi",
    dwbm: "330702"
  },
  //一张图
  earlyWarning: {
    ak: "1483D41088C",
    sk: "13gng06ama4",
    url: "http://*************:19070/fuchune-station/#/earlyWarning"
  },
  //案件预警
  warningInfo: {
    ak: "1483D41088C",
    sk: "13gng06ama4",
    url: "http://*************:19070/fuchune-station/#/warningInfo",
    statistcsApi: API.baseAPI + '/rest/core/case/area/statistics',
    statistcsWarningInfoApi: API.baseAPI + '/rest/core/warningInfo/statistics',
    warningApi: API.baseAPI + '/rest/core/warningInfo'
  },
  //裁判文书
  JudicialDocument:{
    ak:"C2C648FD549",
    sk:"1426e9nj97d",
    url:"http://172.16.137.24/supervise-fcez/#/sign-in",
    suffix:"redirect=data-retrieval",
    statistcsApi: API.baseAPI + '/rest/core/other/ws/statistics',
  },
  //数据查询
  dataShow: {
    ak: "A5C814A0AA7",
    sk: "bna5af73ga6",
    url: "http://*************/search/#/index",
    statistcsApi:API.baseAPI + "/rest/core/other/dataShow/statistics"
  },
  //监督场景
  supervisoryScenario:{
    ak: "C2C648FD549",
    sk: "1426e9nj97d",
    url: "http://172.16.137.24/supervise-fcez/#/sign-in"
  },
  //统一量刑填报
  tylxglquery:{
    ak:"AB1156A482A",
    sk:"g6a27jc1023",
    url:"http://172.16.137.19:9527/tylxk-ui/#/sentencing-library/input"
  },
  //统一量刑管理
  tylxglmanage:{
    ak:"AB1156A482A",
    sk:"g6a27jc1023",
    url:"http://172.16.137.19:9527/tylxk-ui/#/sentencing-library/manage"
  },
  //实时计算
  xqjsquery:{
    ak:"1483D41088C",
    sk:"13gng06ama4",
    url:API.baseAPI + "/rest/core/other/xqjsquery"
  },
  //判决预警
  xqjsmanage:{
    ak:"",
    sk:"",
    url:""
  },
  //剥夺政治权利审监抗
  bdzzqlsjk:{
    ak:"C2C648FD549",
    sk:"1426e9nj97d",
    url:"http://172.16.137.24/supervise-fcez/#/sign-in",
    suffix:"redirect=clue-flow-bdzzql"
  },
  //涉赌人员拒绝执行
  sdryjjzs:{
    ak:"C2C648FD549",
    sk:"1426e9nj97d",
    url:"http://172.16.137.24/supervise-fcez/#/sign-in",
    suffix:"redirect=clue-flow-dbsxxgqd"
  },
  //判决信息查询
  pjxxcx:{
    ak:"",
    sk:"",
    url:""
  },
  //信息推送
  xxts:{
    ak:"",
    sk:"",
    url:""
  },
  //驾驶舱
  jsc:{
    dataCenter: "http://*************/fuyang/#/",
    ajzlzb: "http://143.82.0.122:8080/ajzlzb",
    //护水系统
    hsxt: "http://143.82.72.9:8082",
    //监所智慧纠违系统
    jszhjwxt: "http://143.82.72.9:8081",
    index: "#"
  },
  tabeTest:{
    width: { size: 100 },
    borders: {
      top: { style: "single", size: 4, color: "000000" },
      bottom: { style: "single", size: 4, color: "000000" },
      left: { style: "single", size: 4, color: "000000" },
      right: { style: "single", size: 4, color: "000000" },
      insideHorizontal: { style: "single", size: 2, color: "000000" },
      insideVertical: { style: "single", size: 2, color: "000000" }
    }
  },
  indexMenus: [
    {
      name: "数据中心",
      // icon: require("@/views/modules/dashboard/img/icon/数据中心.png"),
      children: [
        { name: "数据检索", mode: "dataShow" },
        { name: "判决书库", mode: "JudicialDocument" },
        { name: "阅览室" ,url: "fileSystem"}
      ]
    },
    {
      name: "场景中心",
      // icon: require("@/views/modules/dashboard/img/icon/场景中心.png"),
      children: [
        { name: "案发区域预警", mode: "earlyWarning" },
        { name: "剥夺政治权力监督", mode: "bdzzqlsjk" },
        { name: "失信被执行人涉赌拒执监督", mode: "sdryjjzs" },
        { name: "护水系统", cockpit: "hsxt" },
        { name: "监所智慧纠违系统", cockpit: "jszhjwxt" }
      ]
    },
    {
      name: "绩效评估",
      // icon: require("@/views/modules/dashboard/img/icon/绩效评估.png"),
      url: "/durationStatistics"
    },
    {
      name: "办案辅助",
      // icon: require("@/views/modules/dashboard/img/icon/办案辅助.png"),
      children: [
        { name: "统一量刑填报", mode: "tylxglquery" },
        { name: "统一量刑管理", mode: "tylxglmanage" },
        // { name: "刑期计算工具", url: "calculationSentence" },
        { name: "信息推送", url: "sms" }
      ]
    },
    {
      name: "驾驶舱",
      // icon: require("@/views/modules/dashboard/img/icon/驾驶舱.png"),
      children: [
        { name: "首页驾驶舱", cockpit: "index" },
        { name: "数据中心驾驶舱", cockpit: "dataCenter" },
        { name: "案管平台", cockpit: "ajzlzb" }
      ]
    },
    {
      name: "常用工具",
      // icon: require("@/views/modules/dashboard/img/icon/驾驶舱.png"),
      children: [
        { name: "刑期计算工具",url: "calculationSentence" },
        { name: "文件上传", dialog: "dataCenter" }
        // { name: "下载文件", url: "fileSystem" }
      ]
    }
  ]

}
