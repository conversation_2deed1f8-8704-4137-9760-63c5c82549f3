// 不同客户后台后缀不同，打包后配置此文件url
// const unityLogin = 'http://*************/login/#/login?ak=84711E46451'
const unityLogin = 'http://************/login/#/login?ak=133FAAFB6D6'
const PreviewFilePath = 'http://************:8012/onlinePreview?url='
const MinioFileHost = 'http://************:9000'
const API = {
  // domain: '*************',
  domain: '************',
  unityAPI: 'http://************/jdpc-sso',
  chatAPI: 'http://***********',
  baseAPI: 'http://************:19071/fuyang/data/center',
  instanceAPI: 'http://************/dev-dc-api-portal',
  ak:'133FAAFB6D6',
  sk:'6ag1cnje3g4'
}

window.globalConfig = {
  catalogueId: '682eeb20fab6df3304492b8f',
  unityLogin: {
    API:'http://************/api-portal/api/sso/getTicket',
    name: 'FYJcy',
    pass: 'aef5294f133bee82e7208b44b11888d7',
    AK:'5E59177673BBD0E',
    SK:'87ih9ghjl78'
  },
  //资源库
  resourceSearch: {
    ak: "A5C814A0AA7",
    sk: "bna5af73ga6",
    defaultCode: 'f5347f65f8cd11efad823284ea3491b3',
    type: [
      { name:'全部资源',code:'f5347f65f8cd11efad823284ea3491b3' },
      { name:'政务数据',code:'6bd835e7f99c11efad823284ea3491b3' },
      { name:'政法数据',code:'5f58f98bf99c11efad823284ea3491b3' },
      { name:'检务数据',code:'547e5e16f99c11efad823284ea3491b3' }

    ],
    url: "http://143.82.0.49:7000/search/#/index"
  },
  qualityEvaluation: {
    url: "http://************/du/szag-lcjk-api/openapi",
    dwbm: "330183"
  },
  //一张图
  earlyWarning: {
    ak: "1642D09463C",
    sk: "im0f6jhcai4",
    url: "http://************:19070/fuchune-station/#/earlyWarning"
  },
  //案件预警
  warningInfo: {
    ak: "1642D09463C",
    sk: "im0f6jhcai4",
    url: "http://************:19070/fuchune-station/#/warningInfo",
    statistcsApi:"http://************:19071/fuyang/data/center/rest/core/case/area/statistics",
    statistcsWarningInfoApi: 'http://************:19071/fuyang/data/center/rest/core/warningInfo/statistics',
    warningApi: 'http://************:19071/fuyang/data/center/rest/core/warningInfo'
  },
  //裁判文书
  JudicialDocument:{
    ak:'9B333E7E00A',
    sk:'10b59echgc0',
    url:'http://143.80.32.228/fcez/#/sign-in',
    suffix:"redirect=data-retrieval",
    statistcsApi: 'http://************:19071/fuyang/data/center/rest/core/other/ws/statistics'
  },
  //数据查询
  dataShow: {
    ak: "A5C814A0AA7",
    sk: "bna5af73ga6",
    url: "http://143.82.0.49:7000/search/#/index",
    statistcsApi: 'http://************:19071/fuyang/data/center/rest/core/other/dataShow/statistics'
  },
  //监督场景
  supervisoryScenario:{
    ak: "9B333E7E00A",
    sk: "10b59echgc0",
    url: "http://143.80.32.228/fcez/#/sign-in"
  },
  //统一量刑填报
  tylxglquery:{
    ak:"14A1E77C534",
    sk:"8dn4a4c4mjh",
    url:"http://************:8080/tylxk-ui/#/sso?redirect=/sentencing-library/input"
  },
  //统一量刑管理
  tylxglmanage:{
    ak:"14A1E77C534",
    sk:"8dn4a4c4mjh",
    url:"http://************:8080/tylxk-ui/#/sso?redirect=/sentencing-library/manage"
  },
  //实时计算
  xqjsquery:{
    ak:"1642D09463C",
    sk:"im0f6jhcai4",
    url:"http://************:19071/fuyang/data/center/rest/core/other/xqjsquery"
  },
  //判决预警
  xqjsmanage:{
    ak:"9B333E7E00A",
    sk:"10b59echgc0",
    url:"http://143.80.32.228/fcez/#/sign-in",
    suffix:"redirect=clue-flow-xqjs"
  },
  //剥夺政治权利审监抗
  bdzzqlsjk:{
    ak:"9B333E7E00A",
    sk:"10b59echgc0",
    url:"http://143.80.32.228/fcez/#/sign-in",
    suffix:"redirect=clue-flow-bdzzql"
  },
  //涉赌人员拒绝执行
  sdryjjzs:{
    ak:"9B333E7E00A",
    sk:"10b59echgc0",
    url:"http://143.80.32.228/fcez/#/sign-in",
    suffix:"redirect=clue-flow-dbsxxgqd"
  },
  //判决信息查询
  pjxxcx:{
    ak:"9B333E7E00A",
    sk:"10b59echgc0",
    url:"http://143.80.32.228/fcez/#/sign-in",
    suffix:"redirect=data-retrieval",
  },
  //信息推送
  xxts:{
    ak:"",
    sk:"",
    url:""
  },
  //驾驶舱
  jsc:{
    dataCenter: "http://************/fuyang/#/dataCenter",
    ajzlzb: "http://************:8080/ajzlzb",
    //护水系统
    hsxt: "http://143.82.72.9:8082",
    //监所智慧纠违系统
    jszhjwxt: "http://143.82.72.9:8081",
    index: "http://************/fuyang/#/"
  },
  tabeTest:{
    width: { size: 100 },
    borders: {
      top: { style: "single", size: 4, color: "000000" },
      bottom: { style: "single", size: 4, color: "000000" },
      left: { style: "single", size: 4, color: "000000" },
      right: { style: "single", size: 4, color: "000000" },
      insideHorizontal: { style: "single", size: 2, color: "000000" },
      insideVertical: { style: "single", size: 2, color: "000000" }
    }
  },
  indexMenus: [
    {
      name: "数据中心",
      // icon: require("@/views/modules/dashboard/img/icon/数据中心.png"),
      children: [
        { name: "数据检索", mode: "dataShow" },
        { name: "判决书库", mode: "JudicialDocument" },
        { name: "阅览室" ,url: "fileSystem"}
      ]
    },
    {
      name: "场景中心",
      // icon: require("@/views/modules/dashboard/img/icon/场景中心.png"),
      children: [
        { name: "案发区域预警", mode: "earlyWarning" },
        { name: "剥夺政治权力监督", mode: "bdzzqlsjk" },
        { name: "失信被执行人涉赌拒执监督", mode: "sdryjjzs" },
        { name: "护水系统", cockpit: "hsxt" },
        { name: "监所智慧纠违系统", cockpit: "jszhjwxt" }
      ]
    },
    {
      name: "绩效评估",
      // icon: require("@/views/modules/dashboard/img/icon/绩效评估.png"),
      url: "/durationStatistics"
    },
    {
      name: "办案辅助",
      // icon: require("@/views/modules/dashboard/img/icon/办案辅助.png"),
      children: [
        { name: "统一量刑填报", mode: "tylxglquery" },
        { name: "统一量刑管理", mode: "tylxglmanage" },
        // { name: "刑期计算工具", url: "calculationSentence" },
        { name: "信息推送", url: "sms" }
      ]
    },
    {
      name: "驾驶舱",
      // icon: require("@/views/modules/dashboard/img/icon/驾驶舱.png"),
      children: [
        { name: "首页驾驶舱", cockpit: "index" },
        { name: "数据中心驾驶舱", cockpit: "dataCenter" },
        { name: "案管平台", cockpit: "ajzlzb" }
      ]
    },
    {
      name: "常用工具",
      // icon: require("@/views/modules/dashboard/img/icon/驾驶舱.png"),
      children: [
        { name: "刑期计算工具",url: "calculationSentence" },
        { name: "文件上传", dialog: "dataCenter" }
        // { name: "下载文件", url: "fileSystem" }
      ]
    }
  ]

}
