

.pagination-wrapper{
  border: none !important;
  position: relative !important;
}
.el-dialog__footer{
  background: #edf2fa !important;
  border-radius: 0 0 5px 5px !important;
}
.el-dialog__body .header{
  border-radius: 5px 5px 0 0 !important;
}

.el-range-separator{
  font-size:16px !important;
  line-height: 28px !important;
  width: 40px !important;
}

.el-picker-panel {
  border-color: #a9c4df;
}
.popper__arrow {
  border-bottom-color: #a9c4df !important;
}

.el-radio__input.is-checked .el-radio__inner{
  background-color: #4084ef !important;
  border-color: #4084ef !important;
}
.el-radio__label{
  font-size: 16px;
  line-height: 22px;
  line-height: 22px;
}

// 主要按钮样式覆盖
.el-button--primary {
  background: #1B9CFF !important;
  border-color: #1B9CFF !important;

  &:hover, &:focus {
    background: rgba(27, 156, 255, 0.8) !important;
    border-color: rgba(27, 156, 255, 0.8) !important;
  }
}

// 禁用状态的主要按钮
.el-button--primary.is-disabled {
  background: #CAD0D7 !important;
  border-color: #CAD0D7 !important;
  color: #FFFFFF !important;

  &:hover, &:focus {
    background: #CAD0D7 !important;
    border-color: #CAD0D7 !important;
    color: #FFFFFF !important;
  }
}

// 鼠标移入/点击状态
.el-button--primary:not(.is-disabled) {
  &:hover, &:focus {
    opacity: 0.8;
  }

  &:active {
    opacity: 0.9;
  }
}

// 按钮内的文字颜色
.el-button--primary {
  color: #FFFFFF !important;

  &:hover, &:focus {
    color: #FFFFFF !important;
  }
}

// 按钮圆角和内边距调整
.el-button {
  border-radius: 4px !important;
  padding: 8px 16px !important;
}

//   分页样式覆盖
.el-pagination.is-background .el-pager li:not(.disabled).active{
  background: #4084F0 !important;

}
.el-pagination.is-background .el-pager li:not(.disabled):hover{
  color: #4084F0 !important;
  border: 1px solid #4084F0 !important;
  background-color: #EBF4FE !important;
}
.number, .el-pagination.is-background .btn-prev ,  .el-pagination.is-background .btn-next, .el-pagination.is-background .el-pager li{
  background-color: #fff !important;
  border: 1px solid #1B9CFF !important;
}
.el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .btn-next:disabled{
  background-color: #c9d0d8 !important;
  color:#fff !important;
  border: none !important;
}
.el-pagination.is-background .el-pager li {
  background-color: #fff ;
}

// 辅助文字
.auxiliaryText{
  color: #889bba;
  font-size: 14px;
  line-height: 20px;
}
// 弹出框样式
.el-dialog__wrapper{
  background: rgba(0,0,0,0.6) !important;
}
.el-dialog {
  border-radius: 5px !important;
}
.dialog-header{
  border-radius: 5px 5px 0 0 !important;
  background-color: #4084ef !important;
  color: #fff !important;
  padding: 0 15px;
  display: flex;
  font-weight: 400;
  font-size: 16px;
  align-items: center;
  justify-content: space-between;
  height: 40px;
}
.dialog-header .el-icon-close{
  cursor: pointer;
  color: #ffffff !important;
}
.dialog-header .el-icon-close:hover{
  color: #C4DCF4 !important;
}
.dialog-container{
  padding: 15px 15px ;
}
// 输入框
.el-input__inner{
  border: 1px solid #a9c4df !important;
  color: #4F5E7B !important;
  border-radius: 0px;
}
.el-input__inner:focus{
  border: 1px solid  #4084F0 !important;
}
.el-form-item__label{
  font-size: 16px;
  font-weight: 400;
}

.el-radio-button__inner{
  font-size: 14px;
}
// 其他
.title-value{
  font-size: 18px;
  line-height: 24px;
}
.detail-item-title{
  color:#4084ef;
}
.textarea-content{
  font-size: 16px;
  line-height: 22px;
  color:#4f5e7b;
}
.detail-label, .detail-value{
  font-size: 16px;
  line-height: 22px;
  color:#4f5e7b;
}
.textarea-content{
  background:none;
  border:none;
  min-height: none;
  padding: 0;
}
// 描述列表的样式
.is-bordered-label{
  width: 100px !important;
  text-align: right !important;
  background-color: #fff;
}
.el-descriptions-item__cell {
  font-size: 16px;
  line-height: 22px;
  color:#4f5e7b;
  width: 40%;
}
.el-descriptions-item__content p{
  background-color:  #F3F6FB;
  line-height: 24px;
  font-size: 16px;
  padding:0px;
}
.el-table span{
  font-size: 16px;
  line-height: 22px;
  //color:#4F5E7B;
}
.el-table .cell{
  font-size: 16px !important;
}
.el-table thead{
  color:#2D405E;
  font-weight: 500;
  font-size: 16px;
}
.el-table thead th{
  background-color: #fff;
}

.el-table button span{
  color:#4084ef !important;
}
.el-table__row{
  background-color: #fff !important;

}

.el-table__row--striped{
  background-color: #f3f7fb !important;

}

//  .el-table__row:hover , .el-table__row:hover td,
//  .el-table__row--striped:hover, .el-table__row--striped:hover td{
//   // background-color: #fffdec !important;
//   background: red !important;
// }
//  .hover-row:hover{
//   background: green !important;
// }

.el-table__body tr:hover  td {
  //background-color: rgba(255,255,255,.1) !important;
}
.el-table__cell{
  //background: transparent  !important;
  border-bottom: 1px solid #e2ebf3 !important;
}
.el-table__header-wrapper{
  border-top: 1px solid #e2ebf3 !important;
}

//  .is-bordered-label .td{
//   width: 100px !important;
//   text-align: right !important;
// }
