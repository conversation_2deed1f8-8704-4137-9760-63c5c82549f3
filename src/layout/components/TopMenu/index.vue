<template>
    <div class="" style="position: relative;">
        <div class="topMenu">
            <div class="topMenu-item" v-for="(item, i) in routerList" @mouseover="mouseover(item)" :id="'item' + i"
                :class="routerName === item.meta.title ? 'topMenu-item--active' : ''" @click="handleNavClick(item)">
                <div>
                    <i v-if="item.meta.icon" :class="item.meta.icon"></i>
                    <span>{{ item.meta.title }}</span>
                </div>
            </div>
        </div>
        <div class="extendMenu" :class="{ 'extendMenu-hover': hoverFlag }">
            <div style="background-color: #fff;min-height: 10vh;max-height: 50vh;overflow: auto;">
                <div class="extendMenu-item" v-for="(item, i) in currentRoute.children" :key="i"
                    @click="handleSubClick(item)">
                    {{ item.meta.title }}
                </div>
            </div>
            <!-- 触发收起效果 -->
            <div style="height:10vh;width:100%" @mouseover="mouseLeave" @mouseleave="mouseLeave" />
        </div>
    </div>
</template>
<script>
export default {
    props: {
        routerList: {
            type: Array,
            default: () => ([])
        },
        routerName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            currentRoute: [],
            hoverFlag: true
        }
    },
    mounted() {
    },
    methods: {
        mouseover(item) {
            this.hoverFlag = true
            if (item) {
                this.currentRoute = item
            }

        },
        mouseLeave() {
            setTimeout(() => {
                // this.hoverFlag = false
            }, 300)

        },
        handleNavClick(item) {
            if (!item.children || item.children.length === 0) {
                this.$router.push({ name: item.name })
            }
            if (!item.children || item.children.length > 0) {
                this.$router.push({ name: item.children[0].name })
                // this.hoverFlag = false
            }
            this.$emit('change', item)
        },
        handleSubClick(item) {
            this.$emit('change', this.currentRoute)
            this.$router.push({ name: item.name })
            // this.hoverFlag = false
        }
    }
}
</script>
<style lang="scss" scoped >
.topMenu {
    display: flex;
    white-space: nowrap;
    overflow-x: auto;
    width: 100%;

    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
        width: 0;
        height: 0;
    }

    /* 滚动槽 */
    &::-webkit-scrollbar-track {
        width: 0;
        height: 0;
    }

    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        width: 0;
        height: 0;
        background: rgba(0, 0, 0, 0);
    }

    .topMenu-item {
        padding: 0 20px;
        position: relative;
        cursor: pointer;
        transition: all .2s ease-in-out;
        font-size: 15px;
        color: #495060;
        border-bottom: 4px solid transparent;
        outline: none;
        list-style: none;
    }

    .topMenu-item.topMenu-item--active,
    .topMenu-item:hover {
        // color: #4080f0;
        // border-color: #4080f0;
        background: #f4f4f4;
        border-radius: 8px;
    }
}

.extendMenu {
    display: none;
    height: 0;
}

.extendMenu-hover {
    // transition: height 10s;
    position: absolute;
    display: block;
    z-index: 2;
    width: 100vw;
    top: 60px;
    height: calc(100vh - 60px);
    right: -273px;
    // 毛玻璃效果
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(20px);

    .extendMenu-item {
        text-align: center;
        font-weight: 700;

        &:hover {
            color: #409EFF;
        }
    }
}
</style>


