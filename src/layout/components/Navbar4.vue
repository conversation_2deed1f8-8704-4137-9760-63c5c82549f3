<!--
 * @Description:
-->
<template>
  <div>
    <div class="navbar" style="background-color: #fff;border-bottom: 2px solid #f4f4f4;">
      <div class="nav__logo" style="background-color: #fff;">
        <!-- <sidebar-logo :collapse="false"></sidebar-logo> -->
        <div class="nav__text flex" style="align-items: center;flex-direction: row;">
          <div style="padding:4px;margin:4px 12px;background: #fff;border-radius: 4px;" class="hamburger">
            <i style="font-size:20px;font-weight: 500;" class="el-icon-s-operation" @click="toggleSideBar"></i>
          </div>
          <div>
            <h4>系统风格3</h4>
            <p>Portal Management System</p>
          </div>
        </div>
      </div>
      <!-- 路由 -->
      <div style="overflow: auto;">
        <div class="topMenu" id="originPoint">
          <div class="topMenu-item" v-for="(item, i) in routerList" @mouseover="mouseover(item, 'item' + i)"
            :id="'item' + i" :class="routerName === item.meta.title ? 'topMenu-item--active' : ''"
            @click="handleNavClick(item)">
            <div>
              <i v-if="item.meta.icon" :class="item.meta.icon"></i>
              <span>{{ item.meta.title }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- <TopMenu :router-list="routerList" :router-name="routerName" @change="handleNavClick" /> -->
      <!--  右上角 -->
      <div class="navbar__right">
        <div class="navbar__user">
          <el-dropdown @command="handleCommand">
            <div class="navbar__avatar">
              <div class="navbar__avatar__img">
                <Avatar :size="34" :sex="sex" />
              </div>
              <div class="navbar__avatar__info">
                <p>
                  {{ realName }}
                  <i class="el-icon-arrow-down el-icon--right" />
                </p>
                <span>个人中心</span>
              </div>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <span style="display:block;" @click="toggle">切换风格</span>
              </el-dropdown-item>

              <el-dropdown-item>
                <span style="display:block;" @click="toUserIndex">个人设置</span>
              </el-dropdown-item>
              <el-dropdown-item command="logout">
                <span style="display:block;" @click="logout">注销</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <!--        <div class="msg-icon">-->
        <!--          <Poptip placement="bottom-end" width="410" transfer @on-popper-show="openInformation">-->
        <!--            <Badge class="mybadge" :count='msgTotal' dot-->
        <!--              style="cursor:pointer;width:20px;display:inline-block;margin:0 5px;">-->
        <!--              <Tooltip class="messageTooltip" :content="`共 ${msgTotal}条未读消息`" placement="left">-->
        <!--                <Icon @click="() => this.modal = false" style="font-weight: 600;" type="ios-notifications-outline"-->
        <!--                  size="20">-->
        <!--                </Icon>-->
        <!--              </Tooltip>-->
        <!--            </Badge>-->
        <!--            <Information slot="content" @count="count" :msg-total="msgTotal" ref="messageCenter"></Information>-->
        <!--          </Poptip>-->
        <!--          &lt;!&ndash; <img style="margin: 0 5px;" :src="logoutImg" alt="" width="24px" height="20px"> &ndash;&gt;-->
        <!--        </div>-->
      </div>
    </div>
    <!-- 扩展位置 -->
    <div class="extendMenu" :class="{ 'extendMenu-hover': hoverFlag }">
      <div style="background-color: #fff;min-height: 10vh;max-height: 50vh;overflow: auto;">
        <div class="extendMenu-item" v-for="(item, i) in  currentRoute.children " :key="i" @click="handleSubClick(item)"
          :style="{ 'margin-left': marginLeft + 'px' }">
          {{ item.meta.title }}
        </div>
      </div>
      <!-- 触发收起效果 -->
      <div style="height:10vh;width:100%" @mouseover="mouseLeave" @mouseleave="mouseLeave" />
    </div>
  </div>
</template>

<script>
import logoutImg from '@/assets/logout.png'
import { mapGetters } from "vuex"
import Hamburger from "@/components/Hamburger"
import Avatar from '@/layout/components-index-re/Avatar/index'
import store from '@/store'
import SidebarLogo from "@/layout/components/Sidebar/Logo";
import Information from "@/layout/components/Information/Information";
import { v1 as uuidv1 } from 'uuid'
import TopMenu from './TopMenu/index.vue'


export default {
  components: {
    SidebarLogo,
    Avatar,
    Information,
    Hamburger,
    TopMenu
    //  messageCenter
    // ErrorLog,
    // Screenfull,
    // Search,
    // Logo
  },
  props: {
    routerList: {
      type: Array,
      default: () => []
    },
    routerName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      logoutImg,
      msgTotal: 0,
      notifyWarning: '',
      notifyData: {},
      isSetting: false,
      arr: ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      sSize: this.$store.getters.sSize,
      sex: this.$store.getters.sex,
      realName: store.getters.realName,
      messageData: null,
      websock: null,
      userId: null,
      page: 1,
      rows: 10,
      currentRoute: [],
      hoverFlag: true,
      marginLeft: 0
    }
  },
  mounted() {
    // 获取消息列表
    // this.count()
    // if (!this.websock)
    //   this.initWebSocket()
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
  methods: {
    // 切换目录
    mouseover(item, idName) {
      this.hoverFlag = true
      if (item) {
        let scrollLeft = document.getElementById('originPoint').scrollLeft
        this.marginLeft = document.getElementById(idName).offsetLeft + 25 - scrollLeft
        this.currentRoute = item
      }

    },
    mouseLeave() {
      setTimeout(() => {
        // this.hoverFlag = false
      }, 300)

    },
    handleNavClick(item) {
      if (!item.children || item.children.length === 0) {
        this.$router.push({ name: item.name })
      }
      if (!item.children || item.children.length > 0) {
        this.$router.push({ name: item.children[0].name })
        // this.hoverFlag = false
      }
      this.$emit('change', item.meta.title)
    },
    handleSubClick(item) {
      this.$emit('change', this.currentRoute)
      this.$router.push({ name: item.name })
      // this.hoverFlag = false
    },



    randomWord(num) {
      let str = ""
      for (var i = 0; i < num; i++) {
        let pos = Math.round(Math.random() * (this.arr.length - 1))
        str += this.arr[pos]
      }
      return str
    },
    toUserIndex() {
      this.$router.push('/user/index')
    },
    toHelpIndex() {
      this.$router.push('/help')
    },
    handleCommand(command) {
      switch (command) {
        case "changing":
          this.$store.dispatch('user/resetS')
          location.reload()
          break
        case "setting":
          this.isSetting = true
          break
        case "logout":
          this.logout()
      }
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar")
    },

    toggle() {
      this.$store.commit("app/toggleStyle")
    },
    logout() {
      this.$store.dispatch("user/logout").then(() => {
        this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      })
      /* logout(this.$store.getters.token).then(res => {
        console.log(res)
        this.$store.dispatch("user/logout2").then(i => {
          console.log(i)
          this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        })
      }) */
    },
    openInformation() {
      // this.$refs.messageCenter.roomShow = false
    },
    count() {
      // this.$nextTick(() => {
      //   countUserMessage({
      //     userId: this.$store.state.user.id,
      //     meseageStatus: 2
      //   }).then(res => {
      //     this.msgTotal = res.data
      //   })
      // })
    },
    initWebSocket() {//初始化weosocket(必须)
      this.userId = this.$store.state.user.id
      if (typeof (WebSocket) == "undefined") {
        console.log("您的浏览器不支持WebSocket");
      } else if (this.$store.state.user.id) {
        console.log("您的浏览器支持WebSocket");
        console.log(uuidv1())
        let reg = new RegExp("-", "g")

        const wsuri = process.env.VUE_APP_WEBSOCK + uuidv1().replace(reg, '') + '/' + this.$store.state.user.id;    //请根据实际项目需要进行修改'
        console.log(wsuri)
        this.websock = new WebSocket(wsuri);      //新建一个websocket对象
        this.websock.onmessage = this.websocketOnMessage;
        this.websock.onopen = this.websocketOnOpen;
        this.websock.onerror = this.websocketOnError;
        this.websock.onclose = this.websocketClose;
      } else {
        console.log("没有用户信息");
      }
    },
    websocketOnOpen() {//websocket连接后发送数据(send发送)
      console.log("Socket 已打开");
      //  let actions = {"test":"12345"};     //请根据实际项目需要进行修改
      // this.websocketSend(JSON.stringify(actions));
      // 获取线索的统计信息并提示
      // getMessageBase().then(res => {
      //   if (res.data) {
      //     this.$notify.info({
      //       title: '消息',
      //       dangerouslyUseHTMLString: true,
      //       // message: "<strong>今日新增消息: </strong>" + res.data.todayNum  + '\n' +"<strong>案件总量: </strong>" + res.data.caseNum  + '\n'+ "<strong>线索总量: </strong>" + res.data.clueNum,
      //       message: "<div><div><span style='font-size: 16px;font-weight: 800;'><i class='el-icon-success' style='margin-right: 3px'></i>今日新增消息: </span><span style='font-size: 16px;font-weight: 700;margin-left: 10px;'>" + res.data.todayNum + "</span></div>" +
      //         "<div style='display: flex;margin-top: 10px'>" +
      //         "<div style='flex: 1'><span style='font-size: 14px;font-weight: 800;'><i class='el-icon-folder' style='margin-right: 3px'></i>案件总量: </span><span style='font-size: 14px;font-weight: 700;margin-left: 10px;'>" + res.data.caseNum + "</span></div>" +
      //         "<div style='flex: 1;'><span style='font-size: 14px;font-weight: 800;'><i class='el-icon-tickets' style='margin-right: 3px'></i>线索总量: </span><span style='font-size: 14px;font-weight: 700;margin-left: 10px;'>" + res.data.clueNum + "</span></div>" +
      //         "</div>" +
      //         "</div>"
      //     });
      //   }
      // });
    },

    websocketOnError() {//连接建立失败重连
      console.log("连接建立失败重连");
      // this.initWebSocket();
    },
    websocketOnMessage(e) { //数据接收
      // 接收到消息更新未读列表
      const a = JSON.parse(e.data);
      this.notifyData = a
      if (a) {
        this.notifyWarning = this.$notify.info({
          customClass: 'notifyWarningDia',
          type: 'warning',
          title: a.title,
          message: a.content,
          onClick: this.clickNotify,
          duration: 0
        });
      }
      this.$refs.messageCenter.refresh()
    },
    websocketSend(Data) {//数据发送
      this.websock.send(Data);
    },
    clickNotify() {
      //线索
      if (this.notifyData.messageType == 1) {
        this.$router.push('/clueSource/index')
      }
      //定时任务
      else if (this.notifyData.messageType == 2) {
        this.$router.push('/caseSource/index')
      }
      //督办
      else if (this.notifyData.messageType == 3) {
        this.$router.push('/clueSource/index')
      }
      //拒绝
      else if (this.notifyData.messageType == 4) {
        this.$router.push('/caseSource/index')
      }
      this.notifyWarning.close()
    },
    websocketClose(e) {  //关闭
      console.log('断开连接', e);
    },
  },

}
</script>
<style lang="scss" >
.el-notification {
  white-space: pre-wrap !important;
  background-color: rgb(255, 252, 246);

  .el-notification__content {
    width: 270px;
  }
}

.notifyWarningDia {
  cursor: pointer;
}
</style>
<style lang="scss" scoped>
.navbar {
  $height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: $height;
  line-height: 40px;
  // overflow: hidden;
  position: relative;
  background: #f4f4f4;
  // box-shadow: 1px 0 0 0 rgba(232, 243, 245, 1) inset;
  // margin-left: 1px;

  .navbar__link {
    display: flex;
    white-space: nowrap;
    overflow-x: auto;
    width: 100%;

    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }

    /* 滚动槽 */
    &::-webkit-scrollbar-track {
      width: 0;
      height: 0;
    }

    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      width: 0;
      height: 0;
      background: rgba(0, 0, 0, 0);
    }

    .navbar__link-item {
      padding: 0 20px;
      position: relative;
      cursor: pointer;
      transition: all .2s ease-in-out;
      font-size: 15px;
      color: #495060;
      border-bottom: 4px solid transparent;
      outline: none;
      list-style: none;
    }

    .navbar__link-item.navbar__link-item--active,
    .navbar__link-item:hover {
      color: #4080f0;
      border-color: #4080f0;
    }
  }

  .nav__logo {
    display: flex;
    line-height: 1;
    height: 100%;
    align-items: center;
    padding-left: 10px;
    flex-shrink: 0;
    background: #f4f4f4 top left / contain no-repeat;

    .nav__text {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      margin: 0 10px;
    }

    h4 {
      line-height: 1;
      font-size: 18px;
      margin: 0;
      font-weight: 600;
    }

    p {
      font-size: 10px;
      font-weight: 100;
      margin: 0;
      line-height: 1;
    }

    h4+p {
      margin-top: 4px;
    }
  }

  .navbar__right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .navbar__user {
    margin-right: 10px;

    .el-dropdown {
      display: block;
    }
  }

  .navbar__avatar {
    display: flex;
    align-items: center;
    line-height: 1;
    height: $height;
    padding: 0 10px;

    .navbar__avatar__info {
      p {
        margin: 0;
        padding: 0;
      }

      p,
      p>i {
        font-size: 14px;
        color: #2399f1;
      }

      span {
        font-size: 10px;
        color: #b5b5b5;
      }
    }

    .navbar__avatar__img {
      display: block;
      width: 36px;
      height: 36px;
      border-radius: 36px;
      margin-right: 10px;
      border: 1px solid #e1e6ee;
      background: transparent center/cover no-repeat;
    }

    .el-avatar {
      background: transparent;
      color: #2399f1;
    }
  }

  .navbar__help {
    cursor: pointer;
    width: $height;
    height: $height;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    span {
      display: block;
      font-size: 13px;
      line-height: 13px;
      color: #212145;
      border: 1px solid #212145;
      border-radius: 25px;
      padding: 3px 8px;
    }

    i {
      display: block;
      font-size: 16px;
      line-height: 16px;
      color: #212145;
    }

    // box-shadow: 1px 0 0 0 #eeeef1 inset;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .hamburger-container {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: $height;
    width: $height;
    height: $height;
    cursor: pointer;
    color: #333;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 80px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #333;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 2px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .msg-icon {
    margin-left: 1em;
    // width: 60px;
    text-align: center;
    display: flex;
    align-items: center;
    border-left: 1px solid #eee;
    line-height: 24px;
  }

  .mybadge:hover {

    //我的消息鼠标停滞
    i {
      color: rgb(179, 216, 247) !important;
    }
  }

  .messageTooltip {
    .ivu-tooltip-inner {
      white-space: nowrap !important;
    }
  }

}

.hamburger:hover {
  color: #409EFF;
}

.hamburger {
  cursor: pointer;
}



.topMenu {
  display: flex;
  white-space: nowrap;
  overflow-x: auto;
  width: 100%;

  /* 设置滚动条的样式 */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  /* 滚动槽 */
  &::-webkit-scrollbar-track {
    width: 0;
    height: 0;
  }

  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 0;
    height: 0;
    background: rgba(0, 0, 0, 0);
  }

  .topMenu-item {
    padding: 0 20px;
    position: relative;
    cursor: pointer;
    transition: all .2s ease-in-out;
    font-size: 15px;
    color: #495060;
    border-bottom: 4px solid transparent;
    outline: none;
    list-style: none;
  }

  .topMenu-item.topMenu-item--active,
  .topMenu-item:hover {
    // color: #4080f0;
    // border-color: #4080f0;
    background: #f4f4f4;
    border-radius: 8px;
  }
}

.extendMenu {
  display: none;
  height: 0;
}

.extendMenu-hover {
  // transition: height 10s;
  position: absolute;
  display: block;
  z-index: 2;
  width: 100vw;
  top: 60px;
  height: calc(100vh - 60px);
  // right: -273px;
  // 毛玻璃效果
  background: rgba(0, 0, 0, .5);
  backdrop-filter: blur(20px);

  .extendMenu-item {
    font-size: 14px;
    // text-align: center;
    font-weight: 500;
    line-height: 30px;

    &:hover {
      color: #409EFF;
    }
  }
}
</style>
