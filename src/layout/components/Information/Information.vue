<template>
  <div>
    <Button style="position:absolute;top:16px;right:10px;z-index:9" @click="allRead" size="small" type="primary">全部已读</Button>
    <Button style="position:absolute;top:16px;right:100px;z-index:9" @click="refresh" size="small" type="primary">刷新</Button>
    <Tabs v-model="name">
      <TabPane :label="msg" name="msg">
        <div>
          <messageRoom ref="messageRoom" @redaRoom="readRoom" :one-message-data="messageDataGroup" @closeCard="roomShow = false" :room-show="roomShow"> </messageRoom>
          <message-list :is-last-list="isLastList"
                        v-loading="loading"
                        @getScrollData="getScrollData"
                        style="position:absolute;top:0px;"
                        :message-data="messageData"
                        @toggle="toggleNowComponent" ></message-list>
        </div>
      </TabPane>
    </Tabs>

  </div>
</template>

<script>
  import messageList from '../messageCenter/messageList'
  import messageRoom from '../messageCenter/messageRoom'
  export default {
    name: 'information',
    components: {
      messageList,
      messageRoom
    },
    props: ['msgTotal'],
    data() {
      return {
        messageData: [],
        isLastList: false,
        roomShow: false,
        name: "msg",
        count: 0,//起始页数值为0
        loading: false,
        totalPages: "",//取后端返回内容的总页数
        list: [], //后端返回的数组
        index: 0,
        hasUnRead: false,
        todoShow: 0,
        excludeTodoStatus: [],
        cooperationStatus: [],
        todoMessageType: [11, 13],
        messageDataGroup: [],
        page: 1,
        rows: 10,
        msg: (h) => {
          let list = this.messageDataGroup || []
          let count = this.msgTotal
          return h('div', [
            h('span', `待办消息(${count || 0})`),
          ])
        },
        // async: (h) => {
        //   let list = this.syncOutData || []
        //   let count = list.length
        //   return h('div', [
        //     h('span', `预警消息(${count || 0})`),
        //     h('Badge', {
        //       props: {
        //         dot: true,
        //         count: list.filter(item => !item.ack).length
        //       },
        //       style: {
        //         marginTop: '-20px',
        //         marginLeft: '4px'
        //       }
        //     })
        //   ])
        // }
      }
    },
    watch: {
      // messageData(val) {
      //   if (val) {
      //     this.roomShow = false
      //   }
      // },
      // nowComponent(val) {
      //   recode[this.name] = val
      // }
    },
    mounted() {
      this.getMsgList()
      //   this.getExcludeTodoStatusByClueIds()
      // this.getCooperationStatusByClueIds()
    },
    methods: {
      async getMsgList(type) {
        // this.loading = true
        // let data = await pageGroupUserMessage({
        //   entity:{
        //     userId: this.$store.state.user.id,
        //     meseageStatus: 2
        //   },
        //   page: this.page,
        //   rows: this.rows
        // })
        // if (data.data.list.length < this.rows) {
        //   this.isLastList = true
        // }
        // if (type === 'scroll') {
        //   this.messageData = this.messageData.concat(data.data.list)
        // } else {
        //   this.messageData = data.data.list
        // }
        // this.$emit('count')
        // this.loading = false
      },
      deleteMessageData(targetName) {
        let list = this.messageData
        let filter = list.filter(res=>res.targetName !== targetName)
        this.messageData = filter
        this.$emit('count')
      },
      refresh() {
        this.roomShow = false
        this.$refs.messageRoom.clearMsg()
        this.page = 1
        this.rows = 10
        // this.messageData = []
        this.isLastList = false
        this.getMsgList()
      },
      getScrollData() {
        this.page = this.page + 1
        this.getMsgList('scroll')
      },
      readRoom(data) {
        // saveBatchReadFlag({
        //   targetName: data.targetName,
        //   userId: this.$store.state.user.id
        // }).then(res=>{
        //   this.deleteMessageData(data.targetName)
        // })
        // this.roomShow = false
      },
      isInIodoMessageType(messageType) {
        return ~this.todoMessageType.indexOf(messageType)
      },
      // getExcludeTodoStatusByClueIds() {
      //   let clueIdList = this.todoData.map(item => item[0].clueId)
      //   getExcludeTodoStatusByClueIds({ clueIdList }).then(res => {
      //     if (res.status) {
      //       this.excludeTodoStatus = res.data || []
      //     }
      //   })
      // },
      // getCooperationStatusByClueIds() {
      //   let clueIds = this.todoData.map(item => item[0].clueId)
      //   getCooperationStatusByClueIds({ clueIds, userId: +this.userId }).then(res => {
      //     if (res.status) {
      //       this.cooperationStatus = res.content || []
      //     }
      //   })
      // },
      allRead() {
        this.$confirm(`确定已读所有消息?`, '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // saveBatchReadFlag({
          //   userId: this.$store.state.user.id
          // }).then(res=>{
          //   this.refresh()
          // })
        }).catch(()=>{})

      },
      toggleNowComponent(component, index) {
        this.messageDataGroup = this.messageData[index]
        this.roomShow = true
      },
      getFirstMessage() {
        this.$emit('update')
      },
      messageConfirmById(id) {
        messageConfirmById({
          id
        }).then(res => { })
      },
      bind() {
        if (this.index === 0) {
          this.messageData = this.unreadMessageData
        } else if (this.index === 1) {
          this.messageData = this.readMessageData
        } else {
          this.messageData = this.allMessageData
        }
      },
      getOutData(dataStr) {
        console.log(this[dataStr])
        if (this[dataStr]) {
          const arr = this[dataStr].map((item, index) => {
            let count = 0
            let data = item[item.length - 1]
            item.forEach(element => {
              if (element.readFlag === "0") count++
            })
            data.index = index
            data.count = count
            return data
          })
          // arr.sort((a, b) => {
          //   if (a.readFlag === "0" + b.readFlag === "0" == 1) {
          //     if (a.readFlag !== "0") return 1
          //     else return -1
          //   } else {
          //     return +new Date(b.createTime) - +new Date(a.createTime)
          //   }
          // })
          return arr

        } else
          return []
      }
    },
    computed: {
      noMore() {
        //当起始页数大于总页数时停止加载
        return this.count >= this.totalPages - 1;
      },
      disabled() {
        return this.loading || this.noMore
      },
      //messageType==11请求排除类型 13发起协办
      //待办消息列表
      // todoData() {
      //   //切分二维数组
      //   const arrs = []
      //   this.messageData.forEach(item => {
      //     let myindex
      //     const flag = arrs.every((arr, index) => {
      //       if (arr[0].clueId === item.clueId) {
      //         myindex = index
      //         return false
      //       }
      //       return true
      //     })
      //     if (flag) {
      //       (this.isInIodoMessageType(item.messageType)) && arrs.push([item])
      //     } else {
      //       (this.isInIodoMessageType(item.messageType)) && arrs[myindex].push(item)
      //     }
      //   })
      //   return arrs
      // },
      //系统消息列表
      okData() {
        //切分二维数组
        const arrs = []
        this.messageData.forEach(item => {
            let myindex
          const flag = arrs.every((arr, index) => {
            if (arr[0].clueId === item.clueId) {
              myindex = index
              return false
            }
            return true
          })
          if (flag) {
            (!this.isInIodoMessageType(item.messageType))
            && (item.clueId)
            && (!~item.clueId.indexOf('同步'))
            && arrs.push([item])
          } else {
            (!this.isInIodoMessageType(item.messageType))
            && (item.clueId)
            && (!~item.clueId.indexOf('同步'))
            && arrs[myindex].push(item)
          }
        })
        return arrs
      },
      //系统同步列表
      syncData() {
        const arrs = []
        this.messageData.forEach(item => {
          let myindex
          const flag = arrs.every((arr, index) => {
            if (arr[0].id === item.id) {
              // if (arr[0].clueId === item.clueId) {
              myindex = index
              return false
            }
            return true
          })
          if (flag) {
            (item.messageType !==  "1")
            && arrs.push([item])
          } else {
            (item.messageType !==  "1")
            && arrs[myindex].push(item)
          }
        })
        return arrs
      },
      // todoOutData() {
      //   return this.getOutData('todoData')
      // },
      outData() {
        return this.getOutData('okData')
      },
      syncOutData() {
        return this.getOutData('syncData')
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-tabs-bar {
    min-height: 36px;
    margin-bottom: 5px !important;
  }
</style>
