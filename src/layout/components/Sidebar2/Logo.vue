<template>
  <!--<div class="sidebar-logo-container" :class="{'collapse':collapse}">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo">
        <h1 v-else class="sidebar-title">{{ title }} </h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo">
        <h1 class="sidebar-title">{{ title }} </h1>
      </router-link>
  </div>-->
  <div class="sidebar-logo-container">
    <transition name="sidebarLogoFade">
      <router-link key="collapse" class="sidebar-logo-link" to="/">
        <div :style="{'background-image': `url('${logo}')`}" class="sidebar-logo" />
      </router-link>
    </transition>
  </div>
</template>

<script>
  export default {
    name: 'SidebarLogo',
    props: {
      collapse: {
        type: Boolean,
        required: true
      }
    },
    data() {
      return {
        title: 'Vue Element Admin',
        // logo: 'https://keenthemes.com/metronic/themes/metronic/theme/default/demo6/dist/assets/media/logos/logo-6.png'
        logo: require("@/assets/logo.png")
      }
    }
  }
</script>

<style lang="scss" scoped>
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  h4 {
    margin: 0;
    line-height: 1;
  }

  .sidebar-logo-container {
    position: relative;
    margin: 0 auto;
    // background: #fbce44;
    background: transparent;
    text-align: center;
    overflow: hidden;
    // float: left;

    & .sidebar-logo-link {
      width: 100%;
      display: flex !important;
      align-items: center;
      justify-content: center;

      & .sidebar-logo {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        vertical-align: middle;
        background: transparent center / contain no-repeat;
        // margin-right: 12px;
      }

      & .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        line-height: 80px;
        font-size: 28px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
      }
    }

    &.collapse {
      .sidebar-logo {
        margin-right: 0px;
      }
    }
  }
</style>
