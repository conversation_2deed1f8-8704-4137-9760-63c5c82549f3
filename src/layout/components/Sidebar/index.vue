<!--
 * @Description:
-->
<template>
  <div>
    <!--    <div class="side__logo">
            <logo v-if="showLogo" :collapse="isCollapse" />
            <h4 v-if="!isCollapse">数智监督平台</h4>
          </div>-->
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :unique-opened="true" :collapse-transition="false"
        mode="vertical">
        <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
          @toggleClick="toggleSideBar" /> -->
        <sidebar-item v-for="route in routerList" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import SidebarItem from "./SidebarItem"
import variables from "@/styles/variables.scss"
import Logo from "./Logo"
import Hamburger from "@/components/Hamburger";

export default {
  components: { Hamburger, SidebarItem, Logo },
  props: {
    routerList: {
      type: Array,
      default: () => []
    },
    nextPath: {
      type: String,
      default: ''
    }
  },
  watch: {
    routerList(val) {
      this.$router.push(this.nextPath || { name: '404' })
    },
  },
  computed: {
    ...mapGetters(["permission_routes", "sidebar"]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      this.$emit("changeRoute", path)
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar")
    }
  }
}
</script>
<style lang="scss" scoped>
$height: 40px;

.hamburger-container {
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: $height;
  width: 100%;
  height: $height;
  cursor: pointer;
  color: #4080f0 !important;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;
}

// ::v-deep .scrollbar-wrapper {
//   margin-right: 10px;
// }


::v-deep .el-submenu__title:hover {
  background: #f4f4f4 !important;
}

::v-deep .el-submenu__title:focus {
  background: #f4f4f4 !important;

}
</style>
