<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const {icon, title} = context.props
    const vnodes = []

    if (icon && icon !== '#') {
      vnodes.push(<i class={`${icon}`}/>)
    } else {
      // vnodes.push(<i class={`iconfont`}/>)
    }
    if (title) {
      vnodes.push(<span slot='title'>{(title)}</span>)
    }
    return vnodes
  }
}
</script>
