<template>
  <Row :gutter="16" style="height:100%">
    <!-- <Col span="3" style="margin-bottom: 10px;" >
    </Col> -->
    <Col span="5">
    <newListMenu :sync="syncOutData.length" :all="messageData.length" :read="todoOutData.length" :unread="outData.length" @typeChange="typeChange" />
    </Col>
    <Col v-show="todoShow==0" span="19" style="height:93%">
    <component @cooperation-status="getCooperationStatusByClueIds" :todoShow="todoShow==0" @exclude-status="getExcludeTodoStatusByClueIds" :allExcludeTodoStatus="excludeTodoStatus" :index="index" :allCooperationStatus="cooperationStatus" @ack-change="ackChange" :oneMessageData="todoData[index]" @toggle="toggleNowComponent" :messageData="todoOutData" :is="nowComponent"></component>
    </Col>
    <Col v-show="todoShow==1" span="19" style="height:93%">
    <!-- <div style="text-align:right;margin-bottom: 10px;">
        <Button size="small" class="actionOpBtn" type="primary" @click="allRead" >全部标记已读</Button>
      </div> -->
    <component @ack-change="ackChange" :oneMessageData="okData[index]" @toggle="toggleNowComponent" :messageData="outData" :is="nowComponent"></component>
    </Col>
    <Col v-show="todoShow==2" span="19" style="height:93%">
    <!-- <div style="text-align:right;margin-bottom: 10px;">
        <Button size="small" class="actionOpBtn" type="primary" @click="allRead" >全部标记已读</Button>
      </div> -->
    <component @ack-change="ackChange" :oneMessageData="syncData[index]" @toggle="toggleNowComponent" :messageData="syncOutData" :is="nowComponent"></component>
    </Col>
  </Row>
</template>
<script>
  // import listMenu from './list-menu'
  import newListMenu from './new-list-menu'
  import messageList from './messageList'
  import messageRoom from './messageRoom'
  import {
   // ackClue,
    ackAllClue,
    getExcludeTodoStatusByClueIds,
    getCooperationStatusByClueIds
  } from '@/api/system/data'
  export default {
    name: 'messageCenter',
    props: ['messageData'],
    components: {
      newListMenu,
      messageList,
      messageRoom
    },
    data() {
      return {
        nowComponent: 'messageList',
        index: 0,
        hasUnRead: false,
        todoShow: 0,
        excludeTodoStatus: [],
        cooperationStatus: [],
        todoMessageType: [11, 13]
      }
    },
    methods: {
      isInIodoMessageType(messageType) {
        return ~this.todoMessageType.indexOf(messageType)
      },
      getExcludeTodoStatusByClueIds() {
        let clueIdList = this.todoData.map(item => item[0].clueId)
        getExcludeTodoStatusByClueIds({ clueIdList }).then(res => {
          if (res.status) {
            this.excludeTodoStatus = res.data || []
          }
        })
      },
      getCooperationStatusByClueIds() {
        let clueIds = this.todoData.map(item => item[0].clueId)
        getCooperationStatusByClueIds({ clueIds, userId: +this.userId }).then(res => {
          if (res.status) {
            this.cooperationStatus = res.content || []
          }
        })
      },
      allRead() {
        console.log("触发度1")
        ackAllClue({}).then(res => {
          this.$Message.info('全部消息标记已读成功')
          this.$emit('ack-change')
        })
      },
      ackChange() {
        console.log("触发度2")
        this.$emit('ack-change')
      },
      toggleNowComponent(component, index) {
        this.index = index
        this.nowComponent = component
      },
      getFirstMessage() {
        this.$emit('update')
      },
      messageConfirmById(id) {
        messageConfirmById({
          id
        }).then(res => { })
      },
      bind() {
        if (this.index === 0) {
          this.messageData = this.unreadMessageData
        } else if (this.index === 1) {
          this.messageData = this.readMessageData
        } else {
          this.messageData = this.allMessageData
        }
      },
      typeChange(index) {
        this.nowComponent = 'messageList' //切换tab 重置为list界面
        this.todoShow = index
      },
      getOutData(dataStr) {
        if (this[dataStr]) {
          const arr = this[dataStr].map((item, index) => {
            let count = 0
            let data = item[item.length - 1]
            item.forEach(element => {
              if (!element.ack) count++
            })
            data.index = index
            data.count = count
            return data
          })
          arr.sort((a, b) => {
            if (!!a.ack + !!b.ack == 1) {
              if (a.ack) return 1
              else return -1
            } else {
              return +new Date(b.messageDate) - +new Date(a.messageDate)
            }
          })
          return arr
        } else
          return []
      }
    },
    mounted() {
      this.getExcludeTodoStatusByClueIds()
      this.getCooperationStatusByClueIds()
    },
    computed: {
      userId() {
        return this.$store.getters.getUserId
      },
      //messageType==11请求排除类型 13发起协办
      //待办消息列表
      todoData() {
        //切分二维数组
        const arrs = []
        this.messageData.forEach(item => {
          let myindex
          const flag = arrs.every((arr, index) => {
            if (arr[0].clueId === item.clueId) {
              myindex = index
              return false
            }
            return true
          })
          if (flag) {
            (this.isInIodoMessageType(item.messageType)) && arrs.push([item])
          } else {
            (this.isInIodoMessageType(item.messageType)) && arrs[myindex].push(item)
          }
        })
        return arrs
      },
      //系统消息列表
      okData() {
        //切分二维数组
        const arrs = []
        this.messageData.forEach(item => {
          let myindex
          const flag = arrs.every((arr, index) => {
            if (arr[0].clueId === item.clueId) {
              myindex = index
              return false
            }
            return true
          })
          if (flag) {
            (!this.isInIodoMessageType(item.messageType))
              && (item.clueId)
              && (!~item.clueId.indexOf('同步'))
              && arrs.push([item])
          } else {
            (!this.isInIodoMessageType(item.messageType))
              && (item.clueId)
              && (!~item.clueId.indexOf('同步'))
              && arrs[myindex].push(item)
          }
        })
        return arrs
      },
      //系统同步列表
      syncData() {
        const arrs = []
        this.messageData.forEach(item => {
          let myindex
          const flag = arrs.every((arr, index) => {
            if (arr[0].clueId === item.clueId) {
              myindex = index
              return false
            }
            return true
          })
          if (flag) {
            (item.messageType != 11)
              && (item.clueId)
              && (!item.clueId.indexOf('同步'))
              && arrs.push([item])
          } else {
            (item.messageType != 11)
              && (item.clueId)
              && (!item.clueId.indexOf('同步'))
              && arrs[myindex].push(item)
          }
        })
        return arrs
      },
      todoOutData() {
        return this.getOutData('todoData')
      },
      outData() {
        return this.getOutData('okData')
      },
      syncOutData() {
        return this.getOutData('syncData')
      }
    },
    watch: {}
  }

</script>
<style>
</style>
