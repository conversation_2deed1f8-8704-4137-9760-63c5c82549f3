<template>
<ul class="messageUl">
  <li @click="()=>messageLiClick(0)" :class="messageLiClass[0]">
    <span>待办消息</span>
    <span class="counts">{{read}}</span>
  </li>
  <li @click="()=>messageLiClick(1)" :class="messageLiClass[1]">
    <span>系统消息</span>
    <span class="counts">{{unread}}</span>
  </li>
    <li @click="()=>messageLiClick(2)" :class="messageLiClass[2]">
    <span>系统同步</span>
    <span class="counts">{{sync}}</span>
  </li> 
</ul>
</template>
<script>
  export default {
    name: 'list-menu',
    props: {
      unread: {
        type: Number,
        default: 0
      },
      read: {
        type: Number,
        default: 0
      },
      sync: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        index: 0,
      }
    },
    computed: {
      messageLiClass() {
        return [{
          'messageLi': true,
          'messageLi-selected': this.index === 0,
          'messageLi-hover': this.index !== 0
        }, {
          'messageLi': true,
          'messageLi-selected': this.index === 1,
          'messageLi-hover': this.index !== 1
        }, {
          'messageLi': true,
          'messageLi-selected': this.index === 2,
          'messageLi-hover': this.index !== 2
        }]
      },
    },
    methods: {
      messageLiClick(index) {
        this.index = index
        this.$emit('typeChange', index)
      }
    }
  }
</script>
<style lang="less">
  .messageUl {
    .messageLi {
      // display:inline-block;
      width: 100%;
      height: 37px;
      border-radius: 3px;
      margin-bottom: 4px;
      font-size: 14px;
      // background: #fff;
      text-align: left;
      padding: 0 10px;
      cursor: pointer;
      line-height: 37px;
    }
    .messageLi-hover:hover {
      background: #eaecef;
    }
    .messageLi-selected {
      background: #0366d6;
      color: #fff;
    }
    span {
      vertical-align: middle;
      display: inline-block;
    }
    .counts {
      vertical-align: middle;
      display: inline-block;
      float: right;
    }
  }
</style>
