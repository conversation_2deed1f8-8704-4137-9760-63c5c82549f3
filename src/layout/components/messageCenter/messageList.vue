<template>
  <Row style="height:400px;border-radius:4px;width: 380px">
    <template v-if="messageData.length">
      <Scroll ref="scroll" :loading-text="tips" :on-reach-bottom="appendScrollData" :height="400" :distance-to-edge="10">
        <Col :key="index" @click.stop.native="()=>messageListColClick(index)" class="messageListCol"
             v-for="(item,index) in messageData" span="24" style="padding:6px">
<!--          <div class="todo" v-if="hasTodoTag(item)">-->
<!--            待办-->
<!--          </div>-->
          <div style="text-align:left;">
            <!-- 头像 -->
            <!-- <div style="text-align:center;width:10%;vertical-align:middle;display:inline-block">
              <Badge :count="item.ack?'':item.count">
                <MyAvatar icon="person" />
              </Badge>
            </div> -->
            <!-- 主要消息内容 -->
            <div style="width:100%;">
<!--              <Badge :dot="item.readFlag === '0'" style="width:100%">-->
                <!--              <p style="display:flex">-->
              <tooltip-over
                :content="item.messageTitle"
                class="navbar"
                refName="tooltipOver"
              ></tooltip-over>
                <span class="dateandmore" style="color:#999;float:right">
                  {{item.createTime}}
                </span>
<!--              </Badge>-->
              <p style="color:#999;font-size:15px;white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                {{item.messageContent || '无内容'}}
              </p>
            </div>
          </div>
        </Col>

        <p style="text-align: center;margin-top: 10px" v-if="isLastList">
          —————— &nbsp;&nbsp;  到底了 &nbsp;&nbsp;  ——————
        </p>
      </Scroll>
    </template>
    <div v-else style="text-align: center;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)">
      <Icon style="font-size:40px" type="ios-bell"></Icon>
      <p style="font-size:16px">消息列表为空</p>
    </div>
  </Row>
</template>
<script>
import TooltipOver from "@/layout/components/tooltip-over.vue"
import messageRoom from "@/layout/components/messageCenter/messageRoom.vue"

  export default {
    name: 'messageList',
    props: {
      isLastList:{
        type: Boolean,
        default: false
      },
      messageData: {
        type: Array,
        default:() => []
      },
      allExcludeTodoStatus: {
        default: () => []
      },
      allCooperationStatus: {
        default: () => []
      }
    },
    components: {
      TooltipOver
    },
    data() {
      return {
        modal: false,
        fromId: '',
        tips:"加载中",
        scrollMessageData:[],
      }
    },
    computed: {

    },
    mounted() {
    },
    filters: {
      clueIdFilter(val) {
        if (val && val.indexOf('同步') == 0) {
          return '系统同步'
        }
        return val || ''
      }
    },
    watch:{
      messageData:{
        handler(newVal){
          if(newVal && newVal.length > 0){
            this.scrollMessageData = this.messageData
          }
        },
        immediate: true
      }
    },
    methods: {
      hasTodoTag(item) {
        if (item.messageType == 11) {
          return this.allExcludeTodoStatus[item.index] == false
        } else if (item.messageType == 13) {
          return this.allCooperationStatus[item.index] == false
        }
      },
      messageListColClick(index) {
        this.$emit('toggle', 'messageRoom', index)
      },
      dropdownItemClick(fromId) {
        this.fromId = fromId
        this.modal = true
      },
      confirm() {
        this.$emit('deleteList', this.fromId)
        this.modal = false
      },
      cancel() {
        this.modal = false
      },
      appendScrollData(){
        // if (!this.isLastList) {
        //   this.scrollMessageData = this.scrollMessageData.concat(this.messageData)
        //   this.$emit('getScrollData')
        // }

        // this.scrollMessageData = this.scrollMessageData.concat(this.messageData)
        // this.$emit('getScrollData')

        if (!this.isLastList) {
          return new Promise(resolve => {
            resolve()
          }).then(res=>{
            setTimeout(()=>{
              this.$emit('getScrollData')
              this.scrollMessageData = this.scrollMessageData.concat(this.messageData)
            },700)
          })
        }else {
          this.tips= '没有更多数据'
          return false
        }

      }
    }
  }

</script>
<style lang="less">
  .messageListCol {
    cursor: pointer;
    border-radius: 3px;
    border-bottom: 1px solid rgb(238, 238, 238);

    .counts {
      display: inline-block;
      width: 15px;
      height: 15px;
      border: 1px solid transparent;
      border-radius: 50%;
      background-color: #ccc;
      color: #fff;
      line-height: 14px;
      font-size: 14px;
      text-align: center;
      vertical-align: baseline;
    }
  }

  .dateandmore {
    // cursor: default;

    i {
      cursor: pointer;
    }
  }

  div.todo {
    position: absolute;
    transform: rotate(-12deg);
    border: 2px solid rgba(255, 12, 22, 1);
    border-radius: 50%;
    line-height: 37px;
    height: 40px;
    width: 40px;
    color: rgba(255, 12, 22, 1);
    top: 1px;
    right: 2px;
    font-weight: 600;
    text-align: center;
  }

  .ivu-row {
    display: flow-root !important;
  }

  .messageListCol:hover {
    background: #fcfdff;
  }
</style>
