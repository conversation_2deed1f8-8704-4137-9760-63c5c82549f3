<template>
<!--  <Row  >-->
<!--    <Col span="24" style="height:100%">-->
      <div style="height:400px" id="messageRoom">
      <!--    <modalClueDetails :key="rowData&&rowData.clueId" :inMessage="true" :clueDetails.sync="clueDetails" :rowData.sync="rowData" :title="(isCriminal?`案件详情`:`线索详情`)">-->
      <!--    </modalClueDetails>-->
<!--      <Modal v-model="remarkVisible" title="驳回理由" @on-ok="handleRemarkClick">-->
<!--        <Input type="textarea" v-model="remark" placeholder='请输入理由' autofocus></Input>-->
<!--      </Modal>-->
      <!-- <Modal :width="90" title="案件详情" v-model="clueDetails" :styles="{top: '20px'}">
      <clueDetails @hidden="clueDetails = false" v-if="rowData && clueDetails" :rowData="rowData"></clueDetails>
    </Modal> -->
      <transition name="el-zoom-in-top">
      <Card class="messageCard" dis-hover v-show="roomShow" style="height:100%;z-index: 10000">
        <div slot="title" style="height: 24px; text-align: left; display: flex;">
<!--          <a v-if="clickTitle!='系统同步'" class="clueTitle" @click.stop="itemClickHandler(null)"> {{clickTitle}} </a>-->
          <span class="clueTitle">{{clickTitle}}</span>
          <Button @click.stop="read" type="info" shape="circle" size="small" style="font-weight:500;margin-right: 5px;">已读</Button>
          <Button @click.stop="closeCard" type="info" shape="circle" size="small" style="font-weight:500;">返回</Button>
          <!-- <span @click.stop="toggle" class="toggleback" style="font-weight:500;position:absolute;right:0">
            <Icon type="arrow-left-c"></Icon>
            返回
          </span> -->
        </div>
        <!-- <vue-scroll ref="vs" :ops="{scrollPanel: {
                            scrollingX: false
                            },bar: {
                        background: '#ddd',
                        keepShow: false
                      }}"> -->
<!--        <div v-scrollBar ref='scroll' style="width:100%;height:100%">-->
            <Timeline style="overflow:auto" v-if="msgList.length > 0" >
              <TimelineItem :key="index" v-for="(item,index) in msgList">
                <!--              <p style="text-align:left">{{item.createTime }}<span style="margin-left: 1em;">{{item.createTime}}</span></p>-->
                <p style="text-align:left">{{item.createTime }}</p>
                <div
                  style="white-space: pre-line;width:100%;word-wrap:break-word;padding:8px;border-radius:4px;display:inline-block;font-size:16px;text-align:left"
                  :style="{background: item.readFlag=== '0' ?'rgba(231, 238, 245, 0.9)':'rgb(255,254, 243)'}">
                  <span v-html="typeset(item.messageContent) || '无内容'"></span>

                </div>
              </TimelineItem>
            </Timeline>
            <div style="display: flex;align-items: center;justify-content: center;" v-else>
              <i class="el-icon-loading" style="color: #409eff"></i><span style="color: #409eff">加载中</span>
            </div>
            <!-- <div :key="index" style="padding-right:20px;margin-bottom:15px;text-align:left" v-for="(item,index) in oneMessageData">
            </div> -->
<!--        </div>-->
      </Card>
      </transition>
      </div>
<!--    </Col>-->
<!--  </Row>-->
</template>
<script>
  import {
    getDate
  } from '@/libs/tools'

  export default {
    name: 'messageRoom',
    props: {
      roomShow: {
        type: Boolean,
        default: false
      },
      oneMessageData: {
        default: () => []
      },
      todoShow: {},
      allExcludeTodoStatus: {
        default: () => []
      },
      allCooperationStatus: {
        default: () => []
      },
      index: {}
    },
    components: {
      //  clueDetails
    },
    filters: {
      clueIdFilter(val) {
        if (val.indexOf('同步') == 0) {
          return '系统同步'
        }
        return val || ''
      }
    },
    data() {
      return {
        //   clueDetails: false,
        rowData: null,
        syncCaseData: null,
        remarkVisible: false,
        remark: '',
        msgList: []
      }
    },
    computed: {
      userId() {
        return this.$store.getters.getUserId
      },
      hasUnread() {
        return this.oneMessageData.some(item => {
          return item.readFlag === "0"
        })
      },
      clickTitle() {
        if (this.oneMessageData) {
          return this.oneMessageData.messageTitle
        }
        // if (this.oneMessageData[0] && this.oneMessageData[0].clueId) {
        //   let title = this.oneMessageData[0].clueId.indexOf('同步') == 0 ?
        //     '系统同步' :
        //     this.oneMessageData[0].clueId
        //   return title
        // }
        return ''
      },
      area() {
        return this.$store.getters.getArea
      }
    },
    methods: {
      clearMsg() {
        this.msgList = []
      },
      closeCard() {
        this.$emit("closeCard")
      },
      onConfirmShow() {
        this.$nextTick(_ => {
          let messageConfirm = document.querySelectorAll('.messageConfirm')
          Array.from(messageConfirm).forEach(item => {
            item.addEventListener('click', event => {
              event.stopPropagation()
              return false
            })
          })
        })
      },
      handleRemarkClick() {
    //    this.confirmCooperationTask(false)
        this.remarkVisible = false
      },
      handlePoptipClick(item, bool) {
        if (item.messageType == 11) {
      //    this.confirmExcludeRequest(bool)
        } else if (item.messageType == 13) {
       //   this.confirmCooperationTask(bool)
        }
      },
      showBtn(item, index) {
        if (item.messageType == 11) {
          return !this.allExcludeTodoStatus[this.index] && index == this.oneMessageData.length - 1
        } else if (item.messageType == 13) {
          return !this.allCooperationStatus[this.index] && index == this.oneMessageData.length - 1
        }
      },
      // confirmExcludeRequest(bool) {
      //   confirmExcludeRequest({
      //     clueId: this.oneMessageData[0].clueId, confirm: bool
      //   }).then(res => {
      //     this.$emit('exclude-status')//按钮状态刷新
      //     this.$Message.info(res.msg)
      //   })
      // },
      // confirmCooperationTask(bool) {
      //   confirmCooperationTask({
      //     remark: this.remark,
      //     userId: +this.userId,
      //     clueId: this.oneMessageData[0].clueId,
      //     accept: bool
      //   }).then(res => {
      //     this.$emit('cooperation-status')//按钮状态刷新
      //     this.$Message.info(res.msg || res.message)
      //   })
      // },
      // getSyncCase(data) {
      //   getSyncCase(data).then(res => {
      //     if (res.status) {
      //       if (res.data && res.data.length) {
      //         this.syncCaseData = res.data
      //       }
      //     }
      //   })
      // },
      getTowDate() {
        var date = this.oneMessageData[0] && this.oneMessageData[0].messageDate
        if (date) {
          let start = date.split(' ')[0] + ' 00:00:00'
          var _date = new Date(date)
          _date.setDate(_date.getDate() + 1)
          let end = getDate(new Date(_date), 'year')
          end = end.split(' ')[0] + ' 00:00:00'
        }
      },
      typeset(val) {
        if (!val) return ''
        let res = val.replace(/[\n\r\s,，]/g, `<br />`)
        return res.split('<br />').filter(item => item.indexOf('线索:')).join(`<br />`)
      },
      // ackClue() {
      //   let req = {}
      //   let clueId = this.oneMessageData[0].clueId
      //   if (clueId) {
      //     req.clueId = clueId
      //   } else {
      //     req.id = this.oneMessageData[0].id
      //   }
      //   ackClue(req).then(res => {
      //     this.$emit('ack-change')
      //   })
      // },
      ackAllClue() {
        // ackClue({}).then(res => {
        //   this.$emit('ack-change')
        // })
      },
      itemClickHandler(clueId) {
        if (!clueId && this.clickTitle == '系统同步') {
          return
        }
        // getClueItem({
        //   clueId: clueId || this.oneMessageData[0].clueId
        // }).then(res => {
        //   if (!res.status) {
        //     this.$Message.destroy()
        //     return this.$Message.warning('找不到线索详情')
        //   }
        //   this.rowData = res.data.dataClue
        //   if (!this.rowData) {
        //     this.$Message.destroy()
        //     return this.$Message.warning('找不到线索详情')
        //   }
        //   //  this.clueDetails = true
        // })
      },
      toggle() {
        this.$emit('toggle', 'messageList')
      },
      read() {
        this.$emit('redaRoom',this.oneMessageData)
      },
      goto(e, caseCode) {
        if (e.target.id === 'mya') {
          getCaseInfo({
            caseCode
          }).then(res => {
            sessionStorage.setItem('index', 0)
            this.$store.commit('setCaseName', res.data.list[0].caseName)
            this.$store.commit('setRowData', res.data.list[0])
            this.$store.commit('setCaseCode', caseCode)
            this.$router.push({
              name: 'overview',
              params: {
                othersUse: false
              }
            })
            this.$Modal.remove()
          })
        }
      }
    },
    watch: {
      oneMessageData(newData, oldData) {
        // if (newData) {
        //   this.msgList = []
        //   listUserMessage({
        //     targetName: newData.targetName,
        //     userId: this.$store.state.user.id,
        //     meseageStatus: 2
        //   }).then(res=>{
        //     this.msgList = res.data
        //   })
        // }
      }
    },
    mounted() {
      // if (this.clickTitle == '系统同步') {
      //   let date = this.oneMessageData[0].messageDate.split(' ')[0]
      //   let area = this.area && this.area.substr(0, 2)
      //  this.getSyncCase({date, area})
      // }
      // if (this.hasUnread) {
      //   this.ackClue()
      // }

      // let scrollRef = this.$refs.scroll
      // scrollRef.ins.scroll({y: "100%"})
    },
    // beforeDestroy() {
    //
    // }
  }

</script>
<style lang="less">
  #messageRoom {
    .clueTitle {
      font-size: 13px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      line-height: 24px;
      flex: 1;
      text-overflow: ellipsis;
    }

    .ivu-card-body {
      height: calc(~'100% - 51px');
      overflow: auto;
    }

    .messageCard {
      .ivu-card-head {
        padding: 8px 16px;
        position: relative;
      }
    }
  }

  .sync-case-name {
    color: #2d8cf0;

    &:hover {
      color: #57a3f3;
    }
  }

  .toggleback {
    cursor: pointer;
    color: #000;

    &:hover {
      color: #999;
    }
  }

  .ivu-tabs-bar {
    min-height: 36px;
    margin-bottom: 5px !important;
  }
</style>
