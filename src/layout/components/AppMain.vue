<!--
 * @Description: 
-->
<!--
 * @Description: 
-->
<!--
 * @Description: 
-->
<template>
  <section class="app-main">
    <div class="dashboard-container" v-if="style === 2">
      <div class="page-div">
        <div class="page-content">
          <div class="card-item" style="height: calc(100vh - 60px);">
            <transition name="fade-transform" mode="out-in">
              <keep-alive :include="cachedViews">
                <router-view :key="key" />
              </keep-alive>
            </transition>
          </div>
        </div>
      </div>
    </div>
    <div class="page-container" style="padding:32px" v-else="style === 1">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <router-view :key="key" />
        </keep-alive>
      </transition>
    </div>
  </section>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "AppMain",
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
    ...mapState({
      style: state => state.app.style
    })
  }
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 80= navbar  80  */
  height: calc(100vh - 60px);
  // min-height: calc(100vh - 80px);
  width: 100%;
  position: relative;
  overflow: auto;
  // margin-left: 1px;
}

/* .fixed-header+.app-main {
  padding-top: 80px;
} */

.hasTagsView {
  .app-main {
    /* 114 = navbar + tags-view = 80 + 34 */
    min-height: calc(100vh - 114px);
  }

  .fixed-header + .app-main {
    padding-top: 114px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
@import "./style.scss";
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
<style>
.card-item > div {
  height: 100%;
}

.mian-content {
  height: calc(100% - 40px);
  background-color: #fff;
  position: relative;
  overflow: auto;
}
</style>
