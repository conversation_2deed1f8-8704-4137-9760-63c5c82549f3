<template>
  <el-row>
    <el-card class="box-card">
      <pan-thumb width="80px" height="80px" :image="avatar" class="index-container-avatar">
        {{ postName }}<br>{{ orgName }}
      </pan-thumb>
      <h3 class="kt-subheader-search__title">
        {{ new Date() | formatDate(false) }}好您叻，{{ realName }}
        <span class="index-container-ii-title">今天是 {{ new Date() | formatDate(true) }}</span>
      </h3>
      <div style="width: calc(100% - 120px);float: left;padding-top: 4px;">
        <el-tooltip class="item" effect="dark" content="指标说明" placement="top">
          <el-progress :percentage="percentage" :color="customColor" />
        </el-tooltip>
      </div>
      <div style="width: 100%;float: left;padding: 20px 20px 0px;">
        <span style="font-size: 16px;">
          <el-icon class="el-icon-date" style="color: #a9c4df;" />
          <el-dropdown>
            <span class="el-dropdown-link">
              最近三个月<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>最近一周 </el-dropdown-item>
              <el-dropdown-item>最近一个月</el-dropdown-item>
              <el-dropdown-item>最近三个月</el-dropdown-item>
              <el-dropdown-item disabled>最近半年</el-dropdown-item>
              <el-dropdown-item divided>最近一年</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </div>
      <div class="index-container-weather">
        <span>
          <span class="title">个人任务</span>
          <span><a class="danger"><countTo :start-val="0" :end-val="668" :duration="4000" /></a><a class="small">/2030</a></span>
          <span class="bnf"><el-tag type="danger" effect="dark" size="mini" class="bnf-tag"><el-icon class="el-icon-pie-chart" style="margin-right:5px;" />30%</el-tag></span>
        </span>
        <el-divider direction="vertical" class="index-container-divider" />
        <span>
          <span class="title">消息处理</span>
          <span><a class="warning"><countTo :start-val="0" :end-val="1026" :duration="4000" /></a><a class="small">/1080</a></span>
          <span class="bnf"><el-tag type="warning" effect="dark" size="mini" class="bnf-tag"><el-icon class="el-icon-pie-chart" style="margin-right:5px;" />62%</el-tag></span>
        </span>
        <el-divider direction="vertical" class="index-container-divider" />
        <span>
          <span class="title">登录次数</span>
          <span><a><countTo :start-val="startVal" :end-val="endVal" :duration="4000" /></a></span>
          <span class="title">次</span>
        </span>
      </div>
    </el-card>
  </el-row>
</template>

<script>
import PanThumb from '@/components/PanThumb'
import countTo from 'vue-count-to'
import store from '@/store'
var moment = require('moment')
moment.locale('zh-cn')

export default {
  components: {
    PanThumb,
    countTo
  },
  filters: {
    formatDate: function(time, bol) {
      if (time != null && time !== "") {
        var date = new Date(time)
        if (bol) {
          return moment(date).format('llll')
        } else {
          return moment(date).format('a')
        }
      } else {
        return ""
      }
    }
  },
  data() {
    return {
      realName: store.getters.realName,
      avatar: store.getters.avatar,
      orgName: store.getters.orgName,
      postName: store.getters.postName,
      percentage_index: 0,
      percentage: 10,
      customColor: '#f56c6c',
      customColors: [
        { color: '#f56c6c', percentage: 30 },
        { color: '#fbce44', percentage: 50 },
        { color: '#1989fa', percentage: 80 },
        { color: '#1DC5B3', percentage: 100 }
      ],
      startVal: 0,
      endVal: 200
    }
  },
  computed: {

  },
  created() {
    setInterval(this.m_percentage, 5000)
  },
  methods: {
    m_percentage() {
      if (this.percentage_index > 3) {
        this.percentage_index = 0
        this.startVal = 0
        this.endVal = 200
      }
      this.customColor = this.customColors[this.percentage_index].color
      this.percentage = this.customColors[this.percentage_index].percentage
      this.percentage_index++
      this.startVal = this.startVal + 200
      this.endVal = this.endVal + 200
    }
  }
}
</script>

<style lang="scss" scoped>

.index-container-ii-title{
    font-size: 12px;
    font-weight: 500;
    color: #9388fa;
    display: inline-block;
    padding-left: 0.5rem;
}

.index-container-weather{
  position: relative;
  font-size: 30px;
  color: #4f5e7b;
  float: left;
  width: 100%;
  padding: 12px 0 20px;
}

.index-container-weather span{
  float:left;
  margin: 0 18px;
}

.index-container-weather a span{
  margin: 0 !important;
}

.index-container-divider{
    float: left;
    background: #f4f4f4;
    margin-right: 20px;
    margin-top: 2px;
}

.index-container-weather .title{
  font-size: 16px;
  margin-right: 0;
  margin-left: 0;
  margin-top: 10px;
  color: #909399;
}

.index-container-weather .bnf{
  font-size: 16px;
  margin-right: 0;
  margin-left: -20px;
  margin-top: 7px;
}

.bnf-tag{
  border-radius: 50px;
}

.index-container-weather .small{
  font-size: 22px;
}

.index-container-weather .warning{
  color:#fbce44;
}

.index-container-weather .danger{
  color:#f86359;
}

.index-container-weather .success{
  color:#1dc5b3;
}

.index-container-avatar{
  float:left;
  margin:0 15px 0 0;
}

.kt-subheader-search__title{
  margin-top:25px;
  margin-bottom: 15px;
  padding-top:  20px;
}
</style>
