<template>
  <el-row>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="level-i-title">
          <i class="el-icon-date icon-margin" />
          工作安排</span>
      </div>
      <div class="text item" style="padding-top:5px;">
        <el-calendar v-model="value">
          <template slot="dateCell" slot-scope="{date, data}">
            <div :class="data.isSelected ? 'is-selected' : ''" class="calendar-div" @click="handleTasks(data.day)">
              <el-badge v-if="hasTaskDate.indexOf(data.day.split('-').join('')) >= 0" is-dot class="index-calendar-item" type="primary">
                {{ data.day.split('-').slice(2).join('-') }}
              </el-badge>
              <span v-else class="index-calendar-item">
                {{ data.day.split('-').slice(2).join('-') }}
              </span>
            </div>
          </template>
        </el-calendar>
        <div style="padding:0 20px;">
          <div class="index-calendar-title">
            <span>2019.10.8 共<span class="email-info"> 20 </span>个事项</span>
            <span><el-icon class="el-icon-plus" style="float:right;" /></span>
          </div>
          <div v-for="i in 5" :key="i" :class="i%2==0?'index-calendar-div':'index-calendar-div index-calendar-div-bg'">
            <div style="margin: 5px 0px;">
              <span><svg-icon icon-class="thunderbolt-fill" :class="i==1?'email-danger':i==2?'email-warning':i==3?'email-info':'email-open'" />
                {{ i==1?'10:30':i==2?'全天':i==3?'13:30 - 15:00':'下午' }}
              </span>
              <span style="float:right;font-size:14px;">个人事项</span>
            </div>
            <div class="title">
              <span class="index-calendar-tag">
                <el-tag size="mini" effect="plain" type="info">
                  数字门户
                </el-tag>
              </span>
              <span>今天要做些什么都要言简意赅的写清楚...</span>
              <span class="index-calendar-use"><a class="pan-btn transparent-btn"><i class="el-icon-delete icon-margin" style="font-size:16px;" /></a></span>
              <span class="index-calendar-use"><a class="pan-btn transparent-btn"><i class="el-icon-finished icon-margin" style="font-size:16px;" /></a></span>

            </div>
          </div>
        </div>
      </div>
    </el-card>
  </el-row>
</template>

<script>

export default {
  components: {
  },
  data() {
    return {
      value: new Date(),
      hasTaskDate: ["20191001", "20191028", "20191016"]
    }
  },
  computed: {

  },
  methods: {
    handleTasks(date) {
    }
  }
}
</script>

<style lang="scss" scoped>

.index-calendar-div{
  padding:10px 5px;
  border-bottom: 1px dashed #dde7f2;
  color:#85a0bb;
  font-size: 16px;
}

.index-calendar-div-bg{
  background: #fdfdfd;
}

.index-calendar-div:hover{
  background-color: #F5F7FA;
}

.index-calendar-title{
  color:#85a0bb;
  font-size: 16px;
  padding:0 5px 15px;
  border-bottom: 1px solid #f2f6fc;
}

.index-calendar-div .title{
  color: #333;
  margin-top: 12px;
}

.index-calendar-tag{
  padding: 0 10px 0 0;
  margin-top: -1px;
  float: left;
}

.index-calendar-item{
  margin-left: 10px;
  margin-top: -5px;
  float: left;
}

.index-calendar-use{
  float:right;
  margin:0 0 0 6px;
}

.calendar-div{
  width:100%;
  height:100%;
  padding:10px;
}
</style>
