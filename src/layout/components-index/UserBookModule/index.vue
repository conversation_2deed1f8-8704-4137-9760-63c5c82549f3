<template>
  <el-row>
    <el-card class="box-card index-container-card">
      <el-tabs class="index-tabs">
        <el-tab-pane>
          <span slot="label" class="level-i-title"><el-icon class="el-icon-mobile icon-margin" />快捷方式</span>
          <div style="height: 139px;padding:4px 30px 10px;">
            <div class="index-userbook-div">
              <span @click="login"><el-icon class="el-icon-switch-button" />登录</span>
            </div>
            <div class="index-userbook-div">
              <span @click="openWebIM"><el-icon class="el-icon-chat-dot-square" />消息</span>
            </div>
            <div v-for="i in 7" :key="i" class="index-userbook-div">
              <span>快捷方式</span>
            </div>
            <div class="index-userbook-div">
              <span style="border: none;"><el-icon class="el-icon-more" /></span>
            </div>
            <div class="index-common-userbook-div">
              <span style="margin-top: 12px; margin-left: 14px;color: #879bc5;"> <svg-icon icon-class="msg" class="index-userbook-msg-icon" />在线聊天</span>
              <el-badge :value="3" class="index-item-icon-badge">
                <el-tooltip class="item" effect="dark" content="王昭君" placement="top">
                  <el-avatar size="medium" src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" />
                </el-tooltip>
              </el-badge>
              <el-badge :value="8" class="index-item-icon-badge">
                <el-tooltip class="item" effect="dark" content="王昭君" placement="top">
                  <el-avatar size="medium" src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" />
                </el-tooltip>
              </el-badge>
              <el-badge :value="1" class="index-item-icon-badge">
                <el-tooltip class="item" effect="dark" content="王昭君" placement="top">
                  <el-avatar size="medium" src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" />
                </el-tooltip>
              </el-badge>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </el-row>
</template>

<script>

export default {
  components: { },
  data() {
    return {
      loginhost: 'http://************:8001/jdc-sso/',
      host: 'http://************:8001/jdc/',
      socket: 'http://************:8001/jdc/stompServer',
      loginName: 'admin',
      password: '123456'
    }
  },
  computed: {

  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>

.index-userbook-div{
  color: #85a0bb;
  cursor: pointer;
  margin-bottom: 15px;
  margin-right: 20px;
  margin-left: 5px;
  float: left;
}

.index-userbook-div:hover{
  color:#1b9cff;
}

// .index-userbook-div span:hover{
//   box-shadow: 0px 1px 4px #dde7f2;
// }

.index-userbook-div span{
  //border: 1px solid #e0e0e0;
  padding:0 5px;
  font-size: 14px;
  border-radius: 2px;
}

.index-userbook-div span i{
  margin-right: 5px;
}

.index-common-userbook-div{
  float: left;
  width: 100%;
  margin-top: 2px;
  background: #F5F7FA;
  padding: 2px;
  border-radius: 50px;
}

.index-common-userbook-div span{
  float: left;
  margin: 3px 8px;
}

.index-userbook-msg-icon{
  margin-right: 5px;
  font-size: 24px;
  margin-top: -4px;
  transform: rotateY(180deg);
}

</style>
