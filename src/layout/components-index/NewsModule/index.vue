<template>
  <el-row>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="level-i-title">
          <i class="el-icon-postcard icon-margin" />
          新闻通告</span>
        <div style="float: right; padding: 4px 0">
          <a v-for="(item, i) in tabs" :key="i" :class="{'active':activeIndex==i}" class="nav-link" @click="handleModuleChange(i, item.url)">
            {{ item.title }}
          </a>
        </div>
      </div>
      <div class="text item" style="padding-top:5px;">
        <el-table  v-loading="loading" :data="newsData" style="width: 100%" :show-header="false">
          <el-table-column label="标题">
            <template slot-scope="scope">
              <el-avatar v-if="scope.row.imgUrl!=''" class="index-table-span-avatar" shape="square" :size="60" fit="fill" :src="scope.row.imgUrl" />
              <el-avatar v-else class="index-table-span-avatar" shape="square" :size="60"> 无图 </el-avatar>
              <span class="index-table-span-title">
                <el-tag v-if="scope.row.top" size="mini" type="danger" effect="dark" class="icon-margin">置顶</el-tag>
                <a>{{ scope.row.title }}</a>
              </span>
              <span class="index-table-span-author"><svg-icon icon-class="user" class="icon-margin" style="color:#36a3f7;" />{{ scope.row.author }}<span style="margin:0 5px;">/</span>{{ scope.row.createDate | formatDate('MM月DD日 A') }}</span>
              <span style="float:right;margin-top: -25px;">
                <span class="index-table-span-author" style="border-right: 1px solid #f5f5f5;">
                  <div class="index-table-span-number">{{ scope.row.pv }}</div>
                  <div>浏览量</div>
                </span>
                <span class="index-table-span-author">
                  <div class="index-table-span-number">{{ scope.row.cv }}</div>
                  <div>收藏量</div>
                </span>
              </span>

            </template>
          </el-table-column>
        </el-table>
        <div class="index-pagination">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page.sync="listQuery.page"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </el-row>
</template>

<script>

var moment = require('moment')
moment.locale('zh-cn')

export default {
  components: { },
  filters: {
    formatDate: function(time, formatPattern) {
      if (time != null && time !== "") {
        var date = new Date(time)
        return moment(date).format(formatPattern || "YYYY-MM-DD HH:mm:ss")
      } else {
        return ""
      }
    }
  },
  data() {
    return {
      activeIndex: 0,
      loading: false,
      tabs: [
        { title: '新闻传播', url: '/news/list' },
        { title: '通知公告', url: '/notice/list' }
      ],
      listQuery: {
        page: 1,
        pageSize: 10
      },
      total: 0,
      newsData: []
    }
  },
  computed: {

  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
    },


  }
}
</script>

<style lang="scss" scoped>

.index-table-span-avatar{
  float:left;
}

.index-table-span-title{
  float:left;
  padding: 2px 10px 6px;
  width: calc(100% - 60px);
}

.index-table-span-author{
  padding: 6px 15px 0;
  font-size: 14px;
  float: left;
  color: #adadad;
}

.index-table-span-author div{
  text-align: right;
}

.index-table-span-number{
  font-size: 20px;
  color: #707e98;
  text-align: center !important;
}

</style>
