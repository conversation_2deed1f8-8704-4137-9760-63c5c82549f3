<template>
  <div :class="classObj" class="app-wrapper ">
    <template v-if="style === 1">
      <div class="header">
        <navbar :router-list="routerList" :router-name="routerName" @change="handleNavChange" />
        <tags-view v-if="needTagsView" />
      </div>
    </template>
    <template v-if="style === 2">
      <div class="header">
        <navbar2 :router-list="routerList" :router-name="routerName" @change="handleNavChange" />
        <tags-view v-if="needTagsView" />
      </div>
    </template>
    <template v-if="style === 3">
      <div class="header">
        <navbar3 :router-list="routerList" :router-name="routerName" />
        <tags-view v-if="needTagsView" />
      </div>
    </template>

    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <!-- <el-button style="position: absolute;z-index: 1000;right:10px;" @click="clickOpenSettings">aaaa{{ openSettings }}</el-button> -->
    <div :class="{ hasTagsView: needTagsView }" class="main-container"
      :style="{ 'background': style === 3 ? '#fff' : '#f4f4f4' }">
      <div class="sidebar-wrapper">
        <template v-if="style === 1">
          <sidebar class="sidebar-container" :router-list="routerList" :next-path="nextPath" @changeRoute="changeRoute" />
        </template>
        <template v-if="style === 2">
          <sidebar2 class="sidebar-container " :router-list="routerChildren" :next-path="nextPath"
            @changeRoute="changeRoute" />
        </template>
        <!-- <template v-if="style === 3">
          <sidebar2 class="sidebar-container " :router-list="routerChildren" :next-path="nextPath"
            @changeRoute="changeRoute" />
        </template> -->

      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings :click-open="openSettings" />
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView, Navbar2 } from './components'
import sidebar2 from './components/Sidebar2'
import Navbar3 from './components/Navbar3'
import ResizeMixin from './mixin/ResizeHandler'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    Navbar2,
    sidebar2,
    Navbar3
  },
  mixins: [ResizeMixin],
  data() {
    return {
      openSettings: false,
      routerName: '',
      routerChildren: [],
      nextPath: undefined,
    }
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader,
      style: state => state.app.style
    }),
    ...mapGetters(['permission_routes']),
    classObj() {
      return {
        style2: this.style === 2,
        style1: this.style === 1,
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    routerList() {
      return this.permission_routes.filter(i => {
        if (!!i.meta) return true
        else if (!!i.children && i.children.length === 1 && !!i.children[0].meta && !i.children[0].meta.noTab) return true
      }).map(i => {
        if (!i.meta && !!i.children && i.children.length === 1) return i.children[0]
        else return i
      })
    }
  },
  watch: {
    // $route() {
    //   this.routerName = this.handleRouterInit()
    // },
    routerName(val) {
      try {
        if (!val) {
          // 针对右上角进入个人设置时，侧边栏一片空白bug的特殊处理
          this.routerChildren = [{ "path": "/user/index", "name": "user", "meta": { "title": "个人设置", "icon": "el-icon-user", "breadcrumb": true }, "params": { "url": "views/system/user/index" }, "children": [] }]
        } else {
          this.routerChildren = this.routerList.find(i => i.meta.title === val).children || []
        }

      } catch (e) {
        this.routerChildren = []
      }
    }
  },
  created() {
    this.nextPath = this.dive(this.routerList).path
  },
  mounted() {
    this.routerName = this.handleRouterInit()
  },
  methods: {
    dive(res) {
      if (Array.isArray(res)) return this.dive(res[0])
      else if (res.children && res.children.length !== 0) return this.dive(res.children)
      else return res
    },
    handleRouterInit() {
      let name = this.$route.name
      function search(data) {
        if (data.name && data.name === name) return true
        else if (data.children && data.children.length > 0) {
          for (let i of data.children) {
            if (search(i)) return true
          }
        } else return false
      }

      for (let i of this.routerList) {
        if (search(i)) return i.meta.title
      }

      return ''
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    handleNavChange(title) {
      this.nextPath = this.dive(this.routerList.filter(r => r.meta.title === title)).path
      this.routerName = title
    },
    clickOpenSettings() {
      this.openSettings = true
    },
    changeRoute(path) {
      let activeRoute
      this.nextPath = path
      function find(res, path) {
        if (Array.isArray(res)) {
          for (let i = 0; i < res.length; i++) {
            find(res[i], path)
          }
        } else if (res.children && res.children.length !== 0) {
          find(res.children, path)
        } else {
          if (res.path === path) {
            activeRoute = res
          }
        }
      }
      let rName
      this.routerList.forEach(r => {
        activeRoute = undefined
        find(r, path)
        if (activeRoute) {
          rName = r.meta.title
        }
      })
      if (rName !== this.routerName) {
        this.routerName = rName
      }
      if (this.$route.name === 'ContentAdd' || this.$route.name === 'ContentEdit') {
        this.routerName = '发布管理'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .header {
    flex-shrink: 0;
  }

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 2001;
  //width: calc(100% - #{$sideBarWidth});
  width: 100%;
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  //width: calc(100% - 54px)
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
