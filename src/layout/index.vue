<template>
  <div style="height: 100vh;width: 100vw;display: flex;flex-direction: column;">
    <!--  -->
    <div class="system-header" v-if="isShow">
      <div class="header-content">
        <h1>"富春e站" 公益诉讼监督治理中心</h1>
      </div>
    </div>
    <app-main style="height: calc(100vh - 60px);overflow: auto;" />
  </div>
</template>

<script>
import RightPanel from "@/components/RightPanel";
import {
  AppMain,
  Navbar,
  Settings,
  Sidebar,
  TagsView,
  Navbar2
} from "./components";
import sidebar2 from "./components/Sidebar2";
import Navbar3 from "./components/Navbar3";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapGetters, mapState } from "vuex";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    Navbar2,
    sidebar2,
    Navbar3
  },
  mixins: [ResizeMixin],
  data() {
    return {
      openSettings: false,
      routerName: "",
      routerChildren: [],
      nextPath: undefined,
      isShow: true
    };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader,
      style: state => state.app.style
    }),
    ...mapGetters(["permission_routes"]),
    classObj() {
      return {
        style2: this.style === 2,
        style1: this.style === 1,
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile"
      };
    },
    routerList() {
      if (this.permission_routes.length > 1) {
        return this.permission_routes
            .filter(i => {
              if (!!i.meta) {
                return true
              } else if (
                  !!i.children &&
                  i.children.length === 1 &&
                  !!i.children[0].meta &&
                  !i.children[0].meta.noTab
              ) {
                return true
              }
            })
            .map(i => {
              if (!i.meta && !!i.children && i.children.length === 1) {
                return i.children[0]
              } else {
                return i;
              }
            });
      } else {
        return  []
      }
    }
  },
  watch: {
    // $route() {
    //   this.routerName = this.handleRouterInit()
    // },
    routerName(val) {
      try {
        this.routerChildren =
            this.routerList.find(i => i.meta.title === val).children || [];
      } catch (e) {
        this.routerChildren = [];
      }
    }
  },
  created() {
    this.nextPath = this.dive(this.routerList).path;
  },
  mounted() {
    this.routerName = this.handleRouterInit();
    let name = this.$route.name;
    this.isShow = name !== 'areaWarnning';
  },
  methods: {
    dive(res) {
      // if (Array.isArray(res)) return this.dive(res[0]);
      // else if (res.children && res.children.length !== 0)
      //   return this.dive(res.children);
      // else return res;
      return res;
    },
    handleRouterInit() {
      let name = this.$route.name;
      function search(data) {
        if (data.name && data.name === name) return true;
        else if (data.children && data.children.length > 0) {
          for (let i of data.children) {
            if (search(i)) return true;
          }
        } else return false;
      }

      for (let i of this.routerList) {
        if (search(i)) return i.meta.title;
      }

      return "";
    },
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    handleNavChange(title) {
      this.nextPath = this.dive(
        this.routerList.filter(r => r.meta.title === title)
      ).path;
      this.routerName = title;
    },
    clickOpenSettings() {
      this.openSettings = true;
    },
    changeRoute(path) {
      let activeRoute;
      this.nextPath = path;
      function find(res, path) {
        if (Array.isArray(res)) {
          for (let i = 0; i < res.length; i++) {
            find(res[i], path);
          }
        } else if (res.children && res.children.length !== 0) {
          find(res.children, path);
        } else {
          if (res.path === path) {
            activeRoute = res;
          }
        }
      }
      let rName;
      this.routerList.forEach(r => {
        activeRoute = undefined;
        find(r, path);
        if (activeRoute) {
          rName = r.meta.title;
        }
      });
      if (rName !== this.routerName) {
        this.routerName = rName;
      }
      if (
        this.$route.name === "ContentAdd" ||
        this.$route.name === "ContentEdit"
      ) {
        this.routerName = "发布管理";
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .header {
    flex-shrink: 0;
  }

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 2001;
  //width: calc(100% - #{$sideBarWidth});
  width: 100%;
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  //width: calc(100% - 54px)
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
.system-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
}

.system-header {
  background-color: #1e4c7a;
  width: 100%;
  height: 60px;

  z-index: 1000;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.system-header h1 {
  margin: 0;
  color: white;
  font-size: 24px;
  font-weight: bold;
}
</style>
