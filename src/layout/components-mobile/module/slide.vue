<template>
  <div class="mo-slide">
    <div v-for="(item, index) in slides" :key="index" class="slide-item">
      <div class="slide-item__round" />
      <div class="slide-item__name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Slide",
  data() {
    return {
      slides: [
        {
          img: "",
          name: "AAAA"
        },
        {
          img: "",
          name: "BBBB"
        },
        {
          img: "",
          name: "CCCC"
        },
        {
          img: "",
          name: "DDDD"
        },
        {
          img: "",
          name: "EEEE"
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.mo-slide {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  .slide-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
  .slide-item__round {
    width: 48px;
    height: 48px;
    border-radius: 48px;
    margin-bottom: 10px;
    background: linear-gradient(to top, #C1B181, #EFCB7B);
  }
  .slide-item__name {
    font-size: 12px;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    line-height: 17px;
  }
}
</style>
