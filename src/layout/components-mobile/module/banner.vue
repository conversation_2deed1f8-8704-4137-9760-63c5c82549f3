<template>
  <div class="mo-banner">
    <el-carousel height="272px" indicator-position="none" arrow="never">
      <el-carousel-item v-for="item in pics" :key="item">
        <div class="mo-banner__pic" :style="{'background-image': `url('${item}')`}" />
      </el-carousel-item>
    </el-carousel>
    <div class="mo-banner__intro">
      <div class="head-card">
        <div class="head-card__head">
          <div class="head-card__name"><span>{{ site.name }}</span></div>
          <div class="head-card__like golden-color" />
        </div>
        <div class="head-card__content">
          <!--<p :class="{'collapsed-content': collapseContent, 'over2line': collapseContent}" @click="collapseContent = !collapseContent">-->
          <p @click="collapseContent = !collapseContent">
            <span v-if="collapseContent">{{ site.introduction.substr(0, 50) + '...' }}</span>
            <span v-else>{{ site.introduction }}</span>
            <span v-if="collapseContent" class="collapse-btn">详情</span>
            <span v-else class="collapse-btn">&nbsp;&nbsp;收起</span>
          </p>
        </div>
        <div class="head-card__foot">
          <div class="head-card__address">
            <p>
              <span class="golden-color" />
              <span>地址：</span>{{ site.address }}
            </p>
          </div>
          <div class="head-card__leadway">导航</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Banner",
  data() {
    return {
      pics: [
        "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1575959790173&di=a04acf4c57b586108fc1fc5b2efb3ac4&imgtype=0&src=http%3A%2F%2Fimg1.qunarzz.com%2Ftravel%2Fd7%2F1609%2F1a%2F17f74d7275e6a6b5.jpg_r_720x480x95_0dd0c82a.jpg",
        "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1575959790171&di=e7249b2786c9e75260ebc6308d44a210&imgtype=0&src=http%3A%2F%2F9094316.s21i-9.faiusr.com%2F2%2FABUIABACGAAg99mKvQUogKKsrwMwsAk4rQY.jpg"
      ],
      collapseContent: false,
      site: {
        name: "井宿四",
        introduction: "xxxxxxxxxxxxxxxx",
        address: "xxxxxxxxxxxx"
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mo-banner__pic {
  height: 272px;
  background: transparent center / cover no-repeat;
}

.head-card {
  position: relative;
  z-index: 2;
  display: block;
  margin: -71px 12px 0;
  padding: 16px;
  /*min-height: 150px;*/
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 4px 14px 0 rgba(221, 221, 221, 0.5);
  -moz-box-shadow: 0 4px 14px 0 rgba(221, 221, 221, 0.5);
  box-shadow: 0 4px 14px 0 rgba(221, 221, 221, 0.5);
  color: #333;
  .head-card__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .head-card__name {
    font-size: 22px;
    font-weight: normal;
  }
  .head-card__like {
    display: flex;
    align-items: center;
    font-size: 20px;
  }
  .head-card__content {
    font-size: 13px;
    margin: 6px 0 10px;
    p span.collapse-btn {
      color: #AA8970;
      > span {
        color: #333;
      }
    }
    .collapsed-content {
      position: relative;
      overflow: hidden;
      /*height: 40px;*/
      > span {
        position: absolute;
        /*padding-left: 4px;*/
        z-index: 2;
        right: 5px;
        bottom: 2px;
        font-size: 13px;
        background: #fff;
      }
    }
  }
  .head-card__foot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: lighter;
    font-size: 13px;
    p > span {
      font-weight: normal;
    }
    .head-card__leadway {
      font-weight: normal;
      background: #f2f2f2ff;
      border-radius: 10.5px;
      padding: 2px 8px;
      white-space: nowrap;
    }
  }
}
</style>
