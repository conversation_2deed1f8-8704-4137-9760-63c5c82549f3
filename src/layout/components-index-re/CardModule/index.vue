<template>
  <div class="index-card">
    <div ref="cardHeader" class="index-card__header">
      <p class="index-card__title">{{ label }}</p>
      <div v-if="button !== ''" class="index-card__btn" @click="clickMe">{{ button }}</div>
    </div>
    <div class="index-card__body" :class="{'index-card__body--paint': plain, 'index-card__body--nopadding': nopadding}" :style="{height: theight}">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "Card",
  props: {
    button: {
      type: String,
      default: ""
    },
    height: {
      type: [Number, String],
      default: "auto"
    },
    label: {
      type: String,
      default: ""
    },
    nopadding: {
      type: Boolean,
      default: false
    },
    plain: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      theight: "auto"
    }
  },
  watch: {
    height() {
      this.setHeight()
    }
  },
  mounted() {
    this.setHeight()
  },
  methods: {
    clickMe() {
      this.$emit("click")
    },
    setHeight() {
      if (!isNaN(this.height)) {
        this.theight = `${this.height - this.$refs.cardHeader.offsetHeight - 1}px`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.index-card {
  background: transparent;
  .index-card__header,
  .index-card__body {
    position: relative;
    padding: 16px 20px;
    background: #fff;
  }
  .index-card__header + .index-card__body {
    margin-top: 2px;
  }
  .index-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none;
    cursor: default;
    border-radius: 8px 8px 0 0;
    .index-card__btn {
      cursor: pointer;
      font-size: 0.8rem;
      font-weight: 400;
      color: rgba(0, 174, 239, 1);
      line-height: 20px;
    }
  }
  .index-card__body {
    overflow: auto;
    border-radius: 0 0 8px 8px;
    &.index-card__body--paint {
      overflow: hidden;
      background: transparent;
      padding: 0;
    }
    &.index-card__body--nopadding {
      padding: 0;
    }
  }
  .index-card__title {
    font-size: 0.95rem;
    font-weight: 600;
    color: rgba(57, 60, 62, 1);
    line-height: 20px;
    margin: 0;
  }
}
</style>
