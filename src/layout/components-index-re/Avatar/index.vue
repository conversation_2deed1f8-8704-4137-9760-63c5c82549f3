<!--
 * @Description: 
-->
<template>
  <el-avatar :size="size" :src="getSrc" />
</template>

<script>
import src_male from './male.png'
import src_female from './female.png'

export default {
  name: "Avatar",
  props: {
    size: {
      type: Number,
      default: 30
    },
    sex: {
      type: String,
      default: "男"
    },
    src: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // src_male: "https://iccp.heyuetech.cn/static-portal/images/if_male.png",
      // src_female: "https://iccp.heyuetech.cn/static-portal/images/if_female.png"
      src_male,
      src_female
    }
  },
  computed: {
    getSrc() {
      if (this.src && this.src !== "") {
        return this.src
      } else if (this.sex && this.sex === "男") {
        return this.src_male
      } else {
        return this.src_female
      }
    }
  }
}
</script>

<style scoped></style>
