<template>
  <div class="index-count">
    <div
      v-for="(i, index) in counts"
      :key="index"
      v-loading="i.load"
      class="index-count__one"
      :style="{width: `calc(${100 / counts.length}% - ${(counts.length - 1) * 20 / counts.length }px)`}"
    >
      <div class="index-count__title">{{ i.name }}</div>
      <div class="index-count__num">
        <!--<span>{{ toThousands(i.count) }}</span>-->
        <span v-if="i.count.constructor === Number"><countTo :start-val="0" :end-val="i.count" :duration="4000" /></span>
        <span v-if="i.count.length === 2"><countTo :start-val="0" :end-val="i.count[0]" :duration="4000" />/<countTo :start-val="0" :end-val="i.count[1]" :duration="4000" /></span>
        <span>{{ i.count_word }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'

export default {
  name: "Count",
  components: { countTo },
  data() {
    return {
      counts: [
        {
          name: "XX",
          count: 10,
          load: false,
          count_word: "X",
          increase_num: 13
        },
        {
          name: "XX",
          count: 11,
          load: false,
          count_word: "X",
          increase_num: 13
        },
        {
          name: "XXXXX",
          count: [20, 30],
          load: false,
          count_word: "X/X",
          increase_num: -13
        },
        {
          name: "XXXX",
          count: [100, 200],
          load: false,
          count_word: "X/X",
          increase_num: -4
        }
      ]
    }
  },
  computed: {
    toThousands() {
      return (num) => {
        return (num || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      }
    }
  },
  created() {
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.index-count {
  display: flex;
  justify-content: space-between;
}

.index-count__one {
  padding: 16px 20px;
  background: #fff;
  border-radius: 8px;
  p {
    margin: 0;
  }
  .index-count__title {
    font-size: 0.95rem;
    font-weight: 500;
    color: rgba(104, 118, 140, 1);
    line-height: 20px;
  }
  .index-count__num {
    margin-top: 5px;
    > span:first-child {
      font-size: 1.5rem;
      font-weight: bold;
      color: rgba(57, 60, 62, 1);
      line-height: 31px;
    }
    > span:last-child {
      font-size: 0.9rem;
      color: rgba(145, 145, 175, 1);
      line-height: 17px;
    }
  }
  .index-count__increase {
    margin-top: 10px;
    font-size: 0.75rem;
    font-weight: 500;
    color: rgba(214, 91, 74, 1);
    line-height: 14px;
    > [green] {
      color: rgba(46, 185, 143, 1);
    }
  }
}
</style>
