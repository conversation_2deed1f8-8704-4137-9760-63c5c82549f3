import { constantRoutes } from '@/router'
import Layout from '@/layout'
import main from '@/App'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }) {
    return new Promise(resolve => {
      // let accessedRoutes
      // if (roles.includes('admin')) {
      //   accessedRoutes = asyncRoutes || []
      // } else {
      //   accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      // }
      assembleRotes().then((accessedRoutes) => {
        commit('SET_ROUTES', accessedRoutes)
        resolve(accessedRoutes)
      })
    })
  },
  generateRoutesData({ commit },data) {
    return new Promise(resolve => {
      assembleRotesByData(data).then((accessedRoutes) => {
        commit('SET_ROUTES', accessedRoutes)
        resolve(accessedRoutes)
      })
    })
  }
}

export async function assembleRotes() {
  // const { data } = await fetchTree({})
  // return toTreeData(data)
}
export async function assembleRotesByData(data) {
  return toTreeData(data)
}

function toTreeData(data) {
  var pos = {}
  var tree = []
  var i = 0
  while (data.length !== 0) {
    let component = ""
    if (data[i].path !== null && data[i].path !== "") {

      component = data[i].route
    }
    if (data[i].parentKey === null || data[i].parentKey === "" || data[i].parentKey === 0) {
      var p_obj = {
        key: data[i].key,
        parentKey: data[i].parentKey,
        path: "/" + (data[i].path ? data[i].path : data[i].key),
        component: Layout,
        meta: { title: data[i].title, icon: data[i].icon, breadcrumb: false, key: data[i].key },
        children: []
      }
      var result = data.some(item => {
        if (item.parentKey === data[i].key) {
          return true
        }
      })
      if (!result) {
        let o = {
          key: data[i].key,
          parentKey: data[i].parentKey,
          path: data[i].path,
          component: resolve => require(['@/' + component + '.vue'], resolve),
          // component: () => import('@/views/test/index'),
          name: data[i].path,
          meta: { title: data[i].title, icon: data[i].icon, breadcrumb: !result, key: data[i].key },
          params: { url: data[i].route }
        }
        p_obj.children.push(o)
      }
      tree.push(p_obj)
      pos[data[i].key] = [tree.length - 1]
      data.splice(i, 1)
      i--
    } else {
      var posArr = pos[data[i].parentKey]
      if (posArr !== undefined) {
        var obj = tree[posArr[0]]
        for (var j = 1; j < posArr.length; j++) {
          obj = obj.children[posArr[j]]
        }
        var result2 = data.some(item => {
          if (item.parentKey === data[i].key) {
            return true
          }
        })
        var o = {
          key: data[i].key,
          parentKey: data[i].parentKey,
          path: data[i].path.startsWith("/") ? data[i].path : "/" + data[i].path,
          // component: () => import('@/views/test/index'),
          name: data[i].path,
          meta: { title: data[i].title, icon: data[i].icon, breadcrumb: !result2, key: data[i].key },
          params: { url: data[i].route },
          children: []
        }

        if (data[i].menuType === 'M' || data[i].path === '') {
          o.component = main
        } else {

          o.component = resolve => require(['@/' + component + '.vue'], resolve)
        }
        obj.children.push(o);
        pos[data[i].key] = posArr.concat([obj.children.length - 1])
        data.splice(i, 1)
        i--
      }
    }
    i++

    if (i > data.length - 1) {
      i = 0
    }
  }
  tree.push({ path: '*', redirect: '/404', hidden: true })
  return tree
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
