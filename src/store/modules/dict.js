import { getAllDict } from '@/api/system/dictType'
import { listAll } from '@/api/system/dict'

const state = {
  dictTypes: [],
  dicts: []
}

const mutations = {
  SET_DICT_TYPES: (state, dictTypes) => {
    state.dictTypes = dictTypes
  },
  SET_DICTS: (state, dicts) => {
    state.dicts = dicts
  }
}

const actions = {
  getAll({ commit }) {
    return new Promise((resolve, reject) => {
      getAllDict().then(res => {
        commit('SET_DICT_TYPES', res.data)
        resolve()
      }).catch(error => {
        reject(error)
      })
      listAll().then(res => {
        commit('SET_DICTS', res.data)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  refresh({ commit }, dictTypes, dicts) {
    commit('SET_DICT_TYPES', dictTypes)
    commit('SET_DICTS', dicts)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
