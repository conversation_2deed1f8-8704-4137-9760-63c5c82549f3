/*
 * @Description: 
 */
/*
 * @Description: 
 */
/*
 * @Description: 
 */
/*
 * @Description: 
 */
/*
 * @Description: app.js
 */
import Cookies from 'js-cookie'


const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: Cookies.get('size') || 'medium',
  fieldList: [],
  mappingTableField: null,
  style: 2,   //1-风格1,2-风格2,3-风格3
  version: 'officialVersion',//'简单版本-simpleVersion'/'特殊版本-specialVersion'/'正式版本-officialVersion'/'测试版本-testVersion'
}

const mutations = {
  toggleStyle: state => {
    if (state.style === 1) {
      state.style = 2
    } else {
      state.style = 1
    }
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  setFieldList(state, fieldList) {
    state.fieldList = fieldList
  },
  setMappingTableField(state, data) {
    state.mappingTableField = data;
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
