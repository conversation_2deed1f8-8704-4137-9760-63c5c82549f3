import { logout, getInfo, detail, unityTicketToken, unityLogin } from '@/api/system/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import Cookies from "js-cookie";
import store from "@/store";

const state = {
  token: getToken(),
  id: undefined,
  adminType: undefined,
  realName: '',
  roles: [],
  permissions: [],
  departmentCode: '',
  departmentSign: '',//地区、单位、部门
  departmentId: '',
  departmentParentId: '',//上级部门id，部门人员用到
  subDepartmentCode: '',//下级部门code，地区人员时用到
  duty: undefined
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_ADMIN_TYPE: (state, adminType) => {
    state.adminType = adminType
  },
  SET_REAL_NAME: (state, realName) => {
    state.realName = realName
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_DEPARTMENT_CODE: (state, departmentCode) => {
    state.departmentCode = departmentCode
  },
  SET_DEPARTMENT_SIGN: (state, departmentSign) => {
    state.departmentSign = departmentSign
  },
  SET_DEPARTMENT_ID: (state, departmentId) => {
    state.departmentId = departmentId
  },
  SET_DEPARTMENT_PARENT_ID: (state, departmentParentId) => {
    state.departmentParentId = departmentParentId
  },
  SET_DEPARTMENT_SUB_CODE: (state, subDepartmentCode) => {
    state.subDepartmentCode = subDepartmentCode
  },
  SET_USER_ID: (state, id) => {
    state.id = id
  },
  SET_DUTY: (state, duty) => {
    state.duty = duty
  }
}

const actions = {
  unityTicketToToken({ commit }, ticket) {
    if (!getToken() || getToken() === 'null') {
      removeToken()
    }
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('username', window.globalConfig.unityLogin.name);
      formData.append('password', window.globalConfig.unityLogin.pass);
      formData.append('ak', API.ak);
      unityLogin(formData).then(r=>{
        const { data } = r
        unityTicketToken(data.ticket == null ? { ticket: data } : data).then(res => {
          const { token } = res
          setToken(token)
          resolve(token)
        }).catch(error => {
          reject(error)
        })

      })
    })
  },

  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.token)``
        setToken(data.token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },


  getDetail({ commit }) {
    return new Promise((resolve, reject) => {
      detail().then(response => {
        const { data } = response
        if (!data) {
          reject('验证失败, 请再次尝试登录.')
        }
        if (data.departmentList != null && data.departmentList.length > 0) {
          commit('SET_DEPARTMENT_CODE',data.departmentList[0].departmentCode)
          commit('SET_DEPARTMENT_SIGN',data.departmentList[0].departmentSign)
          commit('SET_DEPARTMENT_ID',data.departmentList[0].id)
          commit('SET_DEPARTMENT_PARENT_ID',data.departmentList[0].parentId)
          commit('SET_DEPARTMENT_SUB_CODE',data.departmentList[0].subDepartmentCode)
        }
        commit('SET_ADMIN_TYPE',data.adminType)
        commit('SET_USER_ID',data.id)
        commit('SET_DUTY',data.duty)
        commit('SET_REAL_NAME',data.realname)
        resolve(data)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // get user info
  getInfo({ commit }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response
        if (!data) {
          reject('验证失败, 请再次尝试登录.')
        }
        const { name } = data
        if (data.roles && data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
          commit('SET_ROLES', data.roles);
          commit('SET_PERMISSIONS', data.permissions);
        } else {
          commit('SET_ROLES', ['ROLE_DEFAULT']);
        }
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    // console.log("logout-store")
    return new Promise((resolve, reject) => {
      // console.log("logout-request")
      logout(state.token).then(() => {
        // console.log("logout-clear")
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        Cookies.remove('popped',  { domain: process.env.VUE_APP_DOMAIN, path: '/' })
        removeToken()
        resetRouter()
        resolve()
      }).catch(error => {
        // console.log(error)
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      resolve()
    })
  },

  setToken({ commit }, token) {
    return new Promise(resolve => {
      commit('SET_TOKEN', token)
      setToken(token)
      resolve()
    })
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      await dispatch('getInfo')
      await dispatch('getDetail')

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch('permission/generateRoutes')

      // dynamically add accessible routes
      router.addRoutes(accessRoutes)

      // reset visited views and cached views
      dispatch('tagsView/delAllViews', null, { root: true })

      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
