const getters = {
  style: state => state.app.style,
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  sex: state => state.user.sex,
  sSize: state => state.user.sSize,
  avatar: state => state.user.avatar,
  postName: state => state.user.postName,
  orgName: state => state.user.orgName,
  realName: state => state.user.realName,
  permissions: state => state.user.permissions,
  roles: state => state.user.roles,
  permission_routes: state => state.permission.routes,
  errorLogs: state => state.errorLog.logs,
  dictTypes: state => state.dict.dictTypes,
  dicts: state => state.dict.dicts,
  getXsField: state => (xsIndex, field) => {
    let { mappingTableField } = state;
    if (mappingTableField) {
      let target = mappingTableField[xsIndex];
      if (target) {
        let find = target.find(find => find.tableFieldSearchType == field);
        return find && find.tableField;
      }
    }
  },
  getXsFieldValue: (state, getters) => (row, field) => {
    return row[getters.getXsField(row._es_index, field)];
  }
}
export default getters
