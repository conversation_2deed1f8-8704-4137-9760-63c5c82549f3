.page-container {
    margin: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    min-height: 50vh;
}

.page-content {
    float: left;
    width: 100%;
    height: 100%;
}

.page-div {
    height: calc(100% - 85px);
}

.card-item {
    padding: 5px 32px;
}

//------------------------------
// 公共el-dialog样式1(白色调)
.style1~.el-dialog__wrapper .el-dialog {
    border-radius: 8px;

    .header {
        border-radius: 8px !important;
        background-color: #fff !important;
        font-weight: 600 !important;
        height: 40px !important;
        color: #000 !important;
        font-size: 16px !important;
        line-height: 1.5 !important;
        padding: 12px 25px !important;

        .iccon-btn-close {
            float: right;
            font-size: 16px;
            font-weight: 600;
            color: #000;
            transition: all 0.3s;

            &:hover {
                color: #000;
                font-size: 20px;
                cursor: pointer;
            }
        }
    }

    .content-wrapper {
        padding: 25px 25px;
        overflow: hidden;

        .form {
            margin-top: 15px;

            .form-items-wrapper {
                border: 1px solid #e9eaec;
                padding: 16px 16px 0;
                overflow: hidden;

                .el-form-item {
                    float: left;
                    width: 50%;

                    .el-select {
                        width: 100%;
                    }
                }
            }

            ::v-deep .el-form-item__label {
                font-size: 14px;
                font-weight: bold;
            }
        }

        .uploads {
            margin: 15px 0 15px 20px;

            ::v-deep .el-upload,
            ::v-deep .el-upload-dragger {
                width: 100%;
            }
        }
    }

}

::v-deep .el-dialog__body {
    padding: 0 !important;
}

::v-deep .el-dialog__header {
    padding: 0px !important;
    padding-bottom: 0px !important;
    display: none !important;
}




//---------------------------------------------
// 共同el-dialog样式2（蓝色调）
.style2~.el-dialog__wrapper .el-dialog {
    border-radius: 0;

    .header {
        font-weight: 400 !important;
        border-radius: 0 !important;
        background-color: #4080f0 !important;
        height: 40px !important;
        color: #fff !important;
        padding: 12px 16px !important;

        .iccon-btn-close {
            float: right;
            font-size: 18px;
            color: #c4dcf4;
            transition: all 0.3s;

            &:hover {
                color: #fff;
                cursor: pointer;
            }
        }
    }

    .content-wrapper {
        padding: 15px 35px 15px 15px;
        overflow: hidden;

        .form {
            margin-top: 15px;

            .form-items-wrapper {
                border: 1px solid #e9eaec;
                padding: 16px 16px 0;
                overflow: hidden;

                .el-form-item {
                    float: left;
                    width: 50%;

                    .el-select {
                        width: 100%;
                    }
                }
            }

            ::v-deep .el-form-item__label {
                font-size: 14px;
                font-weight: bold;
            }
        }

        .uploads {
            margin: 15px 0 15px 20px;

            ::v-deep .el-upload,
            ::v-deep .el-upload-dragger {
                width: 100%;
            }
        }
    }

}

.el-menu-vertical-demo {

    width: 150px !important;

    .el-menu {
        width: 150px !important;
    }

    .el-submenu .el-menu-item {
        min-width: 150px !important;
    }
}

.component-right-pane {
    padding: 0 30px;
    width: calc(100% - 150px);
    overflow: auto;
}
