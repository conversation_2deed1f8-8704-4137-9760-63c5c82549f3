// base color
$blue: #324157;
$light-blue: #1B9CFF;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #ffb822;
$panGreen: #30B08F;

// sidebar
$menuText: hsla(0, 0%, 100%, .65);
$menuActiveText: #ffffff;
$subMenuActiveText: #ffffff; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #001529;
// $menuHover:#f5f6fc;
$menuHover: #fff;

$subMenuBg: #4080f0;
// $subMenuHover:#f5f6fc;
$subMenuHover: transparent;

$menuActiveBg: #f5f6fc;

$sideBarWidth: 220px;

$topBarHeight: 60px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  menuActiveBg: $menuActiveBg;
}
