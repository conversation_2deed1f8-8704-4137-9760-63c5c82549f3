@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './custom-btn.scss';

// @import './sidebar2.scss';

// 修改后的el-dialog通用样式

.el-dialog {
  .header {
    background-color: #4080f0;
    height: 40px;
    color: #fff;
    padding: 12px 16px;

    .iccon-btn-close {
      float: right;
      font-size: 18px;
      color: #c4dcf4;
      transition: all 0.3s;

      &:hover {
        color: #fff;
        cursor: pointer;
      }
    }
  }

  .el-dialog__body {
    padding: 0 !important;
  }

  .el-dialog__header {
    padding: 0px !important;
    padding-bottom: 0px !important;
    display: none !important;
  }

  .content-wrapper {
    padding: 15px 35px 15px 15px;
    overflow: hidden;

    .dialog-footer {
      float: right;
    }
  }
}

.el-drawer__wrapper {

  #el-drawer__title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 16px;
  }

  .content-wrapper {
    padding: 20px 35px 20px 20px;
    overflow: hidden;

    .msg-form .el-form-item {
      margin-bottom: 0;
      border-bottom: 1px dashed #DCDFE6;
    }

    .drawer-footer {
      float: right;
    }
  }
}

.flex-2 {
  display: flex;
  flex-wrap: wrap;
}

.flex-2>div {
  flex: 50%;
}

.flex {
  display: flex;
}

.w-2 {
  width: 50%;
}

.w-1 {
  width: 100%;
}

body {
  margin: 0;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式

.main-container {
  background-color: #f4f4f4;
}

.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}


//一级标题字体
.level-i-title {
  font-size: 22px;
  color: #879bba;
}

//二级/三级标题字体
.level-ii-title {
  font-size: 18px;
  color: #2d405e;
}

//图标与文字默认间隔
.icon-margin {
  margin-right: 5px;
}

//box样式
.box-card {
  margin-bottom: 25px !important;
  box-shadow: 0px 0px 13px 0px rgba(82, 63, 105, 0.05) !important;
  border-radius: 4px !important;
  border: none !important;
}

//图标紧急程度样式
.email-danger {
  color: #f86359;
}

.email-warning {
  color: #ffad32;
}

.email-open {
  color: #c0c3c9;
}

.email-info {
  color: #1B9CFF;
}

//tab选项样式
.nav-link {
  color: #595d6e;
  font-weight: 600;
  padding: 6px 12px;
  margin: 0 4px;
}

.nav-link:hover {
  color: #22b9ff;
}

.active {
  background-color: rgba(34, 185, 255, 0.1);
  color: #22b9ff;
  border-radius: 2px;
}

//分页条样式
.index-pagination {
  padding-top: 20px;
  text-align: center;
}

//覆盖el-UI table样式
.el-table thead {
  font-size: 16px;
  color: #2d405e;
}

.el-table {
  font-size: 16px;
  color: #4f5e7b;
}

.el-table--medium th,
.el-table--medium td {
  padding: 10px 0;
}

.el-table th.is-leaf,
.el-table td {
  border-bottom: 1px dashed #DDE7F2;
}

.el-table__column-filter-trigger i {
  font-size: 16px;
  color: #2d405e;
}

.el-table .cell {
  padding-left: 8px;
  padding-right: 8px;
}

//覆盖el-UI card样式
.el-card__body {
  padding: 0 20px 20px;
}

.el-table__column-filter-trigger {
  margin-left: 5px;
}

//自定义el-UI input样式
.index-container-input .el-input__inner {
  border-radius: 20px;
  background: transparent;
  width: 300px;
  border-color: #aca7f9;
}

.index-container-input-date .el-input__inner {
  border-radius: 20px;
  background: transparent;
  width: 200px;
  border-color: #aca7f9;
}

//自定义el-UI card样式
.index-container-card .el-card__body {
  padding: 0 !important;
}


//自定义el-UI tabs样式
.index-tabs .el-tabs__nav {
  padding: 8px 20px;
}

.index-tabs .el-tabs__active-bar {
  left: 20px;
}

.index-tabs .el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #DDE7F2;
}

//覆盖el-UI dropdown样式
.el-dropdown {
  color: #a9c4df;
  cursor: pointer;
}

//覆盖el-UI 日历
.el-calendar-table .el-calendar-day {
  height: 50px;
}

.el-calendar__body {
  padding: 12px 20px 20px;
}

//头像和标记的样式
.index-item-icon-badge .el-badge__content {
  margin-left: -30px;
  position: relative;
  margin-top: 8px;
}

//滚动条
.el-scrollbar__wrap:not(.el-select-dropdown__wrap) {
  overflow-x: hidden;
}

.dialog-header {
  border-bottom: 1px solid #e8e8e8 !important;
  color: #333;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  font-weight: 700;
  align-items: center;
  font-size: 16px;
}

.dialog-container {
  padding: 16px 24px;
}

.description {
  font-size: 10px;
  color: #909399;
}

.expandTable-wrapper {
  background-color: #fafafa;
  padding: 16px;
}

.tip {
  font-size: 12px;
  color: #606266;
  line-height: 30px;
}