
.set_1_btn {
	color: #333;
	cursor: pointer;
	display: block;
	font-size: 16px;
	font-weight: 400;
	line-height: 40px;
	margin-right: 2em;
	text-align: center;
	max-width: 160px;
	position: relative;
	text-decoration: none;
	text-transform: uppercase;
	/* vertical-align: middle; */
	width: 100%;
	font-family: 'fzltxh';
}
.set_1_btn:hover {
	text-decoration: none;
}
.Vbtn-1 {
	background:transparent;
	text-align: center;
	margin:0 auto;
}
.Vbtn-1 svg {
	height: 40px;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.Vbtn-1 rect {
	fill: none;
	stroke: #b4b4b4;
	stroke-width: 3;
	stroke-dasharray: 422, 0;
	transition: all 450ms linear 0s;
}
.Vbtn-1:hover {
	background: rgba(225, 51, 45, 0);
	font-weight: 900;
	letter-spacing: 1px;
	transition: all 150ms linear 0s;
}
.Vbtn-1:hover rect {
	stroke-width: 5;
	stroke-dasharray: 15, 310;
	stroke-dashoffset: 48;
	-webkit-transition: all 1.35s cubic-bezier(0.19, 1, 0.22, 1);
	transition: all 1.35s cubic-bezier(0.19, 1, 0.22, 1);
}
.Vbtn-2 {
	letter-spacing: 0;
	transition: all 150ms linear 0s;
	float:left;
}
.Vbtn-2:hover, .Vbtn-2:active {
	letter-spacing: 5px;
	transition: all 150ms linear 0s;
}
.Vbtn-2:after, .Vbtn-2:before {
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	border: 1px solid rgba(255, 255, 255, 0);
	bottom: 0px;
	content: " ";
	display: block;
	margin: 0 auto;
	position: relative;
	-webkit-transition: all 280ms ease-in-out;
	transition: all 280ms ease-in-out;
	width: 0;
}
.Vbtn-2:hover:after, .Vbtn-2:hover:before {
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	border-color: #56c5ff;
	-webkit-transition: width 350ms ease-in-out;
	transition: width 350ms ease-in-out;
	width: 70%;
}
.Vbtn-2:hover:before {
	bottom: auto;
	top: 0;
	width: 70%;
}


.Vbtn-3 {
	background:transparent;
	text-align: center;
	float:left;
}
.Vbtn-3 svg {
	height: 40px;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.Vbtn-3 rect {
	fill: none;
	stroke: #f2625a;
	stroke-width: 3;
	stroke-dasharray: 422, 0;
	transition: all 450ms linear 0s;	
}
.Vbtn-3:hover {
	background: rgba(225, 51, 45, 0);
	font-weight: 900;
	letter-spacing: 1px;
	transition: all 150ms linear 0s;
}
.Vbtn-3:hover rect {
	stroke-width: 5;
	stroke-dasharray: 15, 310;
	stroke-dashoffset: 48;
	-webkit-transition: all 1.35s cubic-bezier(0.19, 1, 0.22, 1);
	transition: all 1.35s cubic-bezier(0.19, 1, 0.22, 1);
}

.Vbtn-4 {
	letter-spacing: 0;
	transition: all 150ms linear 0s;
	float:left;
}
.Vbtn-4:hover, .Vbtn-4:active {
	letter-spacing: 5px;
	transition: all 150ms linear 0s;
}
.Vbtn-4:after, .Vbtn-4:before {
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	border: 1px solid rgba(255, 255, 255, 0);
	bottom: 0px;
	content: " ";
	display: block;
	margin: 0 auto;
	position: relative;
	-webkit-transition: all 280ms ease-in-out;
	transition: all 280ms ease-in-out;
	width: 0;
}
.Vbtn-4:hover:after, .Vbtn-4:hover:before {
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	border-color: #666;
	-webkit-transition: width 350ms ease-in-out;
	transition: width 350ms ease-in-out;
	width: 70%;
}
.Vbtn-4:hover:before {
	bottom: auto;
	top: 0;
	width: 70%;
}

.set_4_button1 {
  position: relative;
  font-weight: 400;
  text-align: center;
  width: auto;
  padding:0 25px;
  border:1px solid #fff;
  overflow: hidden;
  position: relative;
  z-index: 0;
  cursor: pointer;
  color:#FFF;
  margin-right: 2.5em;
}
.set_4_button1.raised {
  -moz-transition: all 0.1s;
  -o-transition: all 0.1s;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
}
.set_4_button1.raised:active {
  box-shadow: 0px 1px 1px #063e6b;
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}



/****** button 2 *******/

.set_4_button2 {
  position: relative;
  font-weight: 400;
  text-align: center;
  width: auto;
  float:left;
  padding:0 25px;
  line-height:45px;
  overflow: hidden;
  position: relative;
  z-index: 0;
  cursor: pointer;
  color:#2e2302;
  margin-right: 2.5em;
}
.set_4_button2.raised {
  -moz-transition: all 0.1s;
  -o-transition: all 0.1s;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  background: #f8c013;
}
.set_4_button2.raised:active {
  background: #515151;
  box-shadow: 0px 1px 1px #063e6b;
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}




/****** button 3 *******/

.set_4_button3 {
  position: relative;
  font-weight: 400;
  text-align: center;
  width: auto;
  float:left;
  padding:0 25px;
  line-height:45px;
  overflow: hidden;
  position: relative;
  z-index: 0;
  cursor: pointer;
  color:#ffffff;
  margin-right: 2.5em;
}
.set_4_button3.raised {
  -moz-transition: all 0.1s;
  -o-transition: all 0.1s;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  background: #f2625a;
}
.set_4_button3.raised:active {
  background: #515151;
  box-shadow: 0px 1px 1px #063e6b;
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}



/****** button 4 *******/

.set_4_button4 {
  position: relative;
  font-weight: 400;
  text-align: center;
  width: auto;
  float:left;
  padding:0 25px;
  line-height:45px;
  overflow: hidden;
  position: relative;
  z-index: 0;
  cursor: pointer;
  color:#252b2d;
  margin-right: 2.5em;
}
.set_4_button4.raised {
  -moz-transition: all 0.1s;
  -o-transition: all 0.1s;
  -webkit-transition: all 0.1s;
  transition: all 0.1s;
  background: #b9ccd2;
}
.set_4_button4.raised:active {
  background: #515151;
  box-shadow: 0px 1px 1px #063e6b;
}
.anim {
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
}
.anim:before {
  position: relative;
  content: '';
  display: block;
  margin-top: 100%;
}
.anim:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 50%;
}

.clickable .toggle:checked + .anim {
  -moz-animation: anim-in 0.75s;
  -webkit-animation: anim-in 0.75s;
  animation: anim-in 0.75s;
}
.clickable .toggle:checked + .anim:after {
  -moz-animation: anim-in-pseudo 0.75s;
  -webkit-animation: anim-in-pseudo 0.75s;
  animation: anim-in-pseudo 0.75s;
}
.clickable .toggle:not(:checked) + .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.clickable .toggle:not(:checked) + .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}

.hoverable:hover > .anim {
  -moz-animation: anim-out 0.75s;
  -webkit-animation: anim-out 0.75s;
  animation: anim-out 0.75s;
}
.hoverable:hover > .anim:after {
  -moz-animation: anim-out-pseudo 0.75s;
  -webkit-animation: anim-out-pseudo 0.75s;
  animation: anim-out-pseudo 0.75s;
}
@-webkit-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-in {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-in-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-webkit-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-moz-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-ms-keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@keyframes anim-out {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
@-webkit-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-moz-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@-ms-keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}
@keyframes anim-out-pseudo {
  0% {
    background: rgba(0, 0, 0, 0.25);
  }
  100% {
    background: transparent;
  }
}

