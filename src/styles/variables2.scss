// base color
$blue: #324157;
$light-blue: #1B9CFF;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #ffb822;
$panGreen: #30B08F;

$menuText: hsla(0, 0%, 100%, .65);
$menuActiveText: #4080f0;
$subMenuActiveText: #4080f0; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #001529;
// $menuHover:#f5f6fc;
$menuHover: #ecf5ff;

$subMenuBg: #4080f0;
// $subMenuHover:#f5f6fc;
$subMenuHover: transparent;

$menuActiveBg: #f5f6fc;

$sideBarWidth: 200px;

$topBarHeight: 60px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  menuActiveBg: $menuActiveBg;
}
