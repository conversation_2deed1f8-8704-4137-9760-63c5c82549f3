#app {

  .main-container {
    height: 100%;
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
    padding-left: 1px;
  }

  .el-submenu .el-menu-item {
    height: auto !important;
    line-height: 40px !important;
    padding: 5px 20px !important;
  }

  .el-menu-item, .el-submenu__title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 600;
    padding: 5px 40px !important;
    > .iconfont {
      width: 17px;
      text-align: center;
      transition: all 0.3s;
    }
    > i:not(.iconfont) {
      //display: none;
      font-size: 16px;
      font-weight: bold;
      transform: rotate(270deg);
    }
  }

  .submenu-title-noDropdown, .el-submenu__title {
    &:after {
      content: '';
      display: block;
      width: 17px;
    }
  }

  .el-menu-item.is-active.submenu-title-noDropdown {
    color: $menuActiveText !important;
    i {
      color: $menuActiveText !important;
    }
  }

  .el-menu-item:not(.submenu-title-noDropdown) {
    justify-content: center;
    span {
      font-size: 13px;
      color: #68768C;
    }
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    text-align: center;
    // top:80px;
    // -webkit-box-shadow: 13px -2px 27px -12px rgba(41, 50, 66, 0.075);
    // box-shadow: 13px -2px 27px -12px rgba(41, 50, 66, 0.075);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .side__logo {
      user-select: none;
      padding-top: 16px;
      height: 120px;
      font-size: 13px;
      font-weight: bold;
      h4 {
        font-family: JLinXin, MicrosoftYaHei-Bold, MicrosoftYaHei;
        margin: 0;
        line-height: 1;
        text-align: center;
        width: 100%;
        color: #555;
      }
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
      height: calc(100% - 150px);
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      //margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      color: #68768C !important;
      &:hover {
        background-color: $menuHover !important;
        color: $menuActiveText !important;
        > i {
          color: $menuActiveText !important;
        }
      }
    }

    // menu active
    .is-active {
      background-color: $menuHover !important;
    }

    .el-menu-item.is-active:not(.submenu-title-noDropdown) {
      span {
        display: inline-block;
        width: 100%;
        border-radius: 8px;
        background: linear-gradient(to bottom, #1991EB, #2EA1F8);
        color: #fff !important;
      }
    }

    .is-active > .el-submenu__title,
    .is-opened > .el-submenu__title {
      color: $subMenuActiveText !important;
      > i {
        color: $subMenuActiveText !important;
      }
      > i:not(.iconfont) {
        transform: rotate(360deg);
      }
    }

    .el-submenu .el-menu-item i {
      display: none;
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg;

      &:not(.is-active):hover {
        background-color: $subMenuHover !important;
        color: $menuActiveText !important;
        > i {
          color: $menuActiveText !important;
        }
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 100px !important;
    }

    .main-container {
      margin-left: 100px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          //margin-left: 35px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;
        justify-content: center;

        .svg-icon {
          //margin-left: 35px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  .el-menu--collapse .el-submenu__title:after {
    display: none!important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
      color: $menuActiveText !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}


