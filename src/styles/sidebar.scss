#app {

  .style1 {
    .el-menu {
      background-color: #f4f4f4;
      margin-left: 13px;
    }

    .el-submenu .el-menu-item {
      width: 180px !important;
    }

    .el-menu-item.is-active:not(.submenu-title-noDropdown) {
      background-color: #fff !important;
      border-radius: 8px;
    }

    .el-menu-item {
      &:hover {
        color: $menuActiveText !important;
        background-color: #f4f4f4 !important;
      }
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      font-size: 14px;
      font-weight: 500;
      color: #000 !important;

      &:hover {
        background-color: #f4f4f4 !important;
        color: $menuActiveText !important;

        >i {
          color: $menuActiveText !important;
        }
      }
    }
  }




  .style2 {

    .el-menu,
    .el-scrollbar {
      background-color: #fff;
    }

    .el-menu-item.is-active:not(.submenu-title-noDropdown) {
      background-color: #2B5B83 !important;
      border-right: 5px solid #409EFF;
      border-radius: 0px;
      color: #fff;
    }

    .el-menu-item {
      width: auto;

      &:hover {
        border-right: 5px solid #2e70b5;
        color: $menuActiveText !important;
        background-color: #49586c !important;
      }
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      height: 60px;
      background: #455467;
      font-size: 14px;
      font-weight: 500;
      color: #d6d6d6 !important;
      padding-left: 25px !important;


      &:hover {
        border-right: 5px solid #409EFF;
        color: $menuActiveText !important;
        background-color: #49586c !important;
      }
    }

  }

  .main-container {
    height: calc(100% - #{$topBarHeight});
    transition: margin-left .28s;
    position: relative;
    display: flex;

    >.sidebar-wrapper {
      flex-shrink: 0;
    }
  }

  // 三级菜单单独设置
  .el-menu .menu-wrapper ul .menu-wrapper .el-submenu__title {
    // color: red !important;
    width: 206px;
    padding-left: 30px !important;

    i:first-child {
      display: none !important;
    }

  }


  .el-menu-item {
    height: 65px !important;
    line-height: 65px !important;
    padding: 5px 0px !important;

    // margin-left: 50px;


    &:focus {
      color: $menuActiveText;
      background-color: #f4f4f4 !important;
    }

    i {
      margin-right: 0;
    }

    i+span {
      margin-left: 5px;
    }

    span {
      width: 8em;
      text-align: left;
    }
  }

  .temp-block {
    display: inline-block;
    height: 14px;
    width: 14px;
  }

  .el-submenu .el-menu-item {
    height: 50px !important;
    line-height: 40px !important;
    padding: 5px 0px !important;
    min-width: 0px !important;
  }

  .el-menu-item,
  .el-submenu__title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 30px;
    padding: 2px 10px !important;
    text-align: center;
    height: 40px;

    .el-submenu {
      background-color: #000;
    }

    >.iconfont {
      width: 17px;
      text-align: center;
      transition: all 0.3s;
    }

    /* > i:not(.iconfont) {
       //display: none;
       font-size: 14px;
       font-weight: bold;
       transform: rotate(270deg);
     }*/
  }

  .submenu-title-noDropdown,
  .el-submenu__title {
    &:after {
      content: '';
      display: block;
      width: 17px;
    }
  }

  .el-menu-item.is-active.submenu-title-noDropdown {
    position: relative;
    color: $menuActiveText !important;


    i {
      color: $menuActiveText !important;
    }
  }

  .el-menu-item:not(.submenu-title-noDropdown) {
    background: #304156;
    color: #d6d6d6;
    justify-content: flex-start;
    padding-left: 25px !important;
    span {
      font-size: 14px;
      font-weight: 500;
      // color: #000;
    }
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    height: 100%;
    font-size: 0;
    overflow: hidden;
    text-align: center;
    flex-shrink: 0;
    background: #f4f4f4;
    //border-radius: 4px;
    // top:80px;
    // -webkit-box-shadow: 13px -2px 27px -12px rgba(41, 50, 66, 0.075);
    // box-shadow: 13px -2px 27px -12px rgba(41, 50, 66, 0.075);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .side__logo {
      user-select: none;
      padding-top: 16px;
      height: 120px;
      font-size: 13px;
      font-weight: bold;

      h4 {
        font-family: JLinXin, MicrosoftYaHei-Bold, MicrosoftYaHei;
        margin: 0;
        line-height: 1;
        text-align: center;
        width: 100%;
        color: #fff;
        font-size: 14px;
      }
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
      height: 100%;

      background: #304156
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      //margin-right: 16px;
    }

    .el-menu {

      border: none;
      height: 100%;
      width: 100% !important;

    }



    // menu active
    .is-active {}

    .el-menu-item.is-active:not(.submenu-title-noDropdown) {
      position: relative;
      // background-color: $menuHover !important;





      &:before {
        content: '';
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 2px;
        // background: $menuActiveText;
      }
    }

    .is-active>.el-submenu__title,
    .is-opened>.el-submenu__title {
      color: $subMenuActiveText !important;

      >i {
        color: $subMenuActiveText !important;
      }

      >i:not(.iconfont) {
        transform: rotate(360deg);
      }
    }

    .el-submenu .el-menu-item {
      padding-left: 40px !important;
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {

      min-height: 0 !important;

      &:not(.is-active):hover {
        color: $menuActiveText !important;

        >i {
          color: $menuActiveText !important;
        }
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 70px !important;
    }

    .main-container {}

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          //margin-left: 35px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;


      &>.el-submenu__title {
        padding: 0 !important;
        justify-content: center;

        .svg-icon {
          //margin-left: 35px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  .el-menu--collapse .el-submenu__title:after {
    display: none !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
      color: $menuActiveText !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
