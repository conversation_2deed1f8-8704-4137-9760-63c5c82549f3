::v-deep .el-table__expanded-cell {
    padding: 0;
}

.expandTable-wrapper {
    background-color: #fafafa;
    padding: 16px;
}

::v-deep .el-table__header thead th {
    background-color: #fafafa;
    font-size: 12px !important;
}

::v-deep .el-card__body {
    padding: 10px;
}

::v-deep .el-table .cell {
    font-size: 12px;
}

.description {
    font-size: 10px;
    color: #909399;
}

::v-deep .underline-font {
    color: #409EFF;
    cursor: pointer;
    text-decoration: underline;
    text-underline-offset: 4px;
}

.clickable-span {
    color: #409EFF;
    cursor: pointer;
}


/* 修改表单的样式 */
::v-deep .el-form-item__label,
::v-deep .el-radio__label,
::v-deep .el-input--small {
    font-size: 12px;
}

::v-deep .el-radio__label {
    font-weight: 400;
}



// 将el-table的展开图标替换为其他图标
::v-deep .el-table__expand-icon {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}

::v-deep .el-table__expand-icon .el-icon-arrow-right:before {
    content: "\e6d9";
    border: 1px solid #ccc;
    font-weight: bold;
    padding: 2px;
}

::v-deep .el-table__expand-icon--expanded .el-icon-arrow-right:before {
    content: "\e6d8";
}

// 表格多选框大小
::v-deep .el-checkbox__inner {
    width: 18px;
    height: 18px;
}

::v-deep .el-checkbox__inner::after {
    height: 10px;
    left: 6px;

}

::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    top: 7px;
}

/* 修改 */
::v-deep .el-tabs--left .el-tabs__active-bar.is-left {
    width: 2px;
}

::v-deep .el-tabs__item {
    font-size: 14px;
    font-weight: 400;
}

::v-deep .el-tabs__nav-wrap::after {
    background-color: #EBEEF5;
}

.el-notification.right {
    background-color: #fff !important;
}

::v-deep .el-notification.right {
    background-color: #fff !important;
}

.search-bar {
    display: flex;
}


.dialog-wrapper {

    font-size: 12px;

    .dialog-header {
        border-bottom: 1px solid #e8e8e8;
        padding: 16px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, .85);
        line-height: 22px;

        i {
            cursor: pointer;
        }
    }

    .dialog-container {
        padding: 16px 16px;

    }
}

.drawer-wrapper {
    padding: 16px;
}

.el-message-box__title {
    font-size: 14px;
    font-weight: bold;
}

.el-message-box__message {
    font-size: 12px;
}

th.el-table__cell {
    font-size: 12px;
}

.el-table td.el-table__cell {
    font-size: 12px;

}

.el-notification {
    z-index: 999999999 !important;
}