<template>
  <div>
    <!-- 详情-->
    <el-dialog
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :show-close="false"
        :fullscreen="true"
        custom-class="full-screen-dialog"
        @close="closeDialog"
    >
      <div class="header">
        <span>{{activeName}}详情</span>
        <i class="el-icon-close iccon-btn-close" @click="closeDialog" />
      </div>
      <div class="content-wrapper">
          <el-table
              :data="detailTableData"
              style="width: 100%;"
              v-loading="detailLoading"
              @row-click="handleChildrenRowClick"
              ref="table"
          >
            <el-table-column prop="bmsah" label="部门受案号" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ajmc" label="案件名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="yswswh" label="(不)起诉意见书文书号" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ysay_aymc" label="移送案由" width="250"></el-table-column>
            <el-table-column prop="slrq" label="受理日期" width="150"></el-table-column>
            <el-table-column prop="cbbm_mc" label="承办部门" width="100"></el-table-column>
            <el-table-column prop="cbjcg" label="承办人" width="100"></el-table-column>
          </el-table>

          <div class="pagination-container" style="display: flex;justify-content: flex-end;">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="listQuery.pageNo"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="listQuery.pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
          </div>
<!--        </div>-->
      </div>
      <span slot="footer" class="dialog-footer">
<!--        <el-button @click="closeDialog" type="primary">确定</el-button>-->
        <el-button @click="closeDialog">关闭</el-button>
<!--        <el-button type="primary" @click="generateAllReports">生成质量评查报告（个人）</el-button>-->
      </span>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog title="案件详情" :visible.sync="DetailDialogVisible" :modal-class="'custom-overlay'" width="70%" :before-close="handleDetailClose" append-to-body custom-class="case-detail-dialog">
      <div class="dialog-wrapper">
        <div class="dialog-header">
          <span>案件详情</span>
          <i class="el-icon-close" @click="handleDetailClose" />
        </div>
        <div class="dialog-container">
          <el-descriptions
              :column="2"
              border
              class="warning-detail-form"
              :labelStyle="{ width: '250px', padding: '6px 10px', textAlign: 'right',backgroundColor: 'white',fontSize: '16px' }"
              :contentStyle="{ padding: '6px', color: '#5c79b0',fontSize: '16px' }"
          >
            <el-descriptions-item label="部门受案号">
              <div class="detail-content"> {{ currentRow.bmsah }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="案件名称">
              <div class="detail-content">{{ currentRow.ajmc }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="(不)起诉意见书文书号">
              <div class="detail-content">{{ currentRow.yswswh }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="移送案由">
              <div class="detail-content">{{ currentRow.ysay_aymc }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="受理日期">
              <div class="detail-content">{{ currentRow.slrq }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办单位">
              <div class="detail-content">{{ currentRow.cbdw_mc }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办单位编码">
              <div class="detail-content">{{ currentRow.cbdw_bm }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="承办部门编码">
              <div class="detail-content">{{ currentRow.cbbm_bm }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办部门">
              <div class="detail-content">{{ currentRow.cbbm_mc }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="承办人工号">
              <div class="detail-content">{{ currentRow.cbjcgbm }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办人">
              <div class="detail-content">{{ currentRow.cbjcg }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="创建时间">
              <div class="detail-content">{{ currentRow.cjsj }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="最后修改时间">
              <div class="detail-content">{{ currentRow.zhxgsj }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {
  warningCaseDetail,
  warningCasePage,
  warningGroupRule,
  warninglistByFilter,
  warningPageList
} from "@/api/api/warningInfo"
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, WidthType, AlignmentType, BorderStyle, VerticalAlign, Indent, VerticalMerge, PageBreakBefore, HeadingLevel } from 'docx';
import { saveAs } from 'file-saver';
import * as docx from "docx"
import { listCbrpcajqd } from "@/api/api/qualityEvaluation"

export default {
  name: "allDetailDialog",
  props: {
    dialogVisible:{
      type: Boolean,
      default: false
    },
    activeName: {
      type: String,
      default: ""
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      detailLoading: false,
      DetailDialogVisible: false,
      tableCellStyleType: {
        max: 0,
        min: 0,
        zero: 0
      },
      listQuery: {
        page: 1,
        rows: 5,
        entity: {
          cbrgh: "",
          startTime: "",
          endTime: ""
        }
      },
      currentRow: {},
      loading: true,
      tableData: [],
      tableHeader: [],
      columns: [],
      currentCell: {
        department: "",
        cbr: "",
        columnTitle: "",
        value: ""
      },
      detailTableData: [],
      total: 0,
    };
  },
  watch: {
    activeName(activeName) {
      if (activeName) {
        this.listQuery = {
          pageNo: 1,
          pageSize: 10,
          endTime: this.rowData.endTime,
          startTime: this.rowData.startTime,
          cbrgh: this.rowData.cbrgh,
        };
        // 获取详情数据
        this.getDetailTableData();
      }
    }
  },
  async mounted() {

  },
  methods: {
    handleDetailClose() {
      this.currentRow = {}
      this.DetailDialogVisible = false
    },
    handleChildrenRowClick(row){
      this.currentRow = row
      this.DetailDialogVisible = true
    },
    handleCurrentChange(page) {
      this.listQuery.pageNo = page;
      this.getDetailTableData();
    },
    handleSizeChange(size) {
      this.listQuery.pageSize = size;
      this.getDetailTableData();
    },
    async getDetailTableData() {
      this.detailLoading = true;
      const { data,total } = await warningCasePage(
          this.listQuery
      );
      this.detailTableData = []
      this.detailTableData = data;
      this.total = total;
      this.detailLoading = false;
    },
    closeDialog() {
      this.$emit('close')
    },
    getStateText(state) {
      switch(state) {
        case '0': return '待处理';
        case '1': return '待核查';
        case '2': return '已纠正';
        case '3': return '已归档';
        case '4': return '误报';
        default: return '-';
      }
    },
  }
};

</script>
<style scoped lang="scss">

.full-screen-dialog {
  margin: 0 !important;
  padding: 0 !important;

  /deep/ .el-dialog {
    margin: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
  }

  /deep/ .el-dialog__wrapper {
    overflow: hidden !important;
  }

  .header {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    background: #409EFF;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .iccon-btn-close {
      cursor: pointer;
      font-size: 20px;
    }
  }

  .content-wrapper {
    flex: 1;
    padding: 20px;
    overflow: auto;
    height: calc(100vh - 120px);
  }

  /deep/ .el-dialog__body {
    padding: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
  }

  /deep/ .el-dialog__footer {
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-top: 1px solid #e4e7ed;
  }
}
.warning-info {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 16px;
  line-height: 22px;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 30px;
  overflow: auto;
  background-color: #f5f7fa;

  .search-form {

    /deep/ .custom-input {
      .el-input__inner {
        height: 34px;
        border-radius: 3px;
        border-color: #A9C4DF;
        color: #4f5e7b;
        padding: 0 10px;

        &:hover {
          color: #BAC9DF;
        }

        &:focus {
          border-color: #4084F0;
          color: #4F5E7B;
        }
      }

      .el-input__inner::placeholder { /* Standard syntax */
        color: #879BBA;
      }
    }

    /deep/ .custom-select {
      .el-input__inner {
        border-radius: 3px;
        height: 34px;
        border-color: #A9C4DF;
        color: #4f5e7b;
        padding: 0 10px;

        &:hover {
          border-color: #879BBA;
          color: #BAC9DF;
        }

        &:focus {
          border-color: #4084F0;
          color: #4F5E7B;
        }


      }

      .el-input__inner::placeholder { /* Standard syntax */
        color: #879BBA;
      }

      .el-select-dropdown__item{
        padding: 0 6px;

        &:hover {
          color: #217fdc;
          background-color: #c4dcf4;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .detail-content {
    line-height: 22px;
    // background-color: #F3F6F8
  }
}
.dialog-wrapper {

  .dialog-header {
    height: 40px;
    padding-left: 15px;
    padding-right: 0;
    background-color: #4084f0;
    line-height: 40px;
    font-weight: normal;

    .el-icon-close {
      font-size: 12px;
      line-height: 40px;
      width: 40px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #F5B923;
      }
    }
  }
  .dialog-container {
    padding: 15px;
  }
}
/* 定义自定义遮罩层样式 */
.custom-overlay {
  background-color: #000000;
  opacity: 0.6;
}

/deep/ .el-table__header-wrapper {
  min-height: 40px;
  color: #2D405e;
  font-weight: bold;
  border-top: #A9C4DF 1px solid;
  border-bottom: #A9C4DF 1px solid;

  .cell {
    font-size: 16px;
  }
}

/deep/  .el-table__body tr:hover > td {
  background-color: #fffdec !important; /* 悬停时的背景颜色 */
}

/deep/ .el-table__body-wrapper {
  .el-table__row {
    border-bottom: #A9C4DF 1px solid;
    cursor: pointer;
  }


  /* 覆盖默认的斑马纹样式 */
  .el-table__row:nth-child(odd) {
    background-color: #f3f6fb; /* 奇数行背景颜色 */
  }

  .el-table__row:nth-child(even) {
    background-color: white; /* 偶数行背景颜色 */
  }

  .cell {
    font-size: 16px;
  }
}

/deep/ .el-tag {
  color: #ffffff;
  padding: 2px;
  font-size: 12px;
  line-height: 12px;
  height: 18px;
  font-family: 'SimSun', '\5B8B\4F53', sans-serif;
  border-radius: 2px;
}

/deep/ .el-tag--primary {
  background-color: #1b9cff;
}

/deep/ .el-tag--success {
  background-color: #1dc5b3;
}

/deep/ .el-tag--danger {
  background-color: #f36859;
}

.page-btn {
  color: white;
  padding: 5px 10px;
  background-color: #c9cfd7;
  min-width: 50px !important;
  height: 27px;
  margin-top: 2px;
}

/deep/ .case-detail-dialog {
  margin-top: 2vh !important;
  margin-bottom: 2vh !important;

  .el-dialog {
    height: 96vh !important;
    margin: 0 auto !important;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0 !important;
  }
}

.dialog-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .dialog-header {
    // ... existing code ...
  }

  .dialog-container {
    flex: 1;
    padding: 15px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #909399;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #F5F7FA;
    }
  }
}

.warning-data-container {
  max-height: 400px;  // 增加预警数据容器的高度
  min-height: 300px;  // 增加预警数据容器的高度
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #F5F7FA;
  }
}

/deep/ .el-table__expand-icon {
  display: none !important;
}

.expand-detail {
  padding: 20px 40px;
  background: #f8f9fb;

  .label-cell {
    color: #606266;
    font-weight: 500;
    padding: 12px;
    background: #f5f7fa;
  }

  .value-cell {
    padding: 12px;
    color: #5c79b0;
  }
}

/deep/ .el-table__expanded-cell {
  padding: 0;
}

/deep/ .el-table--border {
  border-radius: 4px;
  overflow: hidden;
}

</style>
