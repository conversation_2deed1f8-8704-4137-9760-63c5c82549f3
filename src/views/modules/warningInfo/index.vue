<!--
 * @Description:
-->
<template>
  <div class="duration-statistics">
    <!-- 顶部筛选区域 -->
    <div class="filter-section">
      <el-form inline>
        <el-form-item>
          <el-radio-group
              v-model="searchForm.timeType"
              @change="handleTimeTypeChange"
              style="margin-right: 10px"
          >
            <el-radio :label="1">单月</el-radio>
            <el-radio :label="2">时间段</el-radio>
          </el-radio-group>
          <el-date-picker
              v-show="searchForm.timeType == 1"
              v-model="searchForm.singleMonth"
              type="month"
              placeholder="选择月份"
              value-format="yyyyMM"
              class="date-picker"
              @change="getList"
          >
          </el-date-picker>

          <el-date-picker
              v-if="searchForm.timeType == 2"
              v-model="searchForm.monthrange"
              type="monthrange"
              range-separator="至"
              value-format="yyyyMM"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="getList"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
<!--          <el-button type="primary" @click="download">下载</el-button>-->
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 新增标题区域 -->
      <div class="table-title">
        <div class="title-decoration"></div>
        <h2>案件质量预警情况</h2>
      </div>
<!--      <div style="margin-top: 5px;margin-bottom: 15px">-->
<!--        <el-checkbox v-model="tableCellStyleType.max" :true-label="1" :false-label="0">最大值</el-checkbox>-->
<!--        <el-checkbox v-model="tableCellStyleType.min" :true-label="1" :false-label="0">最小值</el-checkbox>-->
<!--        <el-checkbox v-model="tableCellStyleType.zero" :true-label="1" :false-label="0">0值</el-checkbox>-->
<!--      </div>-->
      <!-- :summary-method="getSummaries" -->
      <!-- 表格区域 -->
      <el-table
          :data="tableData"
          border
          style="width: 100%"
          :span-method="arraySpanMethod"
          v-loading="loading"
          :header-cell-style="{color: '#fff',fontWeight: 'bold',background: '#409EFF'}"
          :cell-style="cellStyle"
      >
        <!-- 部门和人员列 -->
        <el-table-column
            prop="department"
            label="部门"
            width="150"
            fixed="left"
            align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.department }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="cbr" label="员额" width="130" fixed="left" align="center">
          <template slot-scope="scope">
            <span>
              {{ scope.row.cbr }}
            </span>
          </template>
        </el-table-column>
        <template v-for="item in tableHeader">
          <template v-if="item.subTitle && item.subTitle.length">
            <!-- 一级菜单 -->
            <el-table-column :label="item.title" align="center" width="100">
              <!-- 二级菜单 -->
              <template v-for="subItem in item.subTitle">
                <el-table-column
                    :label="subItem.title"
                    :prop="item.title + '-' + subItem.title"
                    width="110"
                    align="center"
                >
                  <template slot-scope="scope">
                    <span
                        class="cell-content"
                        @click="
                        handleCellClick(
                          scope.row,
                          subItem,
                          scope.$index,
                          subItem.codes
                        )
                      "
                    >
                      <!-- {{
                        scope.row[item.title + "-" + subItem.title]
                          ? scope.row[item.title + "-" + subItem.title] +
                            subItem.suffix
                          : "-"
                      }} -->
                      <span v-if="scope.row[item.title + '-' + subItem.title]">
<!--                        <span class="max-num" v-if="isMax(scope.row[item.title + '-' + subItem.title], item.title + '-' + subItem.title) && scope.row.department != '总计'">-->
                        <!--                          {{ scope.row[item.title + "-" + subItem.title] }}-->
                        <!--                        </span>-->
                        <!--                        <span class="min-num" v-else-if="isMin(scope.row[item.title + '-' + subItem.title], item.title + '-' + subItem.title) && scope.row.department != '总计'">-->
                        <!--                          {{ scope.row[item.title + "-" + subItem.title] }}-->
                        <!--                        </span>-->
                        <!--                        <span v-else>{{ scope.row[item.title + '-' + subItem.title] }}</span>-->
                        <span>{{ scope.row[item.title + '-' + subItem.title] }}</span>
                      </span>
                      <span v-else>-</span>
                    </span>
                  </template>
                </el-table-column>
              </template>
            </el-table-column>
          </template>
          <template v-else>
            <!-- 没有子菜单 -->
            <el-table-column :label="item.title" :prop="item.title" :width="item.title.length > 7 ? 220:180" align="center">
              <template slot-scope="scope">
                <span
                    class="cell-content"
                    @click="
                    handleCellClick(scope.row, item, scope.$index, item.codes)
                  "
                >
                  <!-- {{
                    scope.row[item.title]
                      ? scope.row[item.title] + item.suffix
                      : "-"
                  }} -->
                    <span v-if="scope.row[item.title]">
<!--                      <span class="max-num" v-if="isMax(scope.row[item.title], item.title) && scope.row.department != '总计'">{{ scope.row[item.title] }}</span>-->
                      <!--                      <span class="min-num" v-else-if="isMin(scope.row[item.title], item.title) && scope.row.department != '总计'">{{ scope.row[item.title] }}</span>-->
                      <!--                      <span v-else>{{ scope.row[item.title] }}</span>-->
                      <span>{{ scope.row[item.title] }}</span>
                    </span>
                    <span v-else>-</span>
                </span>
              </template>
            </el-table-column>
          </template>
        </template>
      </el-table>

      <!-- 注释说明区域 -->
<!--      <div class="notes-area">-->
<!--        注：1.合计数中包括院领导受理报捕数、受理公诉数；院领导办结数以及未结数；2.省院九部经与一部和案管办协商沟通，明确目前省院通报的办案时长按照最高检统计口径不包括未检案件；3.审结率：1-[020101（第1行，第220列）-080201（第1行，第189列）]/[020101(第1行，第17列）+（第1行，第19列）-080201（第1行，第14列）-080201（第1行，第16列）]；15天内审结率、45天内审结率：190101（第3行，第1列）中办案时长15天内或45天内的件数/总的办结件数；4.详细明细可以点击数字查看。-->
<!--      </div>-->
    </div>
    <WarningDetailDialog
      :dialog-visible="dialogVisible"
      :active-name="activeName"
      :row-cbrgh="currentRow"
      @close="closeDialog"
    >
    </WarningDetailDialog>

  </div>
</template>

<script>
import * as XLSX from 'sheetjs-style'
import { calculateColWidth, calculateWidth, wsInit } from "@/utils/excelUtils"
import { tableHeader } from "@/views/modules/warningInfo/bakJson"
import { warningDepartmentAndUser, warningPageList } from "@/api/api/warningInfo"
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, WidthType, AlignmentType, BorderStyle } from 'docx';
import { saveAs } from 'file-saver';
import WarningDetailDialog from "@/views/modules/warningInfo/detailDialog.vue"

export default {
  name: "DurationStatistics",
  components: { WarningDetailDialog },
  data() {
    return {
      detailLoading: false,
      DetailDialogVisible: false,
      tableCellStyleType: {
        max: 0,
        min: 0,
        zero: 0
      },
      listQuery: {
        page: 1,
        rows: 5,
        entity: {
          cbrgh: "",
          startTime: "",
          endTime: ""
        }
      },
      currentRow: {},
      loading: true,
      searchForm: {
        timeType: 2, // 1: 单月, 2: 时间段
        singleMonth: "202501",
        monthrange: ["202501", "202502"]
      },
      tableData: [],
      tableHeader: [],
      columns: [],
      dialogVisible: false,
      currentCell: {
        department: "",
        cbr: "",
        columnTitle: "",
        value: ""
      },
      detailTableData: [],
      activeName: "",
      total: 0,
      cols: [
        { label: "部门受案号", prop: "BMSAH" },
        { label: "案件名称", prop: "AJMC" },
        { label: "嫌疑人姓名", prop: "XYRXM" },
        { label: "受理日期", prop: "SLRQ_DATE" },
        { label: "审结日期", prop: "SJRQ_DATE" },
        { label: "承办单位", prop: "CBDW_MC" },
        { label: "承办部门", prop: "CBBM_MC" },
        { label: "承办检察官", prop: "CBR" },
        { label: "承办检察官编码", prop: "CBRGH" },
        { label: "办案时长", prop: "DURATION" }
      ],
      departmentAndUserList: [],
      statisticsData:[]
    };
  },
  watch: {

  },
  async mounted() {
    document.title = "案件质量预警情况";
    // 获取字段
    await this.getTableHeader();
    // 获取部门和人员列表，并按照动态字段进行赋值
    await this.getList();
  },
  methods: {
    async generateAllReports() {
      if (!this.detailTableData || this.detailTableData.length === 0) {
        this.$message.warning('没有可导出的数据');
        return;
      }

      // 创建文档内容数组
      let children = [
        new Paragraph({
          children: [
            new TextRun({
              text: "案件质量预警报告汇总",
              bold: true,
              size: 36
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        })
      ];

      // 遍历所有案件
      for (let i = 0; i < this.detailTableData.length; i++) {
        const row = {
          ...this.detailTableData[i],
          warningDataObj: this.detailTableData[i].warningData ? JSON.parse(this.detailTableData[i].warningData) : []
        };

        // 添加分页符（除了第一页）
        if (i > 0) {
          children.push(
            new Paragraph({
              children: [],
              pageBreakBefore: true
            })
          );
        }

        // 添加案件标题
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: `${i + 1}. ${row.caseTitle || "未命名案件"}`,
                bold: true,
                size: 32
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 300 }
          })
        );

        // 准备基本信息表格数据
        const tableData = [
          ['部门受案号', row.caseCode || '-', '案件名称', row.caseTitle || '-'],
          ['案件类别编码', row.caseTypeCode || '-', '案件类别', row.caseTypeName || '-'],
          ['承办单位编码', row.procuratorateCode || '-', '承办单位', row.procuratorate || '-'],
          ['承办部门编码', row.officeCode || '-', '承办部门', row.office || '-'],
          ['承办人工号', row.procuratorCode || '-', '承办人', row.procurator || '-'],
          ['预警类型', row.warningType == '0' ? '提醒' : '预警', '规则代码', row.warningRuleCode || '-'],
          ['规则名称', row.warningRuleTitle || '-', '预警时间', row.warningTime || '-'],
          ['状态', this.getStateText(row.state), '是否已通知承办人', row.isNotified == 1 ? '是' : '否'],
          ['创建时间', row.createTime || '-', '最后修改时间', row.updateTime || '-'],
          ['处置完成时间', row.archivedTime || '-', '业务类型', row.warningBusinessType || '-'],
        ];

        // 创建基本信息表格行
        const tableRows = tableData.map(row => {
          return new TableRow({
            children: row.map(cell => {
              return new TableCell({
                width: {
                  size: 2500,
                  type: WidthType.DXA,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                children: [new Paragraph({
                  children: [new TextRun({ text: cell, size: 24 })],
                  alignment: AlignmentType.CENTER
                })]
              });
            })
          });
        });

        // 添加基本信息表格
        children.push(
          new Table({
            width: {
              size: 100,
              type: WidthType.PERCENTAGE,
            },
            rows: tableRows
          })
        );

        // 添加预警数据标题
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "预警数据",
                bold: true,
                size: 28
              })
            ],
            spacing: { before: 400, after: 200 }
          })
        );

        // 处理预警数据表格
        if (row.warningDataObj && row.warningDataObj.length > 0) {
          const headerMapping = {
            '姓名': '姓名',
            '一审裁判生效日期（案件）': '一审裁判生效日期\n（案件）',
            '一审裁判生效日期（嫌疑人）': '一审裁判生效日期\n（嫌疑人）',
            '一审宣告刑（主刑）': '一审宣告刑\n（主刑）'
          };

          const headers = Object.keys(headerMapping);

          const warningDataRows = [
            // 表头行
            new TableRow({
              children: headers.map(header =>
                new TableCell({
                  width: {
                    size: Math.floor(100 / headers.length),
                    type: WidthType.PERCENTAGE,
                  },
                  borders: {
                    top: { style: BorderStyle.SINGLE, size: 1 },
                    bottom: { style: BorderStyle.SINGLE, size: 1 },
                    left: { style: BorderStyle.SINGLE, size: 1 },
                    right: { style: BorderStyle.SINGLE, size: 1 },
                  },
                  children: [new Paragraph({
                    children: [new TextRun({
                      text: headerMapping[header],
                      bold: true,
                      size: 24
                    })],
                    alignment: AlignmentType.CENTER
                  })]
                })
              )
            }),
            // 数据行
            ...row.warningDataObj.map(item =>
              new TableRow({
                children: headers.map(header => {
                  let value = item[header];
                  if (value && header.includes('日期')) {
                    value = value.trim().split(' ')[0];
                  }
                  return new TableCell({
                    width: {
                      size: Math.floor(100 / headers.length),
                      type: WidthType.PERCENTAGE,
                    },
                    borders: {
                      top: { style: BorderStyle.SINGLE, size: 1 },
                      bottom: { style: BorderStyle.SINGLE, size: 1 },
                      left: { style: BorderStyle.SINGLE, size: 1 },
                      right: { style: BorderStyle.SINGLE, size: 1 },
                    },
                    children: [new Paragraph({
                      children: [new TextRun({
                        text: value || '/',
                        size: 24
                      })],
                      alignment: AlignmentType.CENTER
                    })]
                  });
                })
              })
            )
          ];

          children.push(
            new Table({
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              rows: warningDataRows
            })
          );
        }
      }

      // 创建文档
      const doc = new Document({
        sections: [{
          properties: {},
          children: children
        }]
      });

      // 生成并下载文档
      try {
        const blob = await Packer.toBlob(doc);
        const today = new Date();
        const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
        saveAs(blob, `案件预警报告汇总_${dateStr}.docx`);
      } catch (error) {
        console.error('生成文档失败:', error);
        this.$message.error('生成文档失败');
      }
    },
    cellStyle({row,column,rowIndex,columnIndex}) {
      if (row[column.property] == "0" && row.department != '总计' && this.tableCellStyleType.zero === 1 ) {
        return { background: '#dfdfdf' }
        // return {}
      }
      if (this.isMax(row[column.property], column.property) && row.department != '总计' && this.tableCellStyleType.max === 1) {
        return {background: '#cd1515',color: '#fff'}
        // return {}
      }
      if (this.isMin(row[column.property], column.property) && row.department != '总计' && this.tableCellStyleType.min === 1) {
        return {background: '#47c51f',color: '#fff'}
        // return {}
      }
      return {}

    },
    downloadDetail() {
      if (!this.detailTableData || this.detailTableData.length === 0) {
        this.$message.warning('没有可导出的数据');
        return;
      }

      // 处理数据（添加办案时长）
      const processedData = this.detailTableData;

      // 准备表头和数据
      const headers = this.cols.map(item => item.label);
      const data = processedData.map(item => {
        return this.cols.map(field => {
          const value = item[field.prop];
          // 处理空值
          if (value === null || value === undefined) return '';
          return value;
        });
      });

      // 合并表头和数据
      const excelData = [headers, ...data];

      // 创建工作表
      const ws = wsInit(excelData, 1)

      // 设置列宽
      ws['!cols'] = this.cols.map(field => ({
        wch: calculateColWidth(processedData, field)
      }));

      // 创建工作簿并导出
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '案件列表');

      // 生成文件名（当前日期）
      const today = new Date();
      const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
      XLSX.writeFile(wb, `案件数据_${dateStr}.xlsx`);
    },
    download() {
      const headerStructure = this.tableHeader
      const departmentData = this.departmentAndUserList
      const cbrghData = this.statisticsData

      const headerRow1 = ['部门', '员额'];
      const headerRow2 = ['部门', '员额'];
      const merges = [
        { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }, // 部门
        { s: { r: 0, c: 1 }, e: { r: 1, c: 1 } }, // 员额
      ];
      let colIndex = 2;

      // 表头处理
      headerStructure.forEach(item => {
        if (item.subTitle && item.subTitle.length) {
          headerRow1.push(item.title);
          for (let i = 0; i < item.subTitle.length -1 ; i++) {
            headerRow1.push('')
          }

          item.subTitle.forEach(sub => {
            headerRow2.push(sub.title);
          });
          merges.push({
            s: { r: 0, c: colIndex },
            e: { r: 0, c: colIndex + item.subTitle.length - 1 },
          });
          colIndex += item.subTitle.length;
        } else {
          headerRow1.push(item.title);
          headerRow2.push('');
          merges.push({
            s: { r: 0, c: colIndex },
            e: { r: 1, c: colIndex },
          });
          colIndex += 1;
        }
      });

      // 数据处理
      const excelData = [];
      departmentData.forEach(dept => {
        dept.personList.forEach((person, idx) => {
          const row = [];
          row.push(idx === 0 ? dept.name : '');
          row.push(person.cbr);
          const personData = cbrghData[person.cbrgh] || [];
          row.push(...personData);
          excelData.push(row);
        });
        // 合并部门列
        if (dept.personList.length > 1) {
          merges.push({
            s: { r: 2 + excelData.length - dept.personList.length, c: 0 },
            e: { r: 2 + excelData.length - 1, c: 0 },
          });
        }
      });

      // 合并所有数据
      const allData = [headerRow1, headerRow2, ...excelData];

      const ws = wsInit(allData,2)

      // 合并设置
      ws['!merges'] = merges;

      ws['!cols'] = headerRow1.map((i,index) => ({
        wch: Math.max(
            calculateWidth(headerRow1[index])
        )
      }));

      ws['!cols'][0] = {wch: 13 }
      ws['!cols'][1] = {wch: 10 }

      // 写入工作簿
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      XLSX.writeFile(wb, '部门员额数据.xlsx');
    },
    async getTableHeader() {
      // 获取字段
      // const { data: tableHeader } = await getTableHeader();
      this.tableHeader = tableHeader;
      this.columns = [];
      this.tableHeader.forEach(item => {
        if (item.subTitle && item.subTitle.length) {
          item.subTitle.forEach(subItem => {
            this.columns.push({
              ...subItem,
              title: item.title + "-" + subItem.title
            });
          });
        } else {
          this.columns.push({
            ...item
          });
        }
      });
    },

    async getList() {
      const data  = await warningDepartmentAndUser()
      this.departmentAndUserList = data.departmentAndUserList
      this.statisticsData = data.statisticsData

      let queryData = {};
      // 时间段
      if (this.searchForm.timeType === 1) {
        queryData = {
          startTime: this.searchForm.singleMonth || '199001',
          endTime: this.searchForm.singleMonth || '209912'
        };
      } else {
        queryData = {
          startTime: this.searchForm.monthrange != null ? this.searchForm.monthrange[0] : '199001',
          endTime: this.searchForm.monthrange != null ? this.searchForm.monthrange[1] : '209912'
        };
      }
      // const { data: statisticsData } = await getStatisticsData(queryData);
      this.tableData = [];
      this.departmentAndUserList.forEach(row => {
        row.personList.forEach(person => {
          const obj = {
            department: row.name,
            cbr: person.cbr,
            cbrgh: person.cbrgh
          };
          // 拿到统计数据
          const statisticsDataItem = this.statisticsData[person.cbrgh];
          // 遍历字段进行赋值
          this.columns.forEach((column, idx) => {
            obj[column.title] = statisticsDataItem[idx];
          });
          this.tableData.push(obj);
        });
      });
      // 添加总计行
      // const totalItem = statisticsData["total"];
      // const totalObj = {
      //   department: "总计",
      //   cbr: "",
      //   cbrgh: ""
      // };
      // this.columns.forEach((column, idx) => {
      //   totalObj[column.title] = totalItem[idx];
      // });
      // this.tableData.push(totalObj);
      this.loading = false;
    },
    getMaxAndMin(prop) {
      const values = this.tableData.filter(i => i.department !== '总计' ).map(item => Number(item[prop]));
      const filter = values.filter(i => i !== 0)
      return {
        max: Math.max(...filter),
        min: Math.min(...filter)
      };
    },
    isMax(value, prop) {
      let max = this.getMaxAndMin(prop).max
      return value == max;
    },
    isMin(value, prop) {
      let min = this.getMaxAndMin(prop).min
      return value == min;
    },
    handleTimeTypeChange() {
      // 切换时间类型时清空已选值
      if (this.searchForm.timeType === 1) {
        this.searchForm.monthrange = ["202501", "202501"];
      } else {
        this.searchForm.singleMonth = "202501";
      }
      this.getList()
    },
    handleSearch() {
      // 实现查询逻辑
      this.getList();
    },
    handleReset() {
      this.searchForm = {
        timeType: 1,
        singleMonth: "202501",
        monthrange: ["202501", "202501"]
      };
      this.tableCellStyleType = {
        max: 0,
        min: 0,
        zero: 0
      }
      this.getList();
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // 处理合计行
      if (rowIndex === this.tableData.length) {
        if (columnIndex === 0) {
          return { rowspan: 1, colspan: 2 };
        }
        if (columnIndex === 1) {
          return { rowspan: 0, colspan: 0 };
        }
      }

      // 原有的部门合并逻辑
      if (columnIndex === 0) {
        const dept = this.tableData[rowIndex].department;
        let count = 1;

        if (rowIndex > 0 && dept === this.tableData[rowIndex - 1].department) {
          return { rowspan: 0, colspan: 0 };
        }

        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].department === dept) {
            count++;
          } else {
            break;
          }
        }

        return { rowspan: count, colspan: 1 };
      }
    },

    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }
        if (index === 1) {
          sums[index] = "";
          return;
        }

        // 计算需要统计的列的合计
        const values = data.map(item => Number(item[column.property]) || 0);
        if (values.every(value => isNaN(value))) {
          sums[index] = "N/A";
        } else {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
        }
      });

      return sums;
    },
    handleCurrentChange(page) {
      this.listQuery.pageNo = page;
      this.getDetailTableData();
    },
    handleSizeChange(size) {
      this.listQuery.pageSize = size;
      this.getDetailTableData();
    },
    async getDetailTableData() {
      this.detailLoading = true;
      const { rows,total } = await warningPageList(
          this.listQuery
      );
      rows.forEach(row=>{
        row.warningDataObj = row.warningData ? JSON.parse(row.warningData) : []
      })
      this.detailTableData = rows;
      this.total = total;
      this.detailLoading = false;
    },
    closeDialog() {
      this.activeName = "";
      this.currentRow = {};
      this.dialogVisible = false;
    },
    handleCellClick(row, column, index) {
      this.currentRow = row
      this.activeName = column.title;
      this.dialogVisible = true;
    },
    getStateText(state) {
      switch(state) {
        case '0': return '待处理';
        case '1': return '待核查';
        case '2': return '已纠正';
        case '3': return '已归档';
        case '4': return '误报';
        default: return '-';
      }
    },
  }
};
</script>

<style lang="scss" scoped>

.full-screen-dialog {
  margin: 0 !important;
  padding: 0 !important;

  /deep/ .el-dialog {
    margin: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
  }

  /deep/ .el-dialog__wrapper {
    overflow: hidden !important;
  }

  .header {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    background: #409EFF;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .iccon-btn-close {
      cursor: pointer;
      font-size: 20px;
    }
  }

  .content-wrapper {
    flex: 1;
    padding: 20px;
    overflow: auto;
    height: calc(100vh - 120px);
  }

  /deep/ .el-dialog__body {
    padding: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
  }

  /deep/ .el-dialog__footer {
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-top: 1px solid #e4e7ed;
  }
}

.duration-statistics {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 16px;

  .filter-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;

    .filter-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-group {
      display: flex;
      gap: 12px;
      flex: 1;

      .wide-select {
        width: 240px;
      }

      .normal-select {
        width: 120px;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 12px;

        .date-picker {
          width: 240px;
        }
      }
    }

    .data-type-switch {
      display: flex;
      gap: 16px;

      .switch-item {
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 4px;
        color: #606266;

        &.active {
          background-color: #e6f2ff;
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .analysis-options {
      display: flex;
      gap: 24px;
      align-items: center;

      .option-item {
        cursor: pointer;
        color: #606266;

        &.active {
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      align-items: center;

      .search-input {
        width: 200px;
      }

      .el-button {
        padding: 8px 16px;
      }
    }
  }

  .table-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;

    .table-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;


      .title-decoration {
        width: 4px;
        height: 20px;
        background-color: #409eff;
        margin-right: 8px;
        border-radius: 2px;
      }

      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin: 0;
      }
    }
  }

  .notes-area {
    margin-top: 16px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    line-height: 24px;
    font-size: 16px;
    color: #4f5e7b;
  }
}

.cell-content {
  display: inline-block;
  width: 100%;
  cursor: pointer;

  //.max-num {
  //  //color: #2487fa;
  //  background: #2487fa;
  //  color: #fff;
  //}

  //.max-num:hover {
  //  font-weight: bold;
  //}


  //.min-num {
  //  //color: red;
  //  background: orange;
  //  color: #fff;
  //}

  //.min-num:hover {
  //  font-weight: bold;
  //}

}
.calculation-rule {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .rule-label {
    color: #606266;
    font-weight: 500;
  }

  .rule-content {
    color: #303133;
    line-height: 1.5;
  }
}

.warning-info {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 16px;
  line-height: 22px;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 30px;
  overflow: auto;
  background-color: #f5f7fa;

  .search-form {

    /deep/ .custom-input {
      .el-input__inner {
        height: 34px;
        border-radius: 3px;
        border-color: #A9C4DF;
        color: #4f5e7b;
        padding: 0 10px;

        &:hover {
          color: #BAC9DF;
        }

        &:focus {
          border-color: #4084F0;
          color: #4F5E7B;
        }
      }

      .el-input__inner::placeholder { /* Standard syntax */
        color: #879BBA;
      }
    }

    /deep/ .custom-select {
      .el-input__inner {
        border-radius: 3px;
        height: 34px;
        border-color: #A9C4DF;
        color: #4f5e7b;
        padding: 0 10px;

        &:hover {
          border-color: #879BBA;
          color: #BAC9DF;
        }

        &:focus {
          border-color: #4084F0;
          color: #4F5E7B;
        }


      }

      .el-input__inner::placeholder { /* Standard syntax */
        color: #879BBA;
      }

      .el-select-dropdown__item{
        padding: 0 6px;

        &:hover {
          color: #217fdc;
          background-color: #c4dcf4;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .detail-content {
    line-height: 22px;
    // background-color: #F3F6F8
  }
}
.dialog-wrapper {

  .dialog-header {
    height: 40px;
    padding-left: 15px;
    padding-right: 0;
    background-color: #4084f0;
    line-height: 40px;
    font-weight: normal;

    .el-icon-close {
      font-size: 12px;
      line-height: 40px;
      width: 40px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #F5B923;
      }
    }
  }
  .dialog-container {
    padding: 15px;
  }
}
/* 定义自定义遮罩层样式 */
.custom-overlay {
  background-color: #000000;
  opacity: 0.6;
}

/deep/ .el-table__header-wrapper {
  min-height: 40px;
  color: #2D405e;
  font-weight: bold;
  border-top: #A9C4DF 1px solid;
  border-bottom: #A9C4DF 1px solid;

  .cell {
    font-size: 16px;
  }
}

/deep/  .el-table__body tr:hover > td {
  background-color: #fffdec !important; /* 悬停时的背景颜色 */
}

/deep/ .el-table__body-wrapper {
  .el-table__row {
    border-bottom: #A9C4DF 1px solid;
    cursor: pointer;
  }


  /* 覆盖默认的斑马纹样式 */
  .el-table__row:nth-child(odd) {
    background-color: #f3f6fb; /* 奇数行背景颜色 */
  }

  .el-table__row:nth-child(even) {
    background-color: white; /* 偶数行背景颜色 */
  }

  .cell {
    font-size: 16px;
  }
}

/deep/ .el-tag {
  color: #ffffff;
  padding: 2px;
  font-size: 12px;
  line-height: 12px;
  height: 18px;
  font-family: 'SimSun', '\5B8B\4F53', sans-serif;
  border-radius: 2px;
}

/deep/ .el-tag--primary {
  background-color: #1b9cff;
}

/deep/ .el-tag--success {
  background-color: #1dc5b3;
}

/deep/ .el-tag--danger {
  background-color: #f36859;
}

.page-btn {
  color: white;
  padding: 5px 10px;
  background-color: #c9cfd7;
  min-width: 50px !important;
  height: 27px;
  margin-top: 2px;
}


//   分页样式覆盖
//::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active{
//  background: #4084F0 !important;
//
//}
//::v-deep .el-pagination.is-background .el-pager li:not(.disabled):hover{
//  color: #4084F0 !important;
//  border: 1px solid #4084F0 !important;
//  background-color: #EBF4FE !important;
//}
//::v-deep .number,::v-deep .el-pagination.is-background .btn-prev , ::v-deep .el-pagination.is-background .btn-next, .el-pagination.is-background .el-pager li{
//  background-color: #fff !important;
//  border: 1px solid #1B9CFF !important;
//}
//::v-deep .el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .btn-next:disabled{
//  background-color: #c9d0d8 !important;
//  color:#fff !important;
//  border: none !important;
//}
//::v-deep .el-pagination.is-background .el-pager li {
//  background-color: #fff ;
//}

</style>
