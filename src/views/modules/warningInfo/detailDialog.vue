<template>
  <div>
    <!-- 详情-->
    <el-dialog
        :visible.sync="dialogVisible"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
        :show-close="false"
        :fullscreen="true"
        custom-class="full-screen-dialog"
        @close="closeDialog"
    >
      <div class="header">
        <span>{{activeName}}详情</span>
        <i class="el-icon-close iccon-btn-close" @click="closeDialog" />
      </div>
      <div class="content-wrapper">
        <!--        <div class="calculation-rule">-->
        <!--          <span class="rule-label">计算规则：</span>-->
        <!--          <span class="rule-content">{{ currentCell.column.formula }}</span>-->
        <!--        </div>-->
<!--        <div>-->
<!--          <el-tabs v-model="activeName">-->
<!--            <el-tab-pane-->
<!--                :label="activeName"-->
<!--                :name="activeName"-->
<!--            />-->
<!--          </el-tabs>-->
          <el-table
              :data="detailTableData"
              style="width: 100%;"
              v-loading="detailLoading"
              @row-click="handleRowClick"
              ref="table"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <div class="expand-detail">
                  <el-table
                    :data="props.row.children || []"
                    style="width: 100%"
                    border
                    :ref="'InnerTable' + props.row.warningRuleCode"
                    :reserve-selection="true"
                    @row-click="handleChildrenRowClick"
                  >
                    <el-table-column prop="warningRuleTitle" label="规则名称" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="warningType" label="预警类型" width="120" align="center">
                      <template slot-scope="scope">
                        <el-tag v-if="scope.row.warningType == '0'" type="primary" effect="plain">提醒</el-tag>
                        <el-tag v-else-if="scope.row.warningType == '1'" type="danger" effect="plain">预警</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="warningDataNum" width="130" label="预警状态">
                      <template slot-scope="scope">
                        <el-tag v-show="scope.row.state == '0'" type="primary">待处理</el-tag>
                        <el-tag v-show="scope.row.state == '1'" type="primary">待核查</el-tag>
                        <el-tag v-show="scope.row.state == '2'" type="success">已纠正</el-tag>
                        <el-tag v-show="scope.row.state == '3'" type="success">已归档</el-tag>
                        <el-tag v-show="scope.row.state == '4'" type="success">误报</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="warningData" label="预警数据">
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" placement="top">
                          <div slot="content" v-if="scope.row.warningDataObj && scope.row.warningDataObj.length > 0">
                            <div v-for="item in scope.row.warningDataObj" :key="JSON.stringify(item)" style="padding: 4px 8px; border-bottom: 1px solid #eee;">
                              <div v-for="(value, key) in item" :key="key">
                                <span>{{ key }}: {{ value || '/' }}</span>
                              </div>
                            </div>
                          </div>
                          <div v-if="scope.row.warningDataObj && scope.row.warningDataObj.length > 0">
                            <div v-for="(value, key) in scope.row.warningDataObj[0]" :key="key">
                              <span>{{ key }}: {{ value || '/' }}</span>
                            </div>
                          </div>
                        </el-tooltip>
                        <div v-if="scope.row.warningDataObj && scope.row.warningDataObj.length > 1">...</div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="warningTime" label="预警时间" width="180"></el-table-column>
                  </el-table>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="caseCode" label="部门受案号" width="300" show-overflow-tooltip></el-table-column>
            <el-table-column prop="caseTitle" label="案件名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="caseTypeName" label="案件类别" width="150" show-overflow-tooltip></el-table-column>
<!--            <el-table-column prop="procuratorate" label="承办单位" width="250"></el-table-column>-->
            <el-table-column prop="office" label="承办部门" width="150"></el-table-column>
            <el-table-column prop="procurator" label="承办人" width="100"></el-table-column>
            <el-table-column prop="state" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state == '已纠正'" type="success">已纠正</el-tag>
                <el-tag v-else-if="scope.row.state == '待处理'" type="primary">待处理</el-tag>
                <el-tag v-else-if="scope.row.state == '部分纠正'" type="primary">部分纠正</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="count" label="规则数量" width="100"></el-table-column>
<!--            <el-table-column prop="warningRuleTitle" label="规则名称" show-overflow-tooltip></el-table-column>-->
<!--            <el-table-column prop="warningType" label="预警类型" width="120" align="center">-->
<!--              <template slot-scope="scope">-->
<!--                <el-tag v-if="scope.row.warningType == '0'" type="primary" effect="plain">提醒</el-tag>-->
<!--                <el-tag v-else-if="scope.row.warningType == '1'" type="danger" effect="plain">预警</el-tag>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column prop="warningDataNum" width="130" label="预警数量"></el-table-column>-->
<!--            <el-table-column prop="warningData" label="预警数据" width="400">-->
<!--              <template slot-scope="scope">-->
<!--                <el-tooltip class="item" effect="dark" placement="top">-->
<!--                  <div slot="content" v-if="scope.row.warningDataObj.length > 0">-->
<!--                    <div v-for="item in scope.row.warningDataObj"  style="padding: 4px 8px; border-bottom: 1px solid #eee;">-->
<!--                      <div v-for="(value, key) in item" :key="key">-->
<!--                        <span>{{ key }}: {{ value || '/' }}</span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                  <div v-if="scope.row.warningDataObj.length > 0">-->
<!--                    <div v-for="(value, key) in scope.row.warningDataObj[0]">-->
<!--                      <span>{{ key }}: {{ value || '/' }}</span>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </el-tooltip>-->
<!--                <div v-if="scope.row.warningDataObj.length > 1">...</div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column prop="warningTime" label="预警时间" width="180"></el-table-column>-->
            <el-table-column label="操作" align="center" width="260">
              <template slot-scope="scope">
                <el-button @click.stop="generateWordReport(scope.row)">生成质量评查报告（个案）</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container" style="display: flex;justify-content: flex-end;">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="listQuery.pageNo"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="listQuery.pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
          </div>
<!--        </div>-->
      </div>
      <span slot="footer" class="dialog-footer">
<!--        <el-button @click="closeDialog" type="primary">确定</el-button>-->
        <el-button @click="closeDialog">关闭</el-button>
        <el-button type="primary" @click="generateAllReports">生成质量评查报告（个人）</el-button>
      </span>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog title="案件详情" :visible.sync="DetailDialogVisible" :modal-class="'custom-overlay'" top="15px" width="70%" :before-close="handleDetailClose" append-to-body custom-class="case-detail-dialog">
      <div class="dialog-wrapper">
        <div class="dialog-header">
          <span>案件详情</span>
          <i class="el-icon-close" @click="handleDetailClose" />
        </div>
        <div class="dialog-container">
          <el-descriptions
              :column="2"
              border
              class="warning-detail-form"
              :labelStyle="{ width: '200px', padding: '6px 10px', textAlign: 'right',backgroundColor: 'white',fontSize: '16px' }"
              :contentStyle="{ padding: '6px', color: '#5c79b0',fontSize: '16px' }"
          >
            <el-descriptions-item label="部门受案号">
              <div class="detail-content"> {{ currentRow.caseCode }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="案件名称">
              <div class="detail-content">{{ currentRow.caseTitle }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="案件类别编码">
              <div class="detail-content">{{ currentRow.caseTypeCode }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="案件类别">
              <div class="detail-content">{{ currentRow.caseTypeName }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="承办单位编码">
              <div class="detail-content">{{ currentRow.procuratorateCode }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办单位">
              <div class="detail-content">{{ currentRow.procuratorate }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="承办部门编码">
              <div class="detail-content">{{ currentRow.officeCode }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办部门">
              <div class="detail-content">{{ currentRow.office }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="承办人工号">
              <div class="detail-content">{{ currentRow.procuratorCode }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="承办人">
              <div class="detail-content">{{ currentRow.procurator }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="预警类型">
              <div class="detail-content">
                <el-tag v-if="currentRow.warningType == '0'" type="primary" effect="plain">提醒</el-tag>
                <el-tag v-if="currentRow.warningType == '1'" type="danger" effect="plain">预警</el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="规则代码">
              <div class="detail-content">{{ currentRow.warningRuleCode }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="规则名称">
              <div class="detail-content">{{ currentRow.warningRuleTitle }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="预警时间">
              <div class="detail-content">{{ currentRow.warningTime }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="办案团队名称">
              <div class="detail-content">{{ currentRow.batdmc }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="办案团队编码">
              <div class="detail-content">{{ currentRow.batdbm }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="状态">
              <div class="detail-content">
                <el-tag v-if="currentRow.state == '0'" type="primary">待处理</el-tag>
                <el-tag v-if="currentRow.state == '1'" type="primary">待核查</el-tag>
                <el-tag v-if="currentRow.state == '2'" type="success">已纠正</el-tag>
                <el-tag v-if="currentRow.state == '3'" type="success">已归档</el-tag>
                <el-tag v-if="currentRow.state == '4'" type="danger">误报</el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="是否已通知承办人">
              <div class="detail-content">{{ currentRow.isNotified == 1 ? '是' : '否' }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="创建时间">
              <div class="detail-content">{{ currentRow.createTime }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="最后修改时间">
              <div class="detail-content">{{ currentRow.updateTime }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="处置完成时间">
              <div class="detail-content">{{ currentRow.archivedTime }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="预警数量">
              <div class="detail-content">{{ currentRow.warningDataNum }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="预警数据" :span="2">
              <div class="detail-content warning-data-container">
                <div v-for="item in currentRow.warningDataObj">
                  <div v-for="(value, key) in item">
                    <span>{{ key }}: {{ value }}</span>
                  </div>
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { warningCaseDetail, warningGroupRule, warninglistByFilter, warningPageList } from "@/api/api/warningInfo"
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, WidthType, AlignmentType, BorderStyle, VerticalAlign, Indent, VerticalMerge, PageBreakBefore, HeadingLevel } from 'docx';
import { saveAs } from 'file-saver';
import * as docx from "docx"
import { listCbrpcajqd } from "@/api/api/qualityEvaluation"

export default {
  name: "warningDetailDialog",
  props: {
    warningType:{
      type: String,
      default: ""
    },
    dialogVisible:{
      type: Boolean,
      default: false
    },
    activeName: {
      type: String,
      default: ""
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      detailLoading: false,
      DetailDialogVisible: false,
      tableCellStyleType: {
        max: 0,
        min: 0,
        zero: 0
      },
      listQuery: {
        page: 1,
        rows: 5,
        entity: {
          cbrgh: "",
          startTime: "",
          endTime: ""
        }
      },
      currentRow: {},
      loading: true,
      tableData: [],
      tableHeader: [],
      columns: [],
      currentCell: {
        department: "",
        cbr: "",
        columnTitle: "",
        value: ""
      },
      detailTableData: [],
      total: 0,
    };
  },
  watch: {
    activeName(activeName) {
      if (activeName) {
        this.listQuery = {
          pageNo: 1,
          pageSize: 10,
          sort: 'warningDataNum,warningTime',
          order: 'DESC,DESC',
          filters: {
            type: this.warningType,
            cbrgh: this.rowData.cbrgh,
            endTime: this.rowData.endTime,
            startTime: this.rowData.startTime,
          },
          searchFilters: {}
        };
        // 获取详情数据
        this.getDetailTableData();
      }
    }
  },
  async mounted() {

  },
  methods: {
    handleDetailClose() {
      this.currentRow = {}
      this.DetailDialogVisible = false
    },
    handleChildrenRowClick(row){
      this.currentRow = row
      this.DetailDialogVisible = true
    },
    async handleRowClick(row) {
      // 获取当前行的展开状态
      const expandedRows = this.$refs.table.store.states.expandRows;
      const isExpanded = expandedRows.includes(row);

      row.children.forEach(d=>{
        d.warningDataObj = d.warningData ? JSON.parse(d.warningData) : []
      })

      if (!isExpanded) {
        // 然后展开行
        this.$refs.table.toggleRowExpansion(row, true);
      } else {
        // 如果是收起，直接收起即可
        this.$refs.table.toggleRowExpansion(row, false);
      }
    },
    async generateAllReports() {
      const data = this.rowData.alData;
      let str = `经系统筛查，${data.cbr || ''}今年共受理审查逮捕案件${data['受理报捕件/人-件'] || '0'}件${data['受理报捕件/人-人'] || '0'}人，` +
          `审查起诉案件${data['受理报捕件/人-件'] || '0'}件${data['受理公诉件/人-人'] || '0'}人，` +
          `已办结${data['办结公诉件数'] || '0'}件，未办结${data['未结公诉件数'] || '0'}件，` +
          `平均办案时长${data['平均办案时长（天数）'] || '0'}天，` +
          `15天审结率为${data['15天内审结率'] || '0'}，45天审结率为${data['45天内审结率'] || '0'}，` +
          `监督立案数${data['监督立案数'] || '0'}，监督撤案数${data['监督撤案数'] || '0'}件，` +
          `追捕追诉${data['追捕、追诉数量'] || '0'}人，提起抗诉${data['刑事抗诉数（再审抗诉、二审抗诉）'] || '0'}件，`;
      const qualityLevels = [
        { key: '案件质量评查等次-优秀', label: '优秀案件' },
        { key: '案件质量评查等次-合格', label: '合格案件' },
        { key: '案件质量评查等次-瑕疵', label: '瑕疵案件' },
        { key: '案件质量评查等次-不合格', label: '不合格案件' }
      ];

      const qualityResults = qualityLevels
          .filter(level => data[level.key] && data[level.key] !== '0')
          .map(level => `${level.label}${data[level.key]}个`);

      if (qualityResults.length > 0) {
        str += qualityResults.join('，') + '，';
      }
      const rows = await warninglistByFilter(this.listQuery);
      str = str + '发生案件不规范被平台检测' + rows.length + '次。具体情况如下表：';

      // 生成docx文档
      const { Document, Paragraph, TextRun, Table, TableRow, TableCell, AlignmentType, HeadingLevel } = docx;

      const tableRows = [];
      let prevCaseTitle = null;
      let mergeStartIndex = -1;

      // 表头 - 使用仿宋_GB2312 三号字
      tableRows.push(
          new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: "案件名称",
                    font: "仿宋_GB2312",
                    size: 32
                  })],
                  alignment: AlignmentType.CENTER
                })],
                width: { size: 35, type: "pct" }
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: "规则名称",
                    font: "仿宋_GB2312",
                    size: 32
                  })],
                  alignment: AlignmentType.CENTER
                })],
                width: { size: 45, type: "pct" }
              }),
              new TableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: "是否已纠正",
                    font: "仿宋_GB2312",
                    size: 32
                  })],
                  alignment: AlignmentType.CENTER
                })],
                width: { size: 20, type: "pct" }
              })
            ]
          })
      );

      // 先处理数据，找出需要合并的行范围
      const mergeRanges = [];
      let currentMergeStart = -1;

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        if (i === 0 || row.caseTitle !== rows[i-1].caseTitle) {
          if (currentMergeStart !== -1 && i - currentMergeStart > 1) {
            mergeRanges.push({ start: currentMergeStart, end: i-1 });
          }
          currentMergeStart = i;
        }
      }

      // 处理最后一组
      if (currentMergeStart !== -1 && rows.length - currentMergeStart > 1) {
        mergeRanges.push({ start: currentMergeStart, end: rows.length-1 });
      }

      // 创建表格行
      rows.forEach((row, index) => {
        const isFirstInMerge = mergeRanges.some(range => range.start === index);
        const isInMerge = mergeRanges.some(range => index > range.start && index <= range.end);

        tableRows.push(
            new TableRow({
              children: [
                new TableCell({
                  children: [new Paragraph({
                    children: [new TextRun({
                      text: isInMerge ? "" : row.caseTitle,
                      font: "仿宋_GB2312",
                      size: 28
                    })]
                  })],
                  verticalMerge: isFirstInMerge ? "restart" : (isInMerge ? "continue" : undefined)
                }),
                new TableCell({
                  children: [new Paragraph({
                    children: [new TextRun({
                      text: row.warningRuleTitle,
                      font: "仿宋_GB2312",
                      size: 28
                    })]
                  })]
                }),
                new TableCell({
                  children: [new Paragraph({
                    children: [new TextRun({
                      text: parseInt(row.state) > 1 ? '是' : '否',
                      font: "仿宋_GB2312",
                      size: 28
                    })]
                  })]
                })
              ]
            })
        );
      });

      // 创建文档
      const doc = new Document({
        sections: [{
          properties: {},
          children: [
            // 标题
            new Paragraph({
              children: [
                new TextRun({
                  text: data.cbr + '所办案件评查报告',
                  font: '黑体', // 黑体
                  size: 44 // 二号字体大小 (22pt * 2)
                })
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 400, } // 段后间距
            }),

            // 第一段文字
            new Paragraph({
              children: [
                new TextRun({
                  text: str,
                  font: "宋体",
                  size: 28 // 四号字大约对应24磅
                })
              ],
              indent: { firstLine: 480 }, // 首行缩进2字符（约480磅）
              spacing: {
                after: 200,
                line: 360
              }
            }),

            // 表格
            new Table({
              rows: tableRows,
              ...window.globalConfig.tabeTest
            })
          ]
        }]
      });

      // 生成并下载文档
      const blob = await docx.Packer.toBlob(doc);
      saveAs(blob, data.cbr + '所办案件评查报告.docx');
    },
    generateWordReport(row) {
      const loading = this.$loading({
        lock: true,
        text: '生成中，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      listCbrpcajqd({
        dwbm:window.globalConfig.qualityEvaluation.dwbm,
        cbrgh:this.rowData.cbrgh,
        bmsah: row.caseCode
      }).then(r=>{
        let rate = ""
        if (r.data != null && r.data.length > 0) {
          rate = r.data[0].pcjlmc
        }

        warningCaseDetail({
          caseCode: row.caseCode,
          caseTitle: row.caseTitle,
          children: JSON.stringify(row.children),
          rate: rate
        }).then(res=>{
          this.download(res,row.caseTitle + '评查报告.docx')
          loading.close()
        }).catch(err=>{
          console.log(err)
          loading.close()
        })
      }).catch(err=>{
        console.log(err)
        loading.close()
      })

    },

    download(blob, name) {
      const link = document.createElement("a"); //创建一个a标签
      const url = window.URL.createObjectURL(blob); //将blob文件对象通过URL.createObjectURL()方法转为为url
      link.href = url; //为a标签设置href属性，并赋值为url
      link.download = name; //定义下载的文件名，文件名要包含后缀哟！如'导出EXCEL.xlsx'
      document.body.appendChild(link); //把a标签放在body上
      link.click(); //出发a标签点击下载
      document.body.removeChild(link); //在body中移除这个a标签
      window.URL.revokeObjectURL(url); //释放blob对象
    },
    async generateDocx(data,type) {
      let oneFlag = type === 'single'
      if (!data || data.length === 0) {
        this.$message.warning('没有可导出的数据');
        return;
      }
      let caseTitle = ""
      let cbrName = "" // 假设单个报告也需要承办人姓名
      if (oneFlag) {
        caseTitle = data[0].caseTitle
        cbrName = data[0].procurator || '未知承办人' // 从单个案件数据获取承办人
      } else {
         // 汇总报告时，承办人信息应从外部传入或在调用处获取
         cbrName = this.rowData.cbrgh || '未知承办人' // 示例：从rowData获取
      }


      // 创建文档内容数组
      let children = [
        new Paragraph({
          children: [
            new TextRun({
              text: oneFlag ? `${cbrName}-${caseTitle}质量预警报告` : `${cbrName}案件质量预警报告汇总`, // 调整标题
              bold: true,
              font: '黑体', // 统一字体
              size: 36 // 调整字号，例如三号 (16pt * 2 = 32) 或 小二 (18pt * 2 = 36)
            })
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        })
      ];

       // --- generateDocx 的其余部分代码保持不变 ---
        // 遍历所有案件
      for (let i = 0; i < data.length; i++) {
        const row = {
          ...data[i],
          warningDataObj: data[i].warningData ? JSON.parse(data[i].warningData) : []
        };

        // 添加分页符（除了第一页）
        if (i > 0 && !oneFlag) { // 汇总报告时才加分页符
          children.push(
              new Paragraph({
                children: [],
                pageBreakBefore: true
              })
          );
        }

        // 添加案件标题 (调整字体大小和居中)
        children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: oneFlag ? `${i + 1}. ${row.warningRuleTitle || "未命名规则"}` : `${i + 1}. ${row.caseTitle || "未命名案件"} - ${row.warningRuleTitle || "未命名规则"}`, // 汇总报告显示案件和规则名
                  bold: true,
                  font: '宋体', // 统一字体
                  size: 32 // 调整字号，例如 四号 (14pt * 2 = 28) 或 小三 (15pt * 2 = 30)
                })
              ],
              alignment: oneFlag ? AlignmentType.LEFT : AlignmentType.CENTER, // 单个报告标题左对齐，汇总报告居中
              spacing: { after: 300 }
            })
        );

        // 准备基本信息表格数据 (统一字体和大小)
        const tableData = [
          ['部门受案号', row.caseCode || '-', '案件名称', row.caseTitle || '-'],
          ['案件类别编码', row.caseTypeCode || '-', '案件类别', row.caseTypeName || '-'],
          ['承办单位编码', row.procuratorateCode || '-', '承办单位', row.procuratorate || '-'],
          ['承办部门编码', row.officeCode || '-', '承办部门', row.office || '-'],
          ['承办人工号', row.procuratorCode || '-', '承办人', row.procurator || '-'],
          ['预警类型', row.warningType == '0' ? '提醒' : '预警', '规则代码', row.warningRuleCode || '-'],
          ['状态', this.getStateText(row.state), '是否已通知承办人', row.isNotified == 1 ? '是' : '否'],
          ['创建时间', row.createTime || '-', '最后修改时间', row.updateTime || '-'],
          ['处置完成时间', row.archivedTime || '-', '预警时间', row.warningTime || '-'],
          ['规则名称', row.warningRuleTitle || '-', '预警数量', row.warningDataNum || '-'],
        ];

        // 处理预警数据，确保换行和字体
        if (row.warningDataObj && row.warningDataObj.length > 0) {
          let warningDataParagraphs = [];
          row.warningDataObj.forEach((item, index) => {
             Object.entries(item).forEach(([key, value], i) => {
               warningDataParagraphs.push(new Paragraph({
                 children: [new TextRun({ text: `${key}: ${value || '/'}`, font: '宋体', size: 24 })], // 四号 (12pt * 2)
                 spacing: { before: 50, after: 50 }
               }));
             });
             // 在每个 item 后添加一个空段落作为分隔（可选）
             if (index < row.warningDataObj.length - 1) {
                warningDataParagraphs.push(new Paragraph("")); // 添加空行分隔
             }
          });
           tableData.push(['预警数据', { paragraphs: warningDataParagraphs, colSpan: 3 }]);
        } else {
           tableData.push(['预警数据', { paragraphs: [new Paragraph({ children: [new TextRun({ text: '-', font: '宋体', size: 24 })] })], colSpan: 3 }]);
        }


        // 创建表格单元格 (统一字体、大小、边框、对齐)
        const createTableCell = (content, options = {}) => {
          const { width = 2375, colSpan = 1, isHeader = false } = options; // 默认宽度调整为 9500 / 4

          let cellChildren = [];
          if (typeof content === 'object' && content.paragraphs) {
            // 处理预警数据段落
            cellChildren = content.paragraphs;
          } else {
            // 普通文本
            cellChildren = [new Paragraph({
              children: [new TextRun({ text: content, font: '宋体', size: 24, bold: isHeader })], // 四号 (12pt * 2)
              alignment: AlignmentType.LEFT // 默认左对齐
            })];
          }

          return new TableCell({
            width: { size: width * colSpan, type: WidthType.DXA }, // 考虑 colSpan
            columnSpan: colSpan,
            borders: { // 标准单线边框
              top: { style: BorderStyle.SINGLE, size: 1, color: "auto" },
              bottom: { style: BorderStyle.SINGLE, size: 1, color: "auto" },
              left: { style: BorderStyle.SINGLE, size: 1, color: "auto" },
              right: { style: BorderStyle.SINGLE, size: 1, color: "auto" },
            },
            verticalAlign: VerticalAlign.CENTER, // 垂直居中
            children: cellChildren
          });
        };


        // 创建表格行 (奇偶行背景色在docx中较复杂，通常省略)
        const detailTableRows = tableData.map((rowData, rowIndex) => {
           const cells = [];
           let currentCellIndex = 0;
           while (currentCellIndex < 4) { // 总共4列
              const cellData = rowData[currentCellIndex];
              if (typeof cellData === 'object' && cellData.colSpan) {
                  cells.push(createTableCell(cellData, { width: 2375, colSpan: cellData.colSpan }));
                  currentCellIndex += cellData.colSpan;
              } else {
                  // 判断是否为标签列（奇数索引）
                  const isLabelCell = currentCellIndex % 2 === 0;
                  cells.push(createTableCell(cellData, { width: 2375, isHeader: isLabelCell })); // 标签列加粗
                  currentCellIndex++;
              }
           }
           return new TableRow({ children: cells });
        });


        // 添加表格到文档
        children.push(
            new Table({
              width: { size: 9500, type: WidthType.DXA }, // 4 * 2375
              rows: detailTableRows,
              alignment: AlignmentType.CENTER // 表格居中
            })
        );
      }
      // --- generateDocx 的其余部分代码保持不变 ---

       // 创建文档
      const doc = new Document({
        sections: [{
          properties: {},
          children: children
        }]
      });

       // 生成并下载文档
      try {
        const blob = await Packer.toBlob(doc);
        const today = new Date();
        const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
        const filename = oneFlag
          ? `${cbrName}-${caseTitle}-质量预警报告-${dateStr}.docx`
          : `${cbrName}案件质量预警报告汇总-${dateStr}.docx`;
        saveAs(blob, filename);
        this.$message.success('报告生成成功');
      } catch (error) {
        console.error('生成文档失败:', error);
        this.$message.error('生成文档失败');
      }
    },
    handleCurrentChange(page) {
      this.listQuery.pageNo = page;
      this.getDetailTableData();
    },
    handleSizeChange(size) {
      this.listQuery.pageSize = size;
      this.getDetailTableData();
    },
    async getDetailTableData() {
      this.detailLoading = true;
      const { rows,total } = await warningGroupRule(
          this.listQuery
      );
      this.detailTableData = []
      this.detailTableData = rows;
      this.total = total;
      this.detailLoading = false;
    },
    closeDialog() {
      this.$emit('close')
    },
    getStateText(state) {
      switch(state) {
        case '0': return '待处理';
        case '1': return '待核查';
        case '2': return '已纠正';
        case '3': return '已归档';
        case '4': return '误报';
        default: return '-';
      }
    },
  }
};

</script>
<style scoped lang="scss">

.full-screen-dialog {
  margin: 0 !important;
  padding: 0 !important;

  /deep/ .el-dialog {
    margin: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
  }

  /deep/ .el-dialog__wrapper {
    overflow: hidden !important;
  }

  .header {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    background: #409EFF;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .iccon-btn-close {
      cursor: pointer;
      font-size: 20px;
    }
  }

  .content-wrapper {
    flex: 1;
    padding: 20px;
    overflow: auto;
    height: calc(100vh - 120px);
  }

  /deep/ .el-dialog__body {
    padding: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
  }

  /deep/ .el-dialog__footer {
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-top: 1px solid #e4e7ed;
  }
}
.warning-info {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 16px;
  line-height: 22px;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 30px;
  overflow: auto;
  background-color: #f5f7fa;

  .search-form {

    /deep/ .custom-input {
      .el-input__inner {
        height: 34px;
        border-radius: 3px;
        border-color: #A9C4DF;
        color: #4f5e7b;
        padding: 0 10px;

        &:hover {
          color: #BAC9DF;
        }

        &:focus {
          border-color: #4084F0;
          color: #4F5E7B;
        }
      }

      .el-input__inner::placeholder { /* Standard syntax */
        color: #879BBA;
      }
    }

    /deep/ .custom-select {
      .el-input__inner {
        border-radius: 3px;
        height: 34px;
        border-color: #A9C4DF;
        color: #4f5e7b;
        padding: 0 10px;

        &:hover {
          border-color: #879BBA;
          color: #BAC9DF;
        }

        &:focus {
          border-color: #4084F0;
          color: #4F5E7B;
        }


      }

      .el-input__inner::placeholder { /* Standard syntax */
        color: #879BBA;
      }

      .el-select-dropdown__item{
        padding: 0 6px;

        &:hover {
          color: #217fdc;
          background-color: #c4dcf4;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .detail-content {
    line-height: 22px;
    // background-color: #F3F6F8
  }
}
.dialog-wrapper {

  .dialog-header {
    height: 40px;
    padding-left: 15px;
    padding-right: 0;
    background-color: #4084f0;
    line-height: 40px;
    font-weight: normal;

    .el-icon-close {
      font-size: 12px;
      line-height: 40px;
      width: 40px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #F5B923;
      }
    }
  }
  .dialog-container {
    padding: 15px;
  }
}
/* 定义自定义遮罩层样式 */
.custom-overlay {
  background-color: #000000;
  opacity: 0.6;
}

/deep/ .el-table__header-wrapper {
  min-height: 40px;
  color: #2D405e;
  font-weight: bold;
  border-top: #A9C4DF 1px solid;
  border-bottom: #A9C4DF 1px solid;

  .cell {
    font-size: 16px;
  }
}

/deep/  .el-table__body tr:hover > td {
  background-color: #fffdec !important; /* 悬停时的背景颜色 */
}

/deep/ .el-table__body-wrapper {
  .el-table__row {
    border-bottom: #A9C4DF 1px solid;
    cursor: pointer;
  }


  /* 覆盖默认的斑马纹样式 */
  .el-table__row:nth-child(odd) {
    background-color: #f3f6fb; /* 奇数行背景颜色 */
  }

  .el-table__row:nth-child(even) {
    background-color: white; /* 偶数行背景颜色 */
  }

  .cell {
    font-size: 16px;
  }
}

/deep/ .el-tag {
  color: #ffffff;
  padding: 2px;
  font-size: 12px;
  line-height: 12px;
  height: 18px;
  font-family: 'SimSun', '\5B8B\4F53', sans-serif;
  border-radius: 2px;
}

/deep/ .el-tag--primary {
  background-color: #1b9cff;
}

/deep/ .el-tag--success {
  background-color: #1dc5b3;
}

/deep/ .el-tag--danger {
  background-color: #f36859;
}

.page-btn {
  color: white;
  padding: 5px 10px;
  background-color: #c9cfd7;
  min-width: 50px !important;
  height: 27px;
  margin-top: 2px;
}

/deep/ .case-detail-dialog {
  margin-top: 2vh !important;
  margin-bottom: 2vh !important;

  .el-dialog {
    height: 96vh !important;
    margin: 0 auto !important;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0 !important;
  }
}

.dialog-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .dialog-header {
    // ... existing code ...
  }

  .dialog-container {
    flex: 1;
    padding: 15px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #909399;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #F5F7FA;
    }
  }
}

.warning-data-container {
  max-height: 400px;  // 增加预警数据容器的高度
  min-height: 300px;  // 增加预警数据容器的高度
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #F5F7FA;
  }
}

/deep/ .el-table__expand-icon {
  display: none !important;
}

.expand-detail {
  padding: 20px 40px;
  background: #f8f9fb;

  .label-cell {
    color: #606266;
    font-weight: 500;
    padding: 12px;
    background: #f5f7fa;
  }

  .value-cell {
    padding: 12px;
    color: #5c79b0;
  }
}

/deep/ .el-table__expanded-cell {
  padding: 0;
}

/deep/ .el-table--border {
  border-radius: 4px;
  overflow: hidden;
}

</style>
