<template>
  <div class="container">
    <el-card class="box-card">
      <h2 style="text-align: center;">刑期计算器</h2>
      <el-form :model="form" ref="form" label-width="150px" @submit.native.prevent="submitForm">
        <div v-for="(item, index) in form.qzcsqk" :key="index">
          <el-row :gutter="20">
            <el-col :span="11">
              <el-form-item :label="index === 0 ? '强制措施:' : ''" :prop="'qzcsqk.' + index + '.qzcs'">
                <el-select v-model="item.qzcs" filterable placeholder="请选择强制措施">
                  <el-option v-for="option in options" :key="option" :label="option" :value="option"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item :label="index === 0 ? '确认日期:' : ''" :prop="'qzcsqk.' + index + '.qzcscqrq'">
                <el-date-picker type="date" placeholder="选择日期" v-model="item.qzcscqrq" style="width: 100%;"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="2" style="display: flex; align-items: center;">
              <el-button type="danger" icon="el-icon-delete" circle @click="removeQzcs(index)" v-if="form.qzcsqk.length > 1"></el-button>
            </el-col>
          </el-row>
        </div>

        <el-form-item>
          <el-button type="primary" icon="el-icon-plus" @click="addQzcs">添加强制措施</el-button>
        </el-form-item>

        <el-form-item label="刑期开始日期:" prop="xqksrq">
          <el-date-picker type="date" placeholder="选择日期" v-model="form.xqksrq" style="width: 100%;"></el-date-picker>
        </el-form-item>

        <el-form-item>
          <div style="text-align: right;">
            <el-button @click="resetForm" plain>重置</el-button>
            <el-button type="primary" native-type="submit">计算抵扣天数</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div v-if="result !== null" style="margin-top: 20px; text-align: center;">
        <span style="font-size: 24px; font-weight: bold;">抵扣天数: {{ result }}</span>
      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios';
import { getToken, removeToken } from "@/utils/auth"
import { unitySkipApp } from "@/api/system/login"

export default {
  mounted() {
    document.title = "刑期计算器";
  },
  data() {
    return {
      form: {
        qzcsqk: [{
          qzcs: '',
          qzcscqrq: ''
        }],
        xqksrq: ''
      },
      options: [
        '被行政拘留', '被刑事拘留', '期间精神病鉴定', '被取保候审',
        '被继续取保候审', '被临时寄押', '被逮捕', '被监视居住',
        '被指定居所监视居住'
      ],
      result: null
    };
  },
  methods: {
    addQzcs() {
      this.form.qzcsqk.push({
        qzcs: '',
        qzcscqrq: ''
      });
    },
    removeQzcs(index) {
      this.form.qzcsqk.splice(index, 1);
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    submitForm() {
      const postData = {
        qzcsqk: JSON.stringify(this.form.qzcsqk.map(item => ({
          qzcs: item.qzcs,
          qzcscqrq: this.formatDate(item.qzcscqrq)
        }))),
        xqksrq: this.formatDate(this.form.xqksrq)
      };

      const { ak: configAk } = window.globalConfig.xqjsquery
      const token = getToken();
      unitySkipApp(token, configAk).then(res => {
        if (res.code === 2000) {
          postData.ticket = res.ticket;
          axios.post(window.globalConfig.xqjsquery.url, postData)
            .then(response => {
              this.result = response.data.data;
            })
            .catch(error => {
              console.error('Error:', error);
              this.$message.error('请求失败，请检查输入或稍后再试');
            });
        } else {
          this.$confirm(
              "登录过期,您可以停留在次页面,或者重新登录",
              "确认退出",
              {
                confirmButtonText: "重新登录",
                cancelButtonText: "取消",
                type: "warning"
              }
          ).then(() => {
            removeToken();
            window.location.href = unityLogin;
          });
        }
      });

    },
    resetForm() {
      this.form = {
        qzcsqk: [{
          qzcs: '',
          qzcscqrq: ''
        }],
        xqksrq: ''
      };
      this.result = null;
    }
  }
};
</script>

<style scoped>
.container {
  margin-top: 50px;
  display: flex;
  justify-content: center;
  height: 100vh;
}

.box-card {
  width: 50%;
}

.el-button + .el-button {
  margin-left: 10px;
}
</style>
