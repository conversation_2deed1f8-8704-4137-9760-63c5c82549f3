export function formatStartTime(value) {
  if (!/^\d{6}$/.test(value)) return '';
  const year = value.slice(0, 4);
  const month = value.slice(4, 6);
  return `${year}-${month}-01`;
}

export function formatEndTime(value) {
  if (!/^\d{6}$/.test(value)) return '';
  const year = value.slice(0, 4);
  const month = value.slice(4, 6);
  const lastDay = new Date(year, month, 0).getDate(); // 获取该月最后一天
  return `${year}-${month}-${lastDay.toString().padStart(2, '0')}`;
}
