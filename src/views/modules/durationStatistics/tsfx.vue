<!--
 * @Description:
-->
<template>
  <div class="duration-statistics" @click="handleGlobalClick">
    <!-- 顶部筛选区域 -->
    <div class="filter-section">
      <el-form inline>
        <el-form-item label="年份:">
          <el-select
              v-model="searchForm.year"
              placeholder="选择年份"
              style="width: 120px; margin-right: 10px"
              @change="getList"
          >
            <el-option
                v-for="year in yearOptions"
                :key="year"
                :label="year"
                :value="year"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始月份:">
          <el-select
              v-model="searchForm.startMonth"
              placeholder="开始月份"
              style="width: 100px; margin-right: 10px"
              @change="handleStartMonthChange"
          >
            <el-option
                v-for="month in monthOptions"
                :key="month.value"
                :label="month.label"
                :value="month.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结束月份:">
          <el-select
              v-model="searchForm.endMonth"
              placeholder="结束月份"
              style="width: 100px; margin-right: 10px"
              @change="handleEndMonthChange"
          >
            <el-option
                v-for="month in monthOptions"
                :key="month.value"
                :label="month.label"
                :value="month.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" icon="el-icon-download" @click="download" plain>生成excel</el-button>
<!--          <el-button type="warning" @click="toggleDataSource" plain>-->
<!--            {{ useFixedData ? '切换到API数据' : '切换到固定数据' }}-->
<!--          </el-button>-->
        </el-form-item>
      </el-form>
    </div>

    <!-- 罪名搜索框 -->
    <div class="crime-search-section">
      <el-form :inline="true">
        <el-form-item label="罪名搜索:">
          <el-input
            v-model="crimeNameSearch"
            placeholder="请输入罪名进行模糊搜索"
            style="width: 300px;"
            clearable
            @input="handleCrimeNameSearch"
            @clear="clearCrimeNameSearch"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </el-form-item>
        <el-form-item v-if="matchedCrimes.length > 0">
          <el-tag
            v-for="(crime, index) in matchedCrimes"
            :key="index"
            type="info"
            size="small"
            style="margin-right: 8px; cursor: pointer;"
            @click="scrollToCrime(crime)"
          >
            {{ crime }}
          </el-tag>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 新增标题区域 -->
      <div class="table-title">
        <div class="title-decoration"></div>
        <h2>犯罪态势分析</h2>
      </div>
      <div style="margin-top: 5px;margin-bottom: 15px">
        <el-checkbox v-model="tableCellStyleType.max" :true-label="1" :false-label="0">最大值</el-checkbox>
        <el-checkbox v-model="tableCellStyleType.min" :true-label="1" :false-label="0">最小值</el-checkbox>
        <el-checkbox v-model="tableCellStyleType.zero" :true-label="1" :false-label="0">0值</el-checkbox>
<!--        <el-button style="margin-left: 20px" plain type="primary" @click="caseStatuteOfLimitations($event)">案件时效</el-button>-->
<!--        <el-button type="primary" plain @click="focusPerformance($event)">案件绩效</el-button>-->
<!--        <el-button type="primary" plain @click="focusWarningColumn($event)">案件质量</el-button>-->
      </div>
      <!-- :summary-method="getSummaries" -->


      <!-- 表格区域 -->
      <el-table
          :data="tableData"
          border
          style="width: 100%"
          height="650"
          :span-method="arraySpanMethod"
          v-loading="loading"
          :header-cell-style="getHeaderCellStyle"
          :header-cell-class-name="getHeaderCellClassName"
          :cell-style="cellStyle"
          ref="dataTable"
          @header-click="handleHeaderClick"
      >
        <!-- 动态生成的列 -->
        <el-table-column
            v-for="(column, index) in dynamicColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :fixed="column.fixed"
            align="center"
        >
          <template slot-scope="scope">
            <span
                class="cell-content"
                @click="handleCellClick(scope.row, column.prop, scope.$index)"
                :class="getColumnClass(column.prop, scope.row[column.prop])"
            >
              {{ formatCellValue(column.prop, scope.row[column.prop]) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import {
  getDepartmentAndUser,
  getStatisticsData,
  getTableHeader,
  getDetailTableData, getDetailTableLeader
} from "@/api/api/durationStatistics"
// import * as XLSX from 'xlsx';
import * as XLSX from 'sheetjs-style'
import { calculateColWidth, calculateWidth, wsInit } from "@/utils/excelUtils"
import {
  rulePageList,
  tsfxList,
  warningDepartmentAndUser,
  warninglistByFilter,
  warningPageList
} from "@/api/api/warningInfo"
import WarningDetailDialog from "@/views/modules/warningInfo/detailDialog.vue"
import { listCbrpcjgtj } from "@/api/api/qualityEvaluation"
import { formatEndTime, formatStartTime } from "@/views/modules/durationStatistics/timeUtils"
import { bakPjdcData } from "@/views/modules/durationStatistics/bakJson"
import ajzlpjdcDialog from "@/views/modules/ajzlpjdc/ajzlpjdcDialog.vue"
import AllDetailDialog from "@/views/modules/warningInfo/allDetailDialog.vue"
// import { departmentAndUserList, statisticsData, tableHeader } from "@/views/modules/durationStatistics/bakJson"

export default {
  name: "DurationStatistics",
  components: { AllDetailDialog, WarningDetailDialog,ajzlpjdcDialog },
  data() {
    return {
      detailLoading: false,
      WarningDialogVisible: false,
      allWarningDialogVisible: false,
      pjdcDialogVisible: false,
      ruleDetailLoading: false,
      ruleDialogVisible: false,
      ruleTableData: [],
      tableCellStyleType: {
        max: 1,
        min: 1,
        zero: 0
      },
      listQuery: {
        page: 1,
        rows: 5,
        entity: {
          cbrgh: "",
          startTime: "",
          endTime: ""
        }
      },
      ruleListQuery: {
        pageNo: 1,
        rows: 10,
        sort: "",
        order: "",
        filters: "",
        searchFilters: ""
      },
      ruleTotal: 0,
      loading: true,
      searchForm: {
        year: new Date().getFullYear().toString(),
        startMonth: "1",
        endMonth: (new Date().getMonth() + 1).toString()
      },
      // 年份选项
      yearOptions: [],
      // 月份选项
      monthOptions: [
        { label: '1月', value: '1' },
        { label: '2月', value: '2' },
        { label: '3月', value: '3' },
        { label: '4月', value: '4' },
        { label: '5月', value: '5' },
        { label: '6月', value: '6' },
        { label: '7月', value: '7' },
        { label: '8月', value: '8' },
        { label: '9月', value: '9' },
        { label: '10月', value: '10' },
        { label: '11月', value: '11' },
        { label: '12月', value: '12' }
      ],
      tableData: [],
      tableHeader: [],
      columns: [],
      // 罪名搜索相关
      crimeNameSearch: '',
      matchedCrimes: [],
      dialogVisible: false,
      rowCbrgh: '',
      currentCell: {
        department: "",
        cbr: "",
        columnTitle: "",
        value: ""
      },
      detailTableData: [],
      detailTableAllData: [],
      allWarningActiveName: "",
      WarningActiveName: "",
      pjdcActiveName: "",
      total: 0,
      cols: [
        { label: "部门受案号", prop: "BMSAH" },
        { label: "案件名称", prop: "AJMC" },
        { label: "嫌疑人姓名", prop: "XYRXM" },
        { label: "受理日期", prop: "SLRQ_DATE" },
        { label: "审结日期", prop: "SJRQ_DATE" },
        { label: "承办单位", prop: "CBDW_MC" },
        { label: "承办部门", prop: "CBBM_MC" },
        { label: "承办检察官", prop: "CBR" },
        { label: "承办检察官编码", prop: "CBRGH" },
        { label: "办案时长", prop: "DURATION" }
      ],
      departmentAndUserList: [],
      statisticsData: [],
      focusedColumn: '', // 新增：用于跟踪当前聚焦的列
      nowTime: '',
      warningType: '',
      rowData: {},
      allWarningrowData: {},
      pjdcRowData: {},
      activeName: '',
      cbrpcjgtjData: {
        dwbm: "330183"
      },
      yldData:{
        slgsj: "0",
        slbbj: "0",
        bjgsjs: "0",
        wjgsjs: "0"
      },
      // 预警内容弹框相关数据
      warningContentDialogVisible: false,
      warningContentLoading: false,
      warningContentList: [],
      warningContentTotal: 0,
      currentWarningRule: {},
      warningContentQuery: {
        pageNo: 1,
        pageSize: 10
      },
      expandedWarningRows: [], // 预警内容表格展开状态
      useFixedData: true, // 控制是否使用固定数据
      dynamicColumns: [] // 动态生成的列配置
    };
  },
  watch: {
    activeName(activeName) {
      if (activeName) {
        // 每次切换详情的时候，重置分页
        this.listQuery.page = 1;
        // 获取详情数据
        this.getDetailTableData();
      }
    }
  },

  async mounted() {
    // 初始化年份选项（从2020年到当前年份+2年）
    const currentYear = new Date().getFullYear()
    const currentMonth = new Date().getMonth() + 1 // getMonth()返回0-11，需要+1

    for (let year = 2020; year <= currentYear + 2; year++) {
      this.yearOptions.push(year.toString())
    }

    // 设置默认值：当前年份，1月开始，当前月份结束
    this.searchForm.year = currentYear.toString()
    this.searchForm.startMonth = "1"
    this.searchForm.endMonth = currentMonth.toString()

    document.title = "犯罪态势分析";
    // 获取犯罪统计数据
    await this.getList();
  },
  methods: {
    // 获取开始时间字符串（格式：YYYYMM）
    getStartTimeString() {
      return this.searchForm.year + this.searchForm.startMonth.padStart(2, '0');
    },
    // 获取结束时间字符串（格式：YYYYMM）
    getEndTimeString() {
      return this.searchForm.year + this.searchForm.endMonth.padStart(2, '0');
    },

    // 验证月份范围
    validateMonthRange() {
      const startMonth = parseInt(this.searchForm.startMonth);
      const endMonth = parseInt(this.searchForm.endMonth);

      if (startMonth > endMonth) {
        this.$message.error('开始月份不能大于结束月份');
        return false;
      }
      return true;
    },

    // 处理开始月份变化
    handleStartMonthChange() {
      if (this.validateMonthRange()) {
        this.getList();
      } else {
        // 如果验证失败，重置为有效值
        this.searchForm.startMonth = this.searchForm.endMonth;
      }
    },

    // 处理结束月份变化
    handleEndMonthChange() {
      if (this.validateMonthRange()) {
        this.getList();
      } else {
        // 如果验证失败，重置为有效值
        this.searchForm.endMonth = this.searchForm.startMonth;
      }
    },
    smoothScroll(element, target) {
      const start = element.scrollLeft
      const change = target - start
      const duration = 300
      let startTime = null

      const animation = (currentTime) => {
        if (startTime === null) startTime = currentTime
        const timeElapsed = currentTime - startTime
        const progress = Math.min(timeElapsed / duration, 1)

        const run = progress => progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2

        element.scrollLeft = start + change * run(progress)

        if (timeElapsed < duration) {
          requestAnimationFrame(animation)
        }
      }

      requestAnimationFrame(animation)
    },


    download() {
      this.$confirm(`确定将当前的数据生成一个Excel?`, '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 检查是否有数据
          if (!this.tableData || this.tableData.length === 0) {
            this.$message.warning('没有可导出的数据');
            return;
          }

          // 准备表头
          const headers = this.dynamicColumns.map(col => col.label);

          // 准备数据行
          const data = this.tableData.map(row => {
            return this.dynamicColumns.map(col => {
              const value = row[col.prop];
              // 处理空值和格式化
              if (value === null || value === undefined || value === '') return '';

              // 如果是增长率列，格式化为百分比
              if (col.prop.includes('增长率')) {
                return this.formatCellValue(col.prop, value);
              }

              return value;
            });
          });

          // 合并表头和数据
          const excelData = [headers, ...data];

          // 创建工作表
          const ws = wsInit(excelData, 1);

          // 设置列宽
          const colWidths = this.dynamicColumns.map(col => {
            // 计算列宽，考虑中文字符
            let maxWidth = col.label.length;

            // 检查数据中的最大宽度
            this.tableData.forEach(row => {
              const value = row[col.prop];
              if (value !== null && value !== undefined) {
                const str = value.toString();
                const chineseChars = str.match(/[^\x00-\xff]/g) || [];
                const width = str.length + chineseChars.length;
                if (width > maxWidth) maxWidth = width;
              }
            });

            // 限制最小和最大宽度
            return { wch: Math.min(Math.max(maxWidth + 2, col.label.length + 4), 25) };
          });

          ws['!cols'] = colWidths;

          // 创建工作簿并导出
          const wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, '犯罪态势分析');

          // 生成文件名
          const today = new Date();
          const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
          const timeRange = `${this.searchForm.year}年${this.searchForm.startMonth}月-${this.searchForm.endMonth}月`;
          const fileName = `犯罪态势分析_${timeRange}_${dateStr}.xlsx`;

          XLSX.writeFile(wb, fileName);

          this.$message.success('Excel文件导出成功');
        } catch (error) {
          console.error('Excel generation failed:', error);
          this.$message.error('Excel导出失败，请稍后重试');
        }
      }).catch((err) => {
        console.error('Excel generation cancelled:', err);
      });
    },
    async getList() {
      this.loading = true;

      this.tableData = await tsfxList({
        "year": this.searchForm.year,
        "startMonth": this.searchForm.startMonth,
        "endMonth": this.searchForm.endMonth
      })
      // 根据数据的第一个对象的key生成动态列
      this.generateDynamicColumns();
      this.loading = false;
    },
    getMaxAndMin(prop) {
      const values = this.tableData.filter(i => i.department !== '总计' ).map(item => Number(item[prop]));
      const filter = values.filter(i => i !== 0)
      return {
        max: Math.max(...filter),
        min: Math.min(...filter)
      };
    },
    isMax(value, prop) {
      let max = this.getMaxAndMin(prop).max
      return value == max;
    },
    isMin(value, prop) {
      let min = this.getMaxAndMin(prop).min
      return value == min;
    },

    handleSearch() {
      // 验证月份范围
      if (!this.validateMonthRange()) {
        return;
      }
      // 实现查询逻辑
      this.getList();
    },
    handleReset() {
      // 重置为默认值：当前年份，1月开始，当前月份结束
      const currentYear = new Date().getFullYear().toString()
      const currentMonth = (new Date().getMonth() + 1).toString() // getMonth()返回0-11，需要+1

      this.searchForm = {
        year: currentYear,
        startMonth: "1",
        endMonth: currentMonth
      };
      this.tableCellStyleType = {
        max: 0,
        min: 0,
        zero: 0
      }
      this.getList();
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.useFixedData) {
        // 对于犯罪数据，不需要合并单元格
        return { rowspan: 1, colspan: 1 };
      }

      // 原有的合并逻辑
      // 处理合计行
      if (rowIndex === this.tableData.length) {
        if (columnIndex === 0) {
          return { rowspan: 1, colspan: 2 };
        }
        if (columnIndex === 1) {
          return { rowspan: 0, colspan: 0 };
        }
      }

      // 原有的部门合并逻辑
      if (columnIndex === 0) {
        const dept = this.tableData[rowIndex].department;
        let count = 1;

        if (rowIndex > 0 && dept === this.tableData[rowIndex - 1].department) {
          return { rowspan: 0, colspan: 0 };
        }

        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].department === dept) {
            count++;
          } else {
            break;
          }
        }

        return { rowspan: count, colspan: 1 };
      }
    },
    handleCurrentChange(page) {
      this.listQuery.page = page;
      this.getDetailTableData();
    },
    handleSizeChange(size) {
      this.listQuery.rows = size;
      this.getDetailTableData();
    },
    ruleHandleCurrentChange(page) {
      this.ruleListQuery.pageNo = page;
      this.getRuleTableData();
    },
    ruleHandleSizeChange(size) {
      this.ruleListQuery.rows = size;
      this.getRuleTableData();
    },
    ruleReset() {
      this.ruleListQuery.gzmc = ''
      this.ruleListQuery.pageNo = 1
      this.getRuleTableData()
    },
    getRuleTableData() {
      rulePageList(this.ruleListQuery).then(res=>{
        this.ruleTotal = res.total
        this.ruleTableData = res.rows
      })
    },
    async getDetailTableAllData() {
      const query = this.listQuery
      query.rows = 999999
      const allData = await getDetailTableData(
          this.activeName,
          query
      );
      this.detailTableAllData = allData.data.list;
    },

    async getDetailTableData() {
      this.detailLoading = true;
      const { data } = await getDetailTableData(
          this.activeName,
          this.listQuery
      );
      this.detailTableData = data.list;
      this.total = data.total;
      this.detailLoading = false;
    },
    showRuleDialog(type) {
      this.ruleDialogVisible = true;
      this.ruleListQuery.page = 1;
      this.ruleListQuery.type = type;
      this.getRuleTableData()
    },
    // 生成预警内容表格的唯一行键
    getWarningRowKey(row) {
      return row.id || `warning_${Math.random().toString(36).substr(2, 9)}`;
    },
    handleCellClick(row, column, index) {
      if (this.useFixedData) {
        // 对于固定数据，显示简单的信息
        this.$message.info(`点击了 ${row.罪名} 的 ${column} 数据: ${row[column]}`);
        return;
      }

      if (column.title === '优秀' || column.title === '瑕疵' || column.title === '合格' || column.title === '不合格') {
        let pcjl = '0'
        switch (column.title) {
          case '优秀':
            pcjl = '1'
            break
          case '瑕疵':
            pcjl = '3'
            break
          case '合格':
            pcjl = '2'
            break
          case '不合格':
            pcjl = '4'
            break
        }
        this.pjdcRowData = {
          cbrgh: row.cbrgh,
          dwbm: window.globalConfig.qualityEvaluation.dwbm,
          pcjldm: pcjl
        }
        this.pjdcRowData.wckssj = formatStartTime(this.getStartTimeString());
        this.pjdcRowData.wcjzsj = formatEndTime(this.getEndTimeString());
        if (this.pjdcRowData.wckssj == null) {
          this.pjdcRowData.wcjzsj = '1990-01-01'
        }
        if (this.pjdcRowData.wckssj == null) {
          this.pjdcRowData.wcjzsj = '2099-12-31'
        }
        this.pjdcActiveName = '案件质量评查-' + column.title
        this.pjdcDialogVisible = true;

      } else if (column.title === '本院规则预警') {
        this.rowData = { cbrgh: row.cbrgh,startTime: '',endTime:'',alData: row }
        this.rowData.startTime = this.getStartTimeString();
        this.rowData.endTime = this.getEndTimeString();
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '190001'
        }
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '299901'
        }
        this.WarningActiveName = column.title;
        this.WarningDialogVisible = true;
        this.warningType = '市院'
      }
      else if (column.title === '省平台规则预警') {

        this.rowData = { cbrgh: row.cbrgh,startTime: '',endTime:'',alData: row }
        this.rowData.startTime = this.getStartTimeString();
        this.rowData.endTime = this.getEndTimeString();
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '190001'
        }
        if (this.rowData.startTime == null) {
          this.rowData.endTime = '299901'
        }
        this.WarningActiveName = column.title;
        this.WarningDialogVisible = true;
        this.warningType = '省院'
      }
      else if (column.title === '巡查案件数量') {
        this.allWarningrowData = { cbrgh: row.cbrgh,startTime: '',endTime:'',alData: row }
        this.allWarningrowData.startTime = this.getStartTimeString();
        this.allWarningrowData.endTime = this.getEndTimeString();
        if (this.allWarningrowData.startTime == null) {
          this.allWarningrowData.endTime = '190001'
        }
        if (this.allWarningrowData.startTime == null) {
          this.allWarningrowData.endTime = '299901'
        }
        this.allWarningActiveName = column.title;
        this.allWarningDialogVisible = true
      } else {
        // 初始化查询参数
        this.listQuery = {
          page: 1,
          rows: 5,
          entity: {
            cbrgh: "",
            startTime: "",
            endTime: ""
          }
        }
        if (row.department == "总计") {
          this.listQuery.entity.cbrgh = ""
        } else {
          this.listQuery.entity.cbrgh = row.cbrgh
        }
        this.listQuery.entity.startTime = this.getStartTimeString();
        this.listQuery.entity.endTime = this.getEndTimeString();
        if (this.listQuery.entity.startTime == null) {
          this.listQuery.entity.startTime = '190001'
        }
        if (this.listQuery.entity.endTime == null) {
          this.listQuery.entity.endTime = '299901'
        }
        this.currentCell = {
          row,
          column,
          index,
          value: +row[column.title]
        }
        // 选中第一个 Tabs
        this.activeName = this.currentCell.column.codes[0].code
        this.dialogVisible = true
      }
    },
    //修改列方法
    cellStyle({row, column}) {
      // 基础样式对象
      let style = {}
      return style
    },
    // 修改表头样式方法
    getHeaderCellStyle({ column }) {
      // 基础样式
      const style = {
        color: '#fff',
        fontWeight: 'bold',
        background: '#409EFF'
        // background: '#d60000'
      }

      // 为特定表头添加类名，用于 hover 效果和点击判断
      if (column.label === '省平台规则预警' || column.label === '本院规则预警') {
        style.cursor = 'pointer'; // 直接在这里设置手型光标
        // 为了更方便地在 CSS 中应用 hover 效果，我们也可以返回一个类名，但直接设置 cursor 也可以
        // 这里我们直接在 getHeaderCellStyle 中返回 cursor 样式，并结合 CSS 实现背景变色
      }

      if (this.focusedColumn === '预警案件数量') {
        switch (column.label) {
          case '修改完成数/总预警数':
            style.borderTop = '1px solid #d60000 '
            style.borderBottom = '1px solid #d60000 '
            style.borderLeft = '1px solid #d60000 '
            style.borderRight = '2px solid #d60000 '
            break
          case '本院规则预警':
          case '巡查案件数量':
          case '省平台规则预警':
            style.borderTop = '1px solid #d60000 '
            style.borderBottom = '1px solid #d60000 '
            style.borderLeft = '1px solid #d60000 '
            break
        }
      }
      if (this.focusedColumn === '办案绩效') {
        console.log(column.label)
        switch (column.label) {
          case '案件质量评查等次':
            style.borderBottom = '1px solid #d60000 '
            style.borderTop = '1px solid #d60000 '
            style.borderRight = '1px solid #d60000 '
            break
          case '监督立案数':
            style.borderLeft = '1px solid #d60000 '
            style.borderRight = '1px solid #d60000 '
            style.borderBottom = '1px solid #d60000 '
            style.borderTop = '1px solid #d60000 '
            break
          case '瑕疵':
          case '合格' :
          case '不合格':
          case '优秀':
            style.borderRight = '1px solid #d60000 '
            style.borderBottom = '1px solid #d60000 '
            break
          case '监督撤案数':
          case '追捕、追诉数量' :
          case '刑事抗诉数（再审抗诉、二审抗诉）':
          case '书面纠正侦查活动违法数':
          case '案件材料是否齐全':
            style.borderRight = '1px solid #d60000 '
            style.borderBottom = '1px solid #d60000 '
            style.borderTop = '1px solid #d60000 '
            break
        }
      }
      return style
    },
    // 返回特定表头的类名
    getHeaderCellClassName({ column }) {
      if (column.label === '省平台规则预警' || column.label === '本院规则预警') {
        return 'rule-header-cell'; // 为目标列返回类名
      }
      return ''; // 其他列返回空字符串
    },
    // 表头点击事件处理
    handleHeaderClick(column) {
      if (column.label === '省平台规则预警') {
        this.showRuleDialog('省院');
      }
      if (column.label === '本院规则预警') {
        this.showRuleDialog('本院');
      }
    },
    // 修改聚焦方法
    focusWarningColumn(event) {
      event.stopPropagation()
      this.focusedColumn = '预警案件数量'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, maxScroll)
          this.smoothScroll(headerWrapper, maxScroll)
        }
      })
    },
    focusPerformance(event) {
      event.stopPropagation()
      this.focusedColumn = '办案绩效'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, maxScroll)
          this.smoothScroll(headerWrapper, maxScroll)
          // this.smoothScroll(bodyWrapper, 300)
          // this.smoothScroll(headerWrapper, 300)
        }
      })
    },
    caseStatuteOfLimitations(event) {
      event.stopPropagation()
      this.focusedColumn = '案件时效'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, 1)
          this.smoothScroll(headerWrapper, 1)
          // this.smoothScroll(bodyWrapper, 300)
          // this.smoothScroll(headerWrapper, 300)
        }
      })
    },
    handleGlobalClick() {
      this.focusedColumn = ''
    },

    // 格式化百分比
    formatPercentage(value) {
      if (value === null || value === undefined || value === '') return '-';
      return value.toFixed(2) + '%';
    },

    // 获取增长数据的样式类
    getGrowthClass(value) {
      if (value === null || value === undefined || value === '') return '';
      // const numValue = Number(value);
      // if (numValue > 0) return 'growth-positive';
      // if (numValue < 0) return 'growth-negative';
      return '';
    },
    // 生成动态列配置
    generateDynamicColumns() {
      if (!this.tableData || this.tableData.length === 0) {
        this.dynamicColumns = [];
        return;
      }

      const firstRow = this.tableData[0];
      const columns = [];

      Object.keys(firstRow).forEach((key, index) => {
        const column = {
          prop: key,
          label: key,
          width: this.getColumnWidth(key),
          fixed: index === 0 ? 'left' : false // 第一列固定在左侧
        };
        columns.push(column);
      });

      this.dynamicColumns = columns;
    },
    // 根据列名获取合适的列宽
    getColumnWidth(columnName) {
      // 罪名列较宽
      if (columnName === '罪名')
        return 150;
      // 默认宽度
      return 180;
    },
    // 根据列名和值获取样式类
    getColumnClass(columnName, value) {
      // 增长相关的列应用增长样式
      if (columnName.includes('增长')) {
        return this.getGrowthClass(value);
      }
      return '';
    },
    // 格式化单元格值
    formatCellValue(columnName, value) {
      if (value === null || value === undefined || value === '') return '-';

      // 增长率列显示百分比
      if (columnName.includes('增长率')) {
        return this.formatPercentage(value);
      }

      return value;
    },

    // 切换数据源
    toggleDataSource() {
      this.useFixedData = !this.useFixedData;
      this.getList();
    },

    // 罪名搜索相关方法
    handleCrimeNameSearch() {
      if (!this.crimeNameSearch.trim()) {
        this.matchedCrimes = [];
        return;
      }

      // 从表格数据中提取所有罪名
      const allCrimes = [];
      this.tableData.forEach(row => {
        if (row['罪名']) {
          allCrimes.push(row['罪名']);
        }
      });

      // 模糊匹配罪名
      const searchTerm = this.crimeNameSearch.toLowerCase();
      this.matchedCrimes = allCrimes.filter(crime =>
        crime.toLowerCase().includes(searchTerm)
      ).slice(0, 5); // 限制显示前10个匹配结果
    },

    clearCrimeNameSearch() {
      this.crimeNameSearch = '';
      this.matchedCrimes = [];
    },

    scrollToCrime(crimeName) {
      // 找到对应罪名在表格中的行索引
      const rowIndex = this.tableData.findIndex(row => row['罪名'] === crimeName);

      if (rowIndex !== -1) {
        // 使用Element UI表格的scrollToRow方法滚动到指定行
        this.$nextTick(() => {
          const table = this.$refs.dataTable;
          if (table && table.bodyWrapper) {
            // 计算行高和滚动位置
            const rowHeight = 40; // 假设每行高度为40px，可根据实际情况调整
            const scrollTop = rowIndex * rowHeight;

            // 滚动到指定位置
            table.bodyWrapper.scrollTop = scrollTop;

            // 高亮显示匹配的行和罪名单元格
            this.highlightCrimeCell(rowIndex, crimeName);
          }
        });
      } else {
        this.$message.warning(`未找到罪名: ${crimeName}`);
      }
    },

    highlightCrimeCell(rowIndex, crimeName) {
      // 高亮显示罪名单元格
      this.$nextTick(() => {
        const table = this.$refs.dataTable;
        if (table && table.$el) {
          const rows = table.$el.querySelectorAll('.el-table__body-wrapper tbody tr');
          if (rows[rowIndex]) {
            const targetRow = rows[rowIndex];

            // 找到罪名列的索引
            let crimeColumnIndex = -1;
            this.dynamicColumns.forEach((column, index) => {
              if (column.prop === '罪名' || column.label === '罪名') {
                crimeColumnIndex = index;
              }
            });

            console.log('罪名列索引:', crimeColumnIndex);
            console.log('动态列配置:', this.dynamicColumns);

            if (crimeColumnIndex !== -1) {
              const cells = targetRow.querySelectorAll('td');
              const crimeCell = cells[crimeColumnIndex];

              console.log('找到的罪名单元格:', crimeCell);
              console.log('单元格文本内容:', crimeCell ? crimeCell.textContent.trim() : '未找到');

              if (crimeCell) {
                // 设置罪名单元格的红色边框
                crimeCell.style.border = '1px solid #ff4d4f !important';
                crimeCell.style.borderRadius = '4px !important';
                crimeCell.style.transition = 'border 0.3s !important';
                crimeCell.style.boxSizing = 'border-box !important';

                // 同时高亮整行背景
                targetRow.style.backgroundColor = '#fff2f0 !important';
                targetRow.style.transition = 'background-color 0.3s !important';

                console.log('已应用红色边框样式');

                // 5秒后恢复原样
                // setTimeout(() => {
                //   crimeCell.style.border = '';
                //   crimeCell.style.borderRadius = '';
                //   crimeCell.style.boxSizing = '';
                //   targetRow.style.backgroundColor = '';
                //   console.log('已恢复原始样式');
                // }, 5000);
              } else {
                console.log('未找到罪名单元格');
              }
            } else {
              console.log('未找到罪名列');
            }
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.duration-statistics {
  background-color: #f5f7fa;
  min-height: 90vh;

  .filter-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    //margin-bottom: 16px;

    .filter-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-group {
      display: flex;
      gap: 12px;
      flex: 1;

      .wide-select {
        width: 240px;
      }

      .normal-select {
        width: 120px;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 12px;

        .date-picker {
          width: 240px;
        }
      }
    }

    .data-type-switch {
      display: flex;
      gap: 16px;

      .switch-item {
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 4px;
        color: #606266;

        &.active {
          background-color: #e6f2ff;
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .analysis-options {
      display: flex;
      gap: 24px;
      align-items: center;

      .option-item {
        cursor: pointer;
        color: #606266;

        &.active {
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      align-items: center;

      .search-input {
        width: 200px;
      }

      .el-button {
        padding: 8px 16px;
      }
    }
  }

  .table-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;

    .table-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;


      .title-decoration {
        width: 4px;
        height: 20px;
        background-color: #409eff;
        margin-right: 8px;
        border-radius: 2px;
      }

      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin: 0;
      }
    }
  }

  .notes-area {
    margin-top: 16px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    line-height: 24px;
    font-size: 16px;
    color: #4f5e7b;
  }
}

.cell-content {
  display: inline-block;
  width: 100%;
  cursor: pointer;

  //.max-num {
  //  //color: #2487fa;
  //  background: #2487fa;
  //  color: #fff;
  //}

  //.max-num:hover {
  //  font-weight: bold;
  //}


  //.min-num {
  //  //color: red;
  //  background: orange;
  //  color: #fff;
  //}

  //.min-num:hover {
  //  font-weight: bold;
  //}

}
.calculation-rule {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .rule-label {
    color: #606266;
    font-weight: 500;
  }

  .rule-content {
    color: #303133;
    line-height: 1.5;
  }
}

// 为特定表头添加 hover 效果
::v-deep .el-table__header-wrapper th {
  &.is-leaf { // 只针对最底层的表头单元格
    div.cell:hover {
      // 检查列的 property 是否匹配
      // 注意：这种方式依赖于 Element UI 内部结构，可能不够稳定
      // 如果 getHeaderCellStyle 能稳定添加 class 会更好，但目前先用这种方式
    }
  }
}

// 通过 getHeaderCellStyle 添加的 cursor 样式已经生效
// 这里我们只添加 hover 时的背景色变化
// 使用 getHeaderCellClassName 添加的类名来定位
::v-deep .el-table__header-wrapper th.rule-header-cell {
  &:hover {
    background-color: #66b1ff ; // 悬停时改变背景色
  }
}

/* 预警数量单元格样式 */
.warning-count-cell {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  display: inline-block;
  width: 100%;
  text-align: center;
}

.warning-count-cell:hover {
  background-color: #e6f3ff ; // 浅蓝色背景
  color: #409eff;
}

/* 预警内容展开样式 */
.warning-expand-content {
  padding-right: 20px;
  padding-left: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  margin: 10px;
}

.warning-expand-content h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.warning-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  align-items: start;
}

.detail-item {
  display: flex;
  flex-direction: column;
  background: white;
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  font-size: 12px;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  color: #303133;
  font-size: 14px;
  word-break: break-word;
}

/* JSON行样式 */
.json-row {
  grid-column: 1 / -1;
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.json-item {
  flex: 1;
  margin: 0;
}

.json-content {
  margin-top: 8px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  overflow: visible; /* 去除滚动条 */
  max-height: none; /* 去除高度限制 */
}

.json-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 预警内容表格行点击样式 */
.warning-expand-content + .el-table tbody tr {
  cursor: pointer;
}

.warning-expand-content + .el-table tbody tr:hover {
  background-color: #f5f7fa ;
}

/* 增长数据样式 */
.growth-positive {
  color: #67C23A;
  font-weight: bold;
}

.growth-negative {
  color: #F56C6C;
  font-weight: bold;
}

.cell-content {
  cursor: pointer;
  &:hover {
    color: #409EFF;
  }
}

/* 悬浮表头样式 */
.sticky-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }

  // 隐藏表格内容，只显示表头
  ::v-deep .el-table__body-wrapper {
    display: none;
  }

  // 确保表头样式一致
  ::v-deep .el-table__header-wrapper {
    overflow-x: auto;

    .el-table__header {
      width: 100% ;
    }
  }

  // 保持表头单元格样式
  ::v-deep .el-table th {
    background-color: #409EFF ;
    color: #fff ;
    font-weight: bold ;
  }
}

</style>
