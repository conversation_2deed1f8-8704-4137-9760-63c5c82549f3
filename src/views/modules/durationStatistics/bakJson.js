export const tableHeader = [
  {
    "title": "受理报捕件/人",
    "codes": null,
    "indicatorCode": null,
    "formula": null,
    "suffix": null,
    "subTitle": [
      {
        "title": "件",
        "codes": [
          {
            "title": "受理报捕件数",
            "code": "slbbjs"
          }
        ],
        "indicatorCode": "slbbj",
        "formula": "受理报捕件数",
        "suffix": "",
        "subTitle": null
      },
      {
        "title": "人",
        "codes": [
          {
            "title": "受理报捕人数",
            "code": "slbbrs"
          }
        ],
        "indicatorCode": "slbbr",
        "formula": "受理报捕人数",
        "suffix": "",
        "subTitle": null
      }
    ]
  },
  {
    "title": "受理公诉件/人",
    "codes": null,
    "indicatorCode": null,
    "formula": null,
    "suffix": null,
    "subTitle": [
      {
        "title": "件",
        "codes": [
          {
            "title": "受理公诉件数",
            "code": "slgsjs"
          }
        ],
        "indicatorCode": "slgsj",
        "formula": "受理公诉件数",
        "suffix": "",
        "subTitle": null
      },
      {
        "title": "人",
        "codes": [
          {
            "title": "受理公诉人数",
            "code": "slgsrs"
          }
        ],
        "indicatorCode": "slgsr",
        "formula": "受理公诉人数",
        "suffix": "",
        "subTitle": null
      }
    ]
  },
  {
    "title": "办结公诉件数",
    "codes": [
      {
        "title": "办结公诉件数",
        "code": "bjgsjs"
      }
    ],
    "indicatorCode": "bjgsjs",
    "formula": "办结公诉件数",
    "suffix": "",
    "subTitle": null
  },
  {
    "title": "未结公诉件数",
    "codes": [
      {
        "title": "未结公诉件数",
        "code": "wbjgsjs"
      }
    ],
    "indicatorCode": "wjgsjs",
    "formula": "未结公诉件数",
    "suffix": "",
    "subTitle": null
  },
  {
    "title": "平均办案时长（天数）",
    "codes": [
      {
        "title": "办结公诉件数",
        "code": "bjgsjs"
      },
      {
        "title": "办案时长",
        "code": "basc"
      }
    ],
    "indicatorCode": "pjbasc",
    "formula": "办案时长/办结公诉件数",
    "suffix": "",
    "subTitle": null
  },
  {
    "title": "办案时长10天（含以下）",
    "codes": null,
    "indicatorCode": null,
    "formula": null,
    "suffix": null,
    "subTitle": [
      {
        "title": "办结件数",
        "codes": [
          {
            "title": "办案时长10天（含以下）",
            "code": "basc10"
          }
        ],
        "indicatorCode": "basc10j",
        "formula": "办案时长10天（含以下）",
        "suffix": "",
        "subTitle": null
      },
      {
        "title": "占比",
        "codes": [
          {
            "title": "办案时长10天（含以下）",
            "code": "basc10"
          },
          {
            "title": "办结公诉数",
            "code": "bjgsjs"
          }
        ],
        "indicatorCode": "basc10zb",
        "formula": "办案时长10天（含以下）/办结公诉数",
        "suffix": "%",
        "subTitle": null
      }
    ]
  },
  {
    "title": "办案时长15天（11-15天）",
    "codes": null,
    "indicatorCode": null,
    "formula": null,
    "suffix": null,
    "subTitle": [
      {
        "title": "办结件数",
        "codes": [
          {
            "title": "办案时长15天（11-15天）",
            "code": "basc15"
          }
        ],
        "indicatorCode": "basc15j",
        "formula": "办案时长15天（11-15天）",
        "suffix": "",
        "subTitle": null
      },
      {
        "title": "占比",
        "codes": [
          {
            "title": "办案时长15天（11-15天）",
            "code": "basc15"
          },
          {
            "title": "办结公诉数",
            "code": "bjgsjs"
          }
        ],
        "indicatorCode": "basc15zb",
        "formula": "办案时长15天（11-15天）/办结公诉数",
        "suffix": "%",
        "subTitle": null
      }
    ]
  },
  {
    "title": "15天内审结率",
    "codes": [
      {
        "title": "办案时长15天（11-15天）",
        "code": "basc15"
      },
      {
        "title": "办案时长10天（含以下）",
        "code": "basc10"
      },
      {
        "title": "办结公诉数",
        "code": "bjgsjs"
      }
    ],
    "indicatorCode": "15tnsjl",
    "formula": "(办案时长10天（含以下）+ 办案时长15天（11-15天）)/办结公诉数",
    "suffix": "%",
    "subTitle": null
  },
  {
    "title": "办案时长30天（16-30天）",
    "codes": null,
    "indicatorCode": null,
    "formula": null,
    "suffix": null,
    "subTitle": [
      {
        "title": "办结件数",
        "codes": [
          {
            "title": "办案时长30天（16-30天）",
            "code": "basc30"
          }
        ],
        "indicatorCode": "basc30j",
        "formula": "办案时长30天（16-30天）",
        "suffix": "",
        "subTitle": null
      },
      {
        "title": "占比",
        "codes": [
          {
            "title": "办案时长30天（16-30天）",
            "code": "basc30"
          },
          {
            "title": "办结公诉数",
            "code": "bjgsjs"
          }
        ],
        "indicatorCode": "basc30zb",
        "formula": "办案时长30天（16-30天）/办结公诉数",
        "suffix": "%",
        "subTitle": null
      }
    ]
  },
  {
    "title": "办案时长45天（31-45天）",
    "codes": null,
    "indicatorCode": null,
    "formula": null,
    "suffix": null,
    "subTitle": [
      {
        "title": "办结件数",
        "codes": [
          {
            "title": "办案时长45天（31-45天）",
            "code": "basc45"
          }
        ],
        "indicatorCode": "basc45j",
        "formula": "办案时长45天（31-45天）",
        "suffix": "",
        "subTitle": null
      },
      {
        "title": "占比",
        "codes": [
          {
            "title": "办案时长45天（31-45天）",
            "code": "basc45"
          },
          {
            "title": "办结公诉数",
            "code": "bjgsjs"
          }
        ],
        "indicatorCode": "basc45zb",
        "formula": "办案时长45天（31-45天）/办结公诉数",
        "suffix": "%",
        "subTitle": null
      }
    ]
  },
  {
    "title": "45天内审结率",
    "codes": [
      {
        "title": "办案时长15天（11-15天）",
        "code": "basc15"
      },
      {
        "title": "办案时长10天（含以下）",
        "code": "basc10"
      },
      {
        "title": "办案时长30天（16-30天）",
        "code": "basc30"
      },
      {
        "title": "办案时长45天（31-45天）",
        "code": "basc45"
      },
      {
        "title": "办结公诉数",
        "code": "bjgsjs"
      }
    ],
    "indicatorCode": "45tnsjl",
    "formula": "(办案时长10天（含以下）+办案时长15天（11-15天）+办案时长30天（16-30天）+办案时长45天（31-45天）)/办结公诉数",
    "suffix": "%",
    "subTitle": null
  },
  {
    "title": "审结率",
    "codes": [
      {
        "title": "期末未结（人）",
        "code": "mqwj"
      },
      {
        "title": "受理合计（人）",
        "code": "slgsrs"
      },
      {
        "title": "上期受理未结（人）",
        "code": "sqslwj"
      }
    ],
    "indicatorCode": "sjl",
    "formula": "1-期末未结（人）/(受理合计（人）+上期受理未结（人）)",
    "suffix": "%",
    "subTitle": null
  },
  {
    "title": "超六个半月未结",
    "codes": [
      {
        "title": "超六个半月未结",
        "code": "c6_5ywj"
      }
    ],
    "indicatorCode": "c6_5ywj",
    "formula": "超六个半月未结",
    "suffix": "",
    "subTitle": null
  },
  {
    "title": "超三个半月未结",
    "codes": [
      {
        "title": "超三个半月未结",
        "code": "c3_5ywj"
      }
    ],
    "indicatorCode": "c3_5ywj",
    "formula": "超三个半月未结",
    "suffix": "",
    "subTitle": null
  },
  {
    "title": "超一个半月未结",
    "codes": [
      {
        "title": "未结公诉件数",
        "code": "c1_5ywj"
      }
    ],
    "indicatorCode": "c1_5ywj",
    "formula": "超一个半月未结",
    "suffix": "",
    "subTitle": null
  },
  {
    "title": "超一个月未结",
    "codes": [
      {
        "title": "超一个月未结",
        "code": "c1ywj"
      }
    ],
    "indicatorCode": "c1ywj",
    "formula": "超一个月未结",
    "suffix": "",
    "subTitle": null
  }
]
export const departmentAndUserList = [
  {
    "name": "侦协办办案组",
    "personList": [
      {
        "cbr": "童冬凤",
        "cbrgh": "3301830014"
      }
    ]
  },
  {
    "name": "简易取保组",
    "personList": [
      {
        "cbr": "汪琰婷",
        "cbrgh": "3301830016"
      }
    ]
  },
  {
    "name": "速裁组",
    "personList": [
      {
        "cbr": "章满群",
        "cbrgh": "3301830027"
      },
      {
        "cbr": "俞凯",
        "cbrgh": "3301830017"
      }
    ]
  },
  {
    "name": "繁案组",
    "personList": [
      {
        "cbr": "黄稼嫦",
        "cbrgh": "3301830013"
      },
      {
        "cbr": "杜巧萍",
        "cbrgh": "3301830019"
      },
      {
        "cbr": "张维",
        "cbrgh": "3301830028"
      },
      {
        "cbr": "李伊红",
        "cbrgh": "3301830042"
      },
      {
        "cbr": "朱青",
        "cbrgh": "3301830018"
      }
    ]
  },
  {
    "name": "第三检察部",
    "personList": [
      {
        "cbr": "刘晖娟",
        "cbrgh": "3301830044"
      },
      {
        "cbr": "裘纪迪",
        "cbrgh": "3301830058"
      }
    ]
  },
  {
    "name": "第四检察部",
    "personList": [
      {
        "cbr": "章立英",
        "cbrgh": "3301830036"
      },
      {
        "cbr": "洪琛",
        "cbrgh": "3301830037"
      }
    ]
  },
  {
    "name": "第五检察部",
    "personList": [
      {
        "cbr": "叶慧",
        "cbrgh": "3301830012"
      },
      {
        "cbr": "丁生荣",
        "cbrgh": "3301830041"
      }
    ]
  },
  {
    "name": "第七检察部",
    "personList": [
      {
        "cbr": "朱笛琴",
        "cbrgh": "3301830045"
      }
    ]
  }
]
export const statisticsData = {
  "3301830012": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830013": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830014": [
    "0",
    "0",
    "2",
    "2",
    "0",
    "2",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    "50.00",
    "1",
    "0",
    "1",
    "0"
  ],
  "3301830016": [
    "0",
    "0",
    "0",
    "1",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0.00",
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830017": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830018": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830019": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830027": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830028": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830036": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830037": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830041": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830042": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830044": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830045": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "3301830058": [
    "0",
    "0",
    "0",
    "0",
    "0",
    "0",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    null,
    "0",
    "0",
    "0",
    "0"
  ],
  "total": [
    "0",
    "0",
    "2",
    "3",
    "0",
    "2",
    null,
    "0",
    null,
    "0",
    null,
    null,
    "0",
    null,
    "0",
    null,
    null,
    "40.00",
    "1",
    "0",
    "1",
    "0"
  ]
}

export const bakPjdcData = [
  {
    "bhg": 0,
    "yz": 0,
    "cbr": "吕某某",
    "cbrgh": "3301830013",
    "xc": 0,
    "hg": 0
  },
  {
    "bhg": 0,
    "yz": 0,
    "cbr": "吕某某",
    "cbrgh": "3301830014",
    "xc": 0,
    "hg": 0
  },
  {
    "bhg": 0,
    "yz": 0,
    "cbr": "吕某某",
    "cbrgh": "3301830016",
    "xc": 0,
    "hg": 0
  },
  {
    "bhg": 0,
    "yz": 0,
    "cbr": "吕某某",
    "cbrgh": "3301830017",
    "xc": 0,
    "hg": 0
  },
]
