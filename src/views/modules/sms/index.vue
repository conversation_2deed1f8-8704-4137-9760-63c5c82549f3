<!--
 * @Description:
-->
<template>
  <div class="duration-statistics" @click="handleGlobalClick">
    <!-- 顶部筛选区域 -->
<!--    <div class="filter-section">-->
<!--      <el-form inline>-->
<!--        <el-form-item label="部门受案号">-->
<!--          <el-input v-model="listQuery.bmsah"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="案件名称">-->
<!--          <el-input v-model="listQuery.ajmc"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="承办人">-->
<!--          <el-input v-model="listQuery.cbjcg"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button type="primary" @click="handleSearch">搜索</el-button>-->
<!--          <el-button @click="handleReset">重置</el-button>-->
<!--          <el-button type="primary" icon="el-icon-chat-dot-round" @click="showManualSendDialog" plain>手动发送消息</el-button>-->
<!--          <el-checkbox style="margin-left: 10px;" v-model="enableAuto" :true-label="1" :false-label="0">启用自动发送短信</el-checkbox>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--    </div>-->

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 新增标题区域 -->
      <div class="table-title" style="">
        <div class="title-decoration"></div>
        <h2>未审结案件情况</h2>
        <!-- 移除按钮 -->
        <!-- <el-button type="text" size="mini" @click="showAllSmsDialog">查看所有短信记录</el-button> -->
      </div>
      <el-form inline>
        <el-form-item label="部门受案号">
          <el-input v-model="listQuery.bmsah"></el-input>
        </el-form-item>
        <el-form-item label="案件名称">
          <el-input v-model="listQuery.ajmc"></el-input>
        </el-form-item>
        <el-form-item label="承办人">
          <el-input v-model="listQuery.cbjcg"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" icon="el-icon-chat-dot-round" @click="showManualSendDialog" plain>手动发送消息</el-button>
          <!-- 新增按钮位置，并修改样式 -->
          <el-button type="success" icon="el-icon-document" size="mini" @click="showAllSmsDialog" plain style="margin-left: 10px;">查看所有记录</el-button>
          <el-checkbox style="margin-left: 10px;" v-model="enableAuto" true-label="1" false-label="0" @change="handleEnableAutoChange">启用自动发送短信</el-checkbox>
        </el-form-item>
      </el-form>
<!--      <div style="margin-top: 5px;margin-bottom: 15px">-->
<!--        <el-checkbox v-model="tableCellStyleType.max" :true-label="1" :false-label="0">最大值</el-checkbox>-->
<!--        <el-checkbox v-model="tableCellStyleType.min" :true-label="1" :false-label="0">最小值</el-checkbox>-->
<!--        <el-checkbox v-model="tableCellStyleType.zero" :true-label="1" :false-label="0">0值</el-checkbox>-->
<!--        <el-button style="margin-left: 20px" plain type="primary" @click="caseStatuteOfLimitations($event)">案件时效</el-button>-->
<!--        <el-button type="primary" plain @click="focusPerformance($event)">案件绩效</el-button>-->
<!--        <el-button type="primary" plain @click="focusWarningColumn($event)">案件质量</el-button>-->
<!--      </div>-->
      <!-- :summary-method="getSummaries" -->
      <!-- 表格区域 -->
      <el-table
          :data="tableData"
          border
          max-height="80vh"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="getHeaderCellStyle"
          :header-cell-class-name="getHeaderCellClassName"
          :cell-style="cellStyle"
          ref="dataTable"
          row-key="bmsah"
          :expand-row-keys="expandedRowKeys"
          @expand-change="handleExpandChange"
          @cell-click="handleCellClick"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <div style="padding: 10px 20px;" v-loading="expandedRowLoading">
              <el-table :data="expandedRowDetails" border size="mini" style="width: 100%">
                <el-table-column prop="personName" width="130" label="收信人" align="center"></el-table-column>
                <el-table-column prop="phoneNumber" width="160" label="手机号" align="center"></el-table-column>
                <el-table-column prop="status" label="状态" width="130" align="center">
                  <template slot-scope="scope">
                    <el-tag type="success" v-if="scope.row.status == 1">成功</el-tag>
                    <el-tag type="error" v-else-if="scope.row.status == -1">失败</el-tag>
                    <el-tag type="info" v-else-if="scope.row.status == 0">未发送</el-tag>
                    <el-tag type="info" v-else="scope.row.status == null">无</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="sendTime" width="180" label="发送时间" align="center"></el-table-column>
                <el-table-column prop="type" width="150" label="发送类型" align="center">
                  <template slot-scope="scope">
                    <el-tag type="primary" v-if="scope.row.type == 'system'">系统</el-tag>
                    <el-tag type="info" v-else-if="scope.row.type == 'manual'">手动</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="level" width="150" label="时间" align="left" show-overflow-tooltip></el-table-column>
                <el-table-column prop="content" label="内容" align="left" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作" align="center" width="120">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="mini"
                      @click="handleResend(scope.row)">再次发送</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                style="text-align: center; margin-top: 10px;"
                background
                small
                layout="total, prev, pager, next"
                :total="detailListTotal"
                :page-size="detailListQuery.rows"
                :current-page.sync="detailListQuery.pageNo"
                @current-change="handleDetailCurrentChange(props.row)"
                @size-change="handleDetailSizeChange(props.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
            prop="ajmc"
            label="案件名称"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="bmsah"
            label="部门受案号"
            align="center"
        >
        </el-table-column>
        <el-table-column
            prop="ysay_aymc"
            label="移送案由"
            align="center"
        >
        </el-table-column>
        <el-table-column
            width="180"
            prop="cbjcg"
            label="承办人"
            align="center"
        >
        </el-table-column>
        <el-table-column
            width="180"
            prop="cbbm_mc"
            label="部门"
            align="center"
        >
        </el-table-column>
        <el-table-column
            width="150"
            prop="slrq"
            label="受理日期"
            align="center"
        >

          <template slot-scope="scope">
            <span>
              {{ formatData(scope.row.slrq) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            width="180"
            prop="status"
            label="最近发送情况"
            align="center"
        >
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status == 1">成功</el-tag>
            <el-tag type="error" v-else-if="scope.row.status == -1">失败</el-tag>
            <el-tag type="info" v-else-if="scope.row.status == 0">未发送</el-tag>
            <el-tag type="info" v-else="scope.row.status == null">无</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            width="180"
            prop="sendTime"
            label="最近发送时间"
            align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.sendTime == null ? '无' : scope.row.sendTime }}
            </span>
          </template>
        </el-table-column>
<!--        <el-table-column-->
<!--            width="180"-->
<!--            label="操作"-->
<!--            align="center"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <el-button type="text" @click.stop="senMessage">发送</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
            background
            layout="total,prev, pager, next"
            :total="total"
            :page-size="listQuery.rows"
            :current-page="listQuery.pageNo"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>
      <!-- 注释说明区域 -->
<!--      <div class="notes-area">-->
<!--        注：1.合计数中包括院领导受理报捕数、受理公诉数；院领导办结数以及未结数；2.省院九部经与一部和案管办协商沟通，明确目前省院通报的办案时长按照最高检统计口径不包括未检案件；3.审结率：1-[020101（第1行，第220列）-080201（第1行，第189列）]/[020101(第1行，第17列）+（第1行，第19列）-080201（第1行，第14列）-080201（第1行，第16列）]；15天内审结率、45天内审结率：190101（第3行，第1列）中办案时长15天内或45天内的件数/总的办结件数；4.详细明细可以点击数字查看。-->
<!--      </div>-->
    </div>

    <!-- 手动发送消息弹框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="manualSendDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      @close="resetManualSendForm"
    >
      <div class="header">
        <span>{{ dialogTitle }}</span>
        <i class="el-icon-close iccon-btn-close" @click="manualSendDialogVisible = false" />
      </div>
      <div class="content-wrapper">
      <el-form :model="manualSendMessageForm" :rules="manualSendFormRules" ref="manualSendForm" label-width="110px">
        <el-form-item label="案件名称" prop="caseName">
          <el-input v-model="manualSendMessageForm.caseName" placeholder="请输入案件名称" :disabled="isResendMode"></el-input>
        </el-form-item>
        <el-form-item label="部门受案号" prop="caseNumber">
          <el-input v-model="manualSendMessageForm.caseNumber" placeholder="请输入部门受案号" :disabled="isResendMode"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="personName">
          <el-input v-model="manualSendMessageForm.personName" placeholder="请输入姓名" :disabled="isResendMode"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="manualSendMessageForm.phoneNumber" placeholder="请输入手机号" :disabled="isResendMode"></el-input>
        </el-form-item>
        <el-form-item label="发送内容" prop="content">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入发送内容"
            v-model="manualSendMessageForm.content">
          </el-input>
          <!-- <div style="text-align: right; padding-top: 10px;">
             <el-button type="text" @click="insertTemplate">输入模板</el-button>
          </div> -->
        </el-form-item>
      </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="manualSendDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleManualSend">发 送</el-button>
      </span>
    </el-dialog>

    <!-- 新增：所有短信记录弹框 -->
    <el-dialog
      title="所有短信记录"
      :visible.sync="allSmsDialogVisible"
      fullscreen  
      :close-on-click-modal="false"
      @close="handleAllSmsDialogClose"
    >
      <div class="header">
        <span>所有短信记录</span>
        <i class="el-icon-close iccon-btn-close" @click="allSmsDialogClose" />
      </div>
      <div class="content-wrapper">
      <!-- 搜索区域 -->
      <el-form :model="allSmsQuery" inline>
        <el-form-item label="收信人">
          <el-input v-model="allSmsQuery.personName" placeholder="请输入收信人"></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="allSmsQuery.phoneNumber" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="allSmsQuery.status" @change="handleAllSmsSearch">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="1">成功</el-radio-button>
            <el-radio-button label="-1">失败</el-radio-button>
            <el-radio-button label="0">未发送</el-radio-button>
          </el-radio-group>
        </el-form-item>
         <el-form-item label="发送类型">
          <el-radio-group v-model="allSmsQuery.type" @change="handleAllSmsSearch">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="system">系统</el-radio-button>
            <el-radio-button label="manual">手动</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAllSmsSearch">搜索</el-button>
          <el-button @click="handleAllSmsReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table :data="allSmsTableData" border v-loading="allSmsLoading" size="mini" style="width: 100%; margin-top: 10px;">
        <el-table-column prop="personName" width="130" label="收信人" align="center"></el-table-column>
        <el-table-column prop="phoneNumber" width="160" label="手机号" align="center"></el-table-column>
        <el-table-column prop="caseName" label="案件名称" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="caseNumber" width="180" label="部门受案号" align="center"></el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status == 1">成功</el-tag>
            <el-tag type="error" v-else-if="scope.row.status == -1">失败</el-tag>
            <el-tag type="info" v-else-if="scope.row.status == 0">未发送</el-tag>
            <el-tag type="info" v-else="scope.row.status == null">无</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" width="180" label="发送时间" align="center"></el-table-column>
        <el-table-column prop="type" width="100" label="发送类型" align="center">
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.type == 'system'">系统</el-tag>
            <el-tag type="info" v-else-if="scope.row.type == 'manual'">手动</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" width="150" label="时间" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column prop="content" label="内容" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleResend(scope.row)">再次发送</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        style="text-align: center; margin-top: 15px;"
        background
        layout="total, prev, pager, next, sizes"
        :total="allSmsTotal"
        :page-size="allSmsQuery.rows"
        :current-page.sync="allSmsQuery.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleAllSmsCurrentChange"
        @size-change="handleAllSmsSizeChange"
      />
    </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="allSmsDialogClose">关 闭</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {
  getDepartmentAndUser,
  getStatisticsData,
  getTableHeader,
  getDetailTableData
} from "@/api/api/durationStatistics";
// import * as XLSX from 'xlsx';
import * as XLSX from 'sheetjs-style'
import { calculateColWidth, calculateWidth, wsInit } from "@/utils/excelUtils"
import { rulePageList, warningDepartmentAndUser } from "@/api/api/warningInfo"
import WarningDetailDialog from "@/views/modules/warningInfo/detailDialog.vue"
import { listCbrpcjgtj } from "@/api/api/qualityEvaluation"
import { formatEndTime, formatStartTime } from "@/views/modules/durationStatistics/timeUtils"
import { bakPjdcData } from "@/views/modules/durationStatistics/bakJson"
import ajzlpjdcDialog from "@/views/modules/ajzlpjdc/ajzlpjdcDialog.vue"
import { smsPageList, sendManualSms, smsSave, smsInfoPageList } from "@/api/api/sms"
import { autoSendEnable } from "../../../api/api/sms";
// import { departmentAndUserList, statisticsData, tableHeader } from "@/views/modules/durationStatistics/bakJson"

export default {
  name: "smsIndex",
  data() {
    // 手机号格式校验 (简单示例)
    const validatePhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'));
      } else if (!/^1[3-9]\d{9}$/.test(value)) { // 简单校验中国手机号格式
        callback(new Error('请输入正确的手机号格式'));
      } else {
        callback();
      }
    };
    return {
      detailLoading: false,
      WarningDialogVisible: false,
      pjdcDialogVisible: false,
      ruleDetailLoading: false,
      ruleDialogVisible: false,
      ruleTableData: [],
      tableCellStyleType: {
        max: 0,
        min: 0,
        zero: 0
      },
      listQuery: {
        pageNo: 1,
        rows: 10,
        ajmc: '',
        bmsah: '',
        cbjcg: ''
      },
      ruleListQuery: {
        pageNo: 1,
        rows: 10,
        sort: "",
        order: "",
        filters: "",
        searchFilters: ""
      },
      ruleTotal: 0,
      loading: true,
      searchForm: {
        timeType: 2, // 1: 单月, 2: 时间段
        singleMonth: "202501",
        monthrange: ["202501", "202502"]
      },
      tableData: [],
      tableHeader: [],
      columns: [],
      enableAuto: 0,
      dialogVisible: false,
      rowCbrgh: '',
      currentCell: {
        department: "",
        cbr: "",
        columnTitle: "",
        value: ""
      },
      detailTableData: [],
      detailTableAllData: [],
      activeName: "",
      WarningActiveName: "",
      pjdcActiveName: "",
      total: 0,
      departmentAndUserList: [],
      statisticsData: [],
      focusedColumn: '', // 新增：用于跟踪当前聚焦的列
      nowTime: '',
      manualSendDialogVisible: false, // 控制手动发送弹框的显示
      manualSendMessageForm: {        // 手动发送表单数据
        caseName: '',
        caseNumber: '',
        personName: '',
        phoneNumber: '',
        content: ''
      },
      // 添加表单校验规则
      manualSendFormRules: {
        caseName: [
          { required: true, message: '请输入案件名称', trigger: 'blur' }
        ],
        caseNumber: [
          { required: true, message: '请输入部门受案号', trigger: 'blur' }
        ],
        personName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, validator: validatePhoneNumber, trigger: 'blur' } // 使用自定义校验
        ],
        content: [
          { required: true, message: '请输入发送内容', trigger: 'blur' }
        ]
      },
      expandedRowDetails: [],
      expandedRowLoading: false,
      detailListQuery: {
        pageNo: 1,
        rows: 5,
        filters: ''
      },
      detailListTotal: 0,
      expandedRowKeys: [],
      isResendMode: false, // <-- 新增：标记是否为再次发送模式
      dialogTitle: '手动发送消息', // <-- 新增：弹框标题

      // --- 新增：所有短信记录弹框相关数据 ---
      allSmsDialogVisible: false,
      allSmsLoading: false,
      allSmsQuery: {
        pageNo: 1,
        rows: 10,
        personName: '',
        phoneNumber: '',
        status: '', // '' 代表全部
        type: '',   // '' 代表全部
        // searchFilters 将在方法中动态构建
      },
      allSmsTableData: [],
      allSmsTotal: 0,
      // --- 结束新增 ---
    };
  },
  watch: {
    // activeName(activeName) {
    //   if (activeName) {
    //     // 每次切换详情的时候，重置分页
    //     this.listQuery.page = 1;
    //     // 获取详情数据
    //     this.getDetailTableData();
    //     this.getDetailTableAllData();
    //   }
    // }
  },
  async mounted() {
    // this.nowTime = ("" + new Date().getFullYear().toString() + String(new Date().getMonth() + 1).padStart(2, '0'))
    // this.nowTimeLast = ("" + new Date().getFullYear().toString() + String(new Date().getMonth()).padStart(2, '0'))
    // this.searchForm.singleMonth = this.nowTime
    // this.searchForm.monthrange = [this.nowTimeLast,this.nowTime]
    // document.title = "一审公诉案件办案时长情况";
    // // 获取字段
    // await this.getTableHeader();
    // 获取部门和人员列表，并按照动态字段进行赋值
    await this.getList();
  },
  methods: {
    formatData(dateStr) {
      if (!dateStr) return ''; // 处理空值
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，补零
      const day = String(date.getDate()).padStart(2, '0'); // 补零
      return `${year}年${month}月${day}日`; // 修改格式
    },
    handleEnableAutoChange(val){
        autoSendEnable({enable: val}).then(res=>{
            if(res.state){
              this.$notify({
                title: val == '1' ? '开启': '关闭',
                message: val == '1' ? '开启自动发送短信': '关闭自动发送短信',
                position: "bottom-right",
                type: "success"
              });
            }
        })
    },
    async getList() {
      this.loading = true;
      try { // 添加 try...catch
        const data = await smsPageList(this.listQuery);
        if (data.state) {
          this.tableData = data.data;
          this.total = data.total;
          this.enableAuto = data.enableAuto;
        } else {
          this.$message.error(data.message || '获取列表失败'); // 提示错误信息
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) { // 捕获请求错误
        console.error("获取列表失败:", error);
        this.$message.error('请求列表数据时发生错误');
        this.tableData = [];
        this.total = 0;
      } finally { // 确保 loading 总是被设置为 false
        this.loading = false;
      }
    },
    getMaxAndMin(prop) {
      const values = this.tableData.filter(i => i.department !== '总计' ).map(item => Number(item[prop]));
      const filter = values.filter(i => i !== 0)
      return {
        max: Math.max(...filter),
        min: Math.min(...filter)
      };
    },
    isMax(value, prop) {
      let max = this.getMaxAndMin(prop).max
      return value == max;
    },
    isMin(value, prop) {
      let min = this.getMaxAndMin(prop).min
      return value == min;
    },
    handleTimeTypeChange() {
      // 切换时间类型时清空已选值
      if (this.searchForm.timeType === 1) {
        this.searchForm.monthrange = ["202501", "202501"];
      } else {
        this.searchForm.singleMonth = "202501";
      }
      this.getList()
    },
    handleSearch() {
      // 实现查询逻辑
      this.getList();
    },
    handleReset() {

      this.listQuery = {
        pageNo: 1,
        rows: 10,
        ajmc: '',
        bmsah: '',
        cbjcg: ''
      }
      this.getList();
    },
    handleCurrentChange(page) {
      this.listQuery.pageNo = page;
      this.getList();
    },
    handleSizeChange(size) {
      this.listQuery.rows = size;
      this.getList();
    },
    ruleHandleCurrentChange(page) {
      this.ruleListQuery.pageNo = page;
      this.getRuleTableData();
    },
    ruleHandleSizeChange(size) {
      this.ruleListQuery.rows = size;
      this.getRuleTableData();
    },
    ruleReset() {
      this.ruleListQuery.gzmc = ''
      this.ruleListQuery.pageNo = 1
      this.getRuleTableData()
    },
    getRuleTableData() {
      rulePageList(this.ruleListQuery).then(res=>{
        this.ruleTotal = res.total
        this.ruleTableData = res.rows
      })
    },
    async getDetailTableData() {
      this.detailLoading = true;
      const { data } = await getDetailTableData(
          this.activeName,
          this.listQuery
      );
      this.detailTableData = data.list;
      this.total = data.total;
      this.detailLoading = false;
    },
    allSmsDialogClose(){
      this.handleAllSmsDialogClose()
      this.allSmsDialogVisible = false
    },
    WarningCloseDialog() {
      this.WarningDialogVisible = false;
      this.WarningActiveName = "";
      this.rowData = {};
    },
    pjdcCloseDialog() {
      this.pjdcDialogVisible = false;
      this.pjdcActiveName = "";
      this.pjdcRowData = {};
    },
    showRuleDialog(type) {
      this.ruleDialogVisible = true;
      this.ruleListQuery.page = 1;
      this.ruleListQuery.type = type;
      this.getRuleTableData()
    },
    ruleCloseDialog() {
      this.ruleDialogVisible = false;
    },
    closeDialog() {
      this.dialogVisible = false;
      this.activeName = "";
      this.detailTableAllData = [];
      this.detailTableData = [];
    },
    //修改列方法
    cellStyle({row, column}) {
      // 基础样式对象
      let style = {}

      return style
    },
    // 修改表头样式方法
    getHeaderCellStyle({ column }) {
      // 基础样式
      const style = {
        color: '#fff',
        fontWeight: 'bold',
        background: '#409EFF'
        // background: '#d60000'
      }
      return style
    },
    // 返回特定表头的类名
    getHeaderCellClassName({ column }) {
      if (column.property === '省平台规则预警' || column.property === '本院规则预警') {
        return 'rule-header-cell'; // 为目标列返回类名
      }
      return ''; // 其他列返回空字符串
    },
    // 表头点击事件处理
    handleHeaderClick(column) {
      if (column.property === '省平台规则预警') {
        this.showRuleDialog('省院');
      }
      if (column.property === '本院规则预警') {
        this.showRuleDialog('本院');
      }
    },
    focusPerformance(event) {},
    caseStatuteOfLimitations(event) {
      event.stopPropagation()
      this.focusedColumn = '案件时效'

      this.$nextTick(() => {
        const table = this.$refs.dataTable
        const tableEl = table.$el
        const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper')
        const headerWrapper = tableEl.querySelector('.el-table__header-wrapper')

        if (bodyWrapper && headerWrapper) {
          const maxScroll = bodyWrapper.scrollWidth - bodyWrapper.clientWidth
          this.smoothScroll(bodyWrapper, 1)
          this.smoothScroll(headerWrapper, 1)
          // this.smoothScroll(bodyWrapper, 300)
          // this.smoothScroll(headerWrapper, 300)
        }
      })
    },
    handleGlobalClick() {
      this.focusedColumn = ''
    },
    // 修改 showManualSendDialog 方法
    showManualSendDialog() {
      this.isResendMode = false; // 确保不是再次发送模式
      this.dialogTitle = '手动发送消息'; // 设置正确标题
      // 重置表单（确保 resetManualSendForm 会清空所有字段）
      this.resetManualSendForm(); // 调用重置方法
      this.manualSendDialogVisible = true; // 打开弹框
    },
    // 关闭弹框时重置表单 (可选)
    resetManualSendForm() {
       // 清除校验状态
       this.$refs.manualSendForm && this.$refs.manualSendForm.clearValidate();
       // 重置表单字段
       this.manualSendMessageForm = {
         caseName: '',
         caseNumber: '',
         personName: '',
         phoneNumber: '',
         content: ''
       };
       this.isResendMode = false; // <-- 新增：重置模式标记
       this.dialogTitle = '手动发送消息'; // <-- 新增：重置标题
    },
    // 插入模板内容
    insertTemplate() {

      const template = `【温馨提醒，您承办的${this.manualSendMessageForm.caseName}（将满一个月，已满一个半月，已满3个月，已满5个月)，请抓紧办理。】`;
      // 可以选择追加或者替换，这里选择追加
      this.manualSendMessageForm.content = template;
    },

    // 处理手动发送逻辑
    handleManualSend() {
      this.$refs.manualSendForm.validate(async (valid) => { // validate 会自动使用定义的 rules
        if (valid) {
          try {
            // 显示加载状态 (如果需要)
            // this.loading = true; // 可以考虑为发送操作单独设置 loading 状态
             const formToSubmit = {
                ...this.manualSendMessageForm,
                type: 'manual' // 显式设置类型为手动
            };

            // 如果是再次发送，可能需要传递原始消息的ID或其他标识符，取决于API设计
            // if (this.isResendMode && this.manualSendMessageForm.id) {
            //    formToSubmit.originalMsgId = this.manualSendMessageForm.id;
            // }

            smsSave(formToSubmit).then(res=>{ // 使用修改后的表单数据
              if(res.state){
                this.$message.success('发送成功');
                this.manualSendDialogVisible = false;
                // 发送成功后，可能需要刷新相关列表
                if (this.allSmsDialogVisible) {
                  this.getAllSmsData(); // 如果所有记录弹框开着，刷新它
                }
                 // 检查当前展开行是否与发送的短信相关，如果相关则刷新
                 const currentExpandedKey = this.expandedRowKeys[0];
                 if (currentExpandedKey && currentExpandedKey === formToSubmit.caseNumber) {
                    // 延迟一下再刷新，确保数据库更新完成
                    setTimeout(() => {
                         this.getExpandedDetails(currentExpandedKey);
                    }, 500);
                 }
                 this.getList(); // 刷新主列表，因为最近发送状态可能改变

              } else {
                  this.$message.error(res.message || '发送失败');
              }

            }).catch(err=>{
              console.log(err)
               this.$message.error('发送失败，请稍后重试');
              // this.manualSendDialogVisible = false; // 发生错误时不一定关闭弹窗
            })


          } catch (error) {
            console.error("发送失败:", error);
            this.$message.error('发送过程中出现错误');
          } finally {
            // this.loading = false; // 关闭加载状态
          }
        } else {
          console.log('表单验证失败');
          this.$message.warning('请检查表单信息是否完整且正确'); // 提示用户检查
          return false;
        }
      });
    },
    async handleExpandChange(row, expandedRows) {
      // expandedRows 包含了当前所有逻辑上"应该"展开的行。
      // 由于我们希望只展开一个，我们需要决定哪个是"当前"要展开的行。
      // 通常，expandedRows 在单行展开模式下，要么包含一个元素（新展开的行），要么为空（刚折叠了最后一行）。
      // 如果由于某种原因（例如快速点击）expandedRows 包含多个，我们选择最后一个作为目标。

      let targetRowKey = null;
      if (expandedRows.length > 0) {
        // 获取最后一个被展开的行（通常也是唯一一个）
        const targetRow = expandedRows[expandedRows.length - 1];
        targetRowKey = targetRow.bmsah;
        console.log(`handleExpandChange: 目标展开行 ${targetRowKey}`);
      } else {
        console.log(`handleExpandChange: 所有行已折叠`);
      }

      // 1. 更新 expandedRowKeys 以强制只展开目标行（或都不展开）
      this.expandedRowKeys = targetRowKey ? [targetRowKey] : [];

      // 2. 根据目标行是否存在来加载或清除数据
      if (targetRowKey) {
        // 如果当前展开的 bmsah 与目标不符，或者需要重新加载
         console.log('加载/更新数据 for:', targetRowKey);
         // --- 修改：确保detailListQuery.filters有值，否则设为空对象字符串 ---
         const filterObj = { caseNumber: targetRowKey };
         this.detailListQuery.filters = JSON.stringify(filterObj);
         this.detailListQuery.pageNo = 1; // 每次展开都重置页码
         await this.getExpandedDetails(targetRowKey);
      } else {
         // 如果没有目标行（所有行都折叠了），清除数据
         console.log('清除详情数据');
         this.expandedRowDetails = [];
         this.detailListTotal = 0;
         // --- 修改：清除 bmsah 过滤条件 ---
         this.detailListQuery.filters = JSON.stringify({}); // 或根据API要求设为 ''
         this.detailListQuery.pageNo = 1; // 重置页码
      }
    },
    async getExpandedDetails(bmsah) {
      this.expandedRowLoading = true;
      try {
        // --- 移除模拟延迟 ---
        // await new Promise(resolve => setTimeout(resolve, 500));

        // 构造查询参数，确保 filters 包含 caseNumber
        const queryParams = {
          ...this.detailListQuery,
          // 确保 filters 是一个有效的 JSON 字符串
          filters: JSON.stringify({ ...(this.detailListQuery.filters ? JSON.parse(this.detailListQuery.filters) : {}), caseNumber: bmsah })
        };


        const data = await smsInfoPageList(queryParams) // 使用构造好的参数
        if(data.state){
          this.expandedRowDetails = data.rows
          this.detailListTotal = data.total
        }else{
           this.$message.error(data.message || '获取短信记录失败');
          this.expandedRowDetails = []
          this.detailListTotal = 0
        }


      } catch (error) {
        console.error("获取展开行详情失败:", error);
        this.$message.error('获取短信记录失败');
        this.expandedRowDetails = [];
        this.detailListTotal = 0;
      } finally {
        this.expandedRowLoading = false;
      }
    },
    handleDetailCurrentChange(row) {
       console.log(`详细列表页码改变: ${this.detailListQuery.pageNo}`);
       // --- 修改：直接调用 getExpandedDetails，不再检查 bmsah ---
       this.getExpandedDetails(row.bmsah);
       // if (this.detailListQuery.bmsah === row.bmsah) { // 移除检查
       //     this.getExpandedDetails(row.bmsah);
       // } else {
       //     console.warn("展开行 bmsah 与当前查询不匹配！");
       // }
    },
    handleDetailSizeChange(row) {
      console.log(`详细列表每页数量改变: ${this.detailListQuery.rows}`);
      this.detailListQuery.pageNo = 1;
      // --- 修改：直接调用 getExpandedDetails，不再检查 bmsah ---
      this.getExpandedDetails(row.bmsah);
      // if (this.detailListQuery.bmsah === row.bmsah) { // 移除检查
      //      this.getExpandedDetails(row.bmsah);
      //  } else {
      //      console.warn("展开行 bmsah 与当前查询不匹配！");
      //  }
    },
    handleCellClick(row, column, cell, event) {
       // 只处理非 expand 列的点击
       if (column.type !== 'expand') {
          console.log(`CellClick: 触发 toggleRowExpansion for ${row.bmsah}`);
          // 调用 el-table 的方法来切换展开状态，这将触发 @expand-change
          this.$refs.dataTable.toggleRowExpansion(row);
       }
    },
    // --- 修改：处理再次发送，移除 parentRow 参数 ---
    handleResend(detailRow) {
      // 检查 detailRow 是否有效
      if (!detailRow || typeof detailRow !== 'object') {
        console.error("handleResend: 无效的 detailRow 数据", detailRow);
        this.$message.error("无法获取短信详情，请重试。");
        return;
      }
      console.log("再次发送:", detailRow);

      this.isResendMode = true; // 设置为再次发送模式
      this.dialogTitle = '再次发送'; // 修改标题

      // 填充表单，确保字段存在且正确赋值
      this.manualSendMessageForm = {
        caseName: detailRow.caseName || '', // 从详情中获取
        caseNumber: detailRow.caseNumber || '', // 从详情中获取
        personName: detailRow.personName || '',
        phoneNumber: detailRow.phoneNumber || '',
        content: detailRow.content || '',
        // 如果需要，可以保留原始消息的ID或其他信息
        id: detailRow.id || null // 假设详情对象中有id
      };


      this.manualSendDialogVisible = true; // 打开弹框

      // 清除可能存在的旧校验状态
      this.$nextTick(() => {
        this.$refs.manualSendForm && this.$refs.manualSendForm.clearValidate();
      });
    },
    // --- 结束修改 ---

    // --- 新增：所有短信记录弹框方法 ---
    showAllSmsDialog() {
      this.allSmsDialogVisible = true;
      this.handleAllSmsReset(); // 打开时重置并加载第一页数据
    },

    handleAllSmsDialogClose() {
       // 清理工作，比如重置查询条件（可选）
       this.allSmsQuery = {
         pageNo: 1,
         rows: 10,
         personName: '',
         phoneNumber: '',
         status: '',
         type: '',
       };
       this.allSmsTableData = [];
       this.allSmsTotal = 0;
    },

    async getAllSmsData() {
      this.allSmsLoading = true;
      try {
        // 构建 searchFilters (只包含 personName 和 phoneNumber)
        const searchFilters = {};
        if (this.allSmsQuery.personName) {
          searchFilters.personName = this.allSmsQuery.personName;
        }
        if (this.allSmsQuery.phoneNumber) {
          searchFilters.phoneNumber = this.allSmsQuery.phoneNumber;
        }

        // 构建 filters (包含 status 和 type, 如果它们有值)
        const filters = {};
        if (this.allSmsQuery.status !== '') { // 注意判断空字符串
          filters.status = this.allSmsQuery.status;
        }
        if (this.allSmsQuery.type !== '') { // 注意判断空字符串
          filters.type = this.allSmsQuery.type;
        }


        const params = {
          pageNo: this.allSmsQuery.pageNo,
          rows: this.allSmsQuery.rows,
          // 只有当 searchFilters 不为空时才传递
          searchFilters: Object.keys(searchFilters).length > 0 ? JSON.stringify(searchFilters) : undefined,
          // 只有当 filters 不为空时才传递
          filters: Object.keys(filters).length > 0 ? JSON.stringify(filters) : undefined // 将 status 和 type 放到 filters
        };

        const data = await smsInfoPageList(params);
        if (data.state) {
          this.allSmsTableData = data.rows;
          this.allSmsTotal = data.total;
        } else {
          this.$message.error(data.message || '获取所有短信记录失败');
          this.allSmsTableData = [];
          this.allSmsTotal = 0;
        }
      } catch (error) {
        console.error("获取所有短信记录失败:", error);
        this.$message.error('请求所有短信记录时发生错误');
        this.allSmsTableData = [];
        this.allSmsTotal = 0;
      } finally {
        this.allSmsLoading = false;
      }
    },

    handleAllSmsSearch() {
      this.allSmsQuery.pageNo = 1; // 搜索时重置到第一页
      this.getAllSmsData();
    },

    handleAllSmsReset() {
      this.allSmsQuery = {
        ...this.allSmsQuery, // 保留 pageNo 和 rows
        personName: '',
        phoneNumber: '',
        status: '',
        type: '',
        pageNo: 1, // 重置页码
      };
      this.getAllSmsData();
    },

    handleAllSmsCurrentChange(page) {
      this.allSmsQuery.pageNo = page;
      this.getAllSmsData();
    },

    handleAllSmsSizeChange(size) {
      this.allSmsQuery.rows = size;
      this.allSmsQuery.pageNo = 1; // 改变每页大小时重置到第一页
      this.getAllSmsData();
    }
    // --- 结束新增 ---
  }
};
</script>

<style lang="scss" scoped>
.duration-statistics {
  background-color: #f5f7fa;
  min-height: 90vh;

  .filter-section {
    background-color: #fff;
    padding: 16px;
    border-radius: 4px;
    //margin-bottom: 16px;

    .filter-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-group {
      display: flex;
      gap: 12px;
      flex: 1;

      .wide-select {
        width: 240px;
      }

      .normal-select {
        width: 120px;
      }

      .time-select {
        display: flex;
        align-items: center;
        gap: 12px;

        .date-picker {
          width: 240px;
        }
      }
    }

    .data-type-switch {
      display: flex;
      gap: 16px;

      .switch-item {
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 4px;
        color: #606266;

        &.active {
          background-color: #e6f2ff;
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .analysis-options {
      display: flex;
      gap: 24px;
      align-items: center;

      .option-item {
        cursor: pointer;
        color: #606266;

        &.active {
          color: #409eff;
        }

        i {
          margin-right: 4px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      align-items: center;

      .search-input {
        width: 200px;
      }

      .el-button {
        padding: 8px 16px;
      }
    }
  }

  .table-section {
    background-color: #fff;
    padding-right: 16px;
    padding-left: 16px;
    border-radius: 4px;
    height: 81vh; /* 调整高度以适应屏幕 */
    display: flex; /* 使用 flex 布局 */
    flex-direction: column; /* 垂直排列 */

    .table-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px; /* 减少间距 */
      margin-top: 15px;  /* 增加顶部间距 */
      flex-shrink: 0; /* 防止标题区域被压缩 */


      .title-decoration {
        width: 4px;
        height: 20px;
        background-color: #409eff;
        margin-right: 8px;
        border-radius: 2px;
      }

      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        margin: 0;
         margin-right: auto; /* 将按钮推到右侧 */
      }
       .el-button { /* 给按钮一些左边距 */
         margin-left: 10px;
       }
    }
     .el-form { /* 搜索表单 */
       flex-shrink: 0; /* 防止表单被压缩 */
       margin-bottom: 10px; /* 表单和表格间距 */
     }

     .el-table { /* 表格 */
       flex-grow: 1; /* 让表格填充剩余空间 */
       overflow: auto; /* 如果内容过多，允许表格滚动 */
       margin-bottom: 0; /* 移除默认的 el-table 下边距 */
     }


     .pagination-wrapper {
       flex-shrink: 0; /* 防止分页被压缩 */
       text-align: right;
       padding-top: 10px; /* 与表格的间距 */
       padding-bottom: 10px; /* 底部留白 */
     }
  }

  .notes-area {
    margin-top: 16px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    line-height: 24px;
    font-size: 16px;
    color: #4f5e7b;
  }
}

.cell-content {
  display: inline-block;
  width: 100%;
  cursor: pointer;

  //.max-num {
  //  //color: #2487fa;
  //  background: #2487fa;
  //  color: #fff;
  //}

  //.max-num:hover {
  //  font-weight: bold;
  //}


  //.min-num {
  //  //color: red;
  //  background: orange;
  //  color: #fff;
  //}

  //.min-num:hover {
  //  font-weight: bold;
  //}

}
.calculation-rule {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .rule-label {
    color: #606266;
    font-weight: 500;
  }

  .rule-content {
    color: #303133;
    line-height: 1.5;
  }
}

// 为特定表头添加 hover 效果
::v-deep .el-table__header-wrapper th {
  &.is-leaf { // 只针对最底层的表头单元格
    div.cell:hover {
      // 检查列的 property 是否匹配
      // 注意：这种方式依赖于 Element UI 内部结构，可能不够稳定
      // 如果 getHeaderCellStyle 能稳定添加 class 会更好，但目前先用这种方式
    }
  }
}

// 通过 getHeaderCellStyle 添加的 cursor 样式已经生效
// 这里我们只添加 hover 时的背景色变化
// 使用 getHeaderCellClassName 添加的类名来定位
::v-deep .el-table__header-wrapper th.rule-header-cell {
  &:hover {
    background-color: #66b1ff !important; // 悬停时改变背景色
  }
}

// 可以在这里为新添加的弹框或按钮添加特定样式
.el-dialog__body .el-form-item:last-child {
  margin-bottom: 0; // 避免最后一个表单项底部有过多间距
}

// 确保模板按钮在文本域右下角
.el-form-item__content {
  position: relative; // 使得内部绝对定位的元素相对于它定位

  .el-textarea {
    // 可能需要调整 textarea 的样式，如果按钮覆盖了 resize handle
    // margin-bottom: 25px; // 为按钮留出空间 (如果模板按钮存在)
  }

  div[style*="text-align: right"] {
    position: absolute;
    bottom: 0px; // 定位到容器底部
    right: 10px; // 定位到容器右侧
  }
}

/* 新增：为所有短信记录弹框内的表格添加一些样式 */
.el-dialog {
  .el-table th { /* 弹框内表格表头 */
    background-color: #f5f7fa; /* 淡一点的背景色 */
    color: #606266;
  }
  .el-form-item { /* 弹框内搜索项间距调整 */
      margin-bottom: 10px;
  }
}

</style>
