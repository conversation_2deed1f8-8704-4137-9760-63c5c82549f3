<template>
  <div class="page flex-col">
    <div class="block_1 flex-col">
      <div class="section_1 flex-col">
        <img
          class="image_1 mx-auto mt-20 mb-4"
          referrerpolicy="no-referrer"
          src="./img/slices/title_1.png"
        />
        <span class="text_1 text-center mb-8">公益诉讼监督治理中心</span>
        <div class="w-3/4 mx-auto">
          <div class="box_1 flex-row justify-between">
            <div
              class="box_3 hoverable-button flex-row cursor-pointer flex items-center justify-center"
              style="background-color: #ebeef5;"
            >
              <img
                class="label_1"
                referrerpolicy="no-referrer"
                src="./img/slices/ai.png"
              />
              <span class=" text-group_1 pl-2 ">深度思考</span>
            </div>
            <div
              class="box_3 flex-row cursor-pointer flex items-center justify-center"
            >
              <img
                class="label_2"
                referrerpolicy="no-referrer"
                src="./img/slices/search.png"
              />
              <span class="text-group_2 pl-2">站内搜索</span>
            </div>
            <div
              class="box_3 hoverable-button flex-row cursor-pointer flex items-center justify-center"
              @click="showChatIn"
              style="background-color: #ebeef5;"
            >
              <img
                class="label_2"
                referrerpolicy="no-referrer"
                src="./icon/audio.png"
              />
               <span class="text-group_2 pl-2">音频转文字</span>
            </div>
          </div>
          <div class="my-5">
            <el-input
              placeholder="请输入资源名称等关键信息"
              v-model="searchKeyword"
              class="search-input"
            >
              <el-select
                v-model="searchType"
                slot="prepend"
                placeholder="请选择"
                class="bg-white"
                style="width: 140px;height: 80%;border: none;border-right: 1px solid #ececec;"
              >
                <el-option
                  v-for="item in searchTpyeList"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
              <div slot="append" class="append-wrapper">
                <img
                  @click="handleSearch"
                  class="m-auto cursor-pointer"
                  src="./img/slices/search-btn.png"
                />
              </div>
            </el-input>
          </div>

          <div class="box_5 flex-col">
            <div class="text-wrapper_1 flex-row">
              <span
                v-for="(menu, index) in menus"
                :key="index"
                :class="['text_4', { active: currentMenu === menu.name }]"
                @click="handleMenuClick(menu)"
              >
                {{ menu.name }}
              </span>
            </div>
            <div class=" flex-row flex mt-10 px-6 flex-wrap">
              <div
                v-for="(item, index) in currentSubMenus"
                :key="index"
                class="image-text_3 flex-row flex justify-start mb-4 items-center"
                style="width:30%"
                @click="handleSubMenuClick(item)"
              >
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="./img/slices/dot.png"
                />
                <span class="pl-2">{{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
        :visible="dialogVisible"
        width="60%"
        :close-on-click-modal="false"
        :before-close="handleClose"
        top="10vh"
    >
      <div class="dialog-wrapper">
          <FileAddForm
            @close="handleClose"
          >
          </FileAddForm>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { unitySkipApp } from "@/api/system/login";
import { getToken, removeToken } from "@/utils/auth";
import FileAddForm from "@/views/modules/systemFile/fileAddForm.vue"
export default {
  components: { FileAddForm },
  data() {
    return {
      searchTpyeList: window.globalConfig.resourceSearch.type,
      searchType: "全部资源",
      searchKeyword: "",
      currentMenu: "数据中心",
      dialogVisible: false,
      fileList: [],
      action: API.baseAPI + "/rest/system/file/upload",
      menus: window.globalConfig.indexMenus
      // menus: [
      //   {
      //     name: "数据中心",
      //     icon: require("./img/icon/数据中心.png"),
      //     children: [
      //       { name: "数据检索", mode: "dataShow" },
      //       { name: "判决书库", mode: "JudicialDocument" },
      //       { name: "阅览室" ,url: "fileSystem"}
      //     ]
      //   },
      //   {
      //     name: "剥夺政治权利监督",
      //     icon: require("./img/icon/场景中心.png"),
      //     children: [
      //       { name: "案发区域预警", mode: "earlyWarning" },
      //       { name: "剥夺政治权利审监抗类案监督", mode: "bdzzqlsjk" },
      //       { name: "涉赌人员拒执犯罪监督场景", mode: "sdryjjzs" },
      //       { name: "护水系统", cockpit: "hsxt" },
      //       { name: "监所智慧纠违系统", cockpit: "jszhjwxt" }
      //     ]
      //   },
      //   {
      //     name: "绩效评估",
      //     icon: require("./img/icon/绩效评估.png"),
      //     url: "/durationStatistics"
      //   },
      //   {
      //     name: "办案辅助",
      //     icon: require("./img/icon/办案辅助.png"),
      //     children: [
      //       { name: "统一量刑填报", mode: "tylxglquery" },
      //       { name: "统一量刑管理", mode: "tylxglmanage" },
      //       // { name: "刑期计算工具", url: "calculationSentence" },
      //       { name: "信息推送", url: "sms" }
      //     ]
      //   },
      //   {
      //     name: "驾驶舱",
      //     icon: require("./img/icon/驾驶舱.png"),
      //     children: [
      //       { name: "首页驾驶舱", cockpit: "index" },
      //       { name: "数据中心驾驶舱", cockpit: "dataCenter" },
      //       { name: "案管平台", cockpit: "ajzlzb" }
      //     ]
      //   },
      //   {
      //     name: "常用工具",
      //     icon: require("./img/icon/驾驶舱.png"),
      //     children: [
      //       { name: "刑期计算工具",url: "calculationSentence" },
      //       { name: "文件上传", dialog: "dataCenter" }
      //       // { name: "下载文件", url: "fileSystem" }
      //     ]
      //   }
      // ]
    };
  },
  computed: {
    currentSubMenus() {
      const currentMenu = this.menus.find(
        menu => menu.name === this.currentMenu
      );
      return currentMenu.children || [];
    }
  },
  methods: {
    showChatIn() {
      window.open(
          window.location.origin + window.location.pathname + "#" + '/audioChat'
      );
    },
    logout() {
      this.$confirm("登录过期,您可以停留在次页面,或者重新登录", "确认退出", {
        confirmButtonText: "重新登录",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        removeToken();
        window.location.href = unityLogin;
      });
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSearch() {
      if (this.searchKeyword) {
        const {
          ak: configAk,
          sk: configSk,
          url: configUrl,
          suffix: suffix,
          type: type
        } = window.globalConfig.resourceSearch;
        const token = getToken();
        unitySkipApp(token, configAk).then(res => {
          if (res.code === 2000) {
            let href = "#";
            href =
              configUrl +
              "?query=" +
              this.searchKeyword +
              "&type=" +
              this.searchType +
              "&ticket=" +
              res.ticket;
            window.open(href);
          } else {
            this.logout();
          }
        });
      } else {
        this.$message.warning("请输入关键字");
      }
    },
    handleMenuClick(row) {
      if (row.url) {
        window.open(
          window.location.origin + window.location.pathname + "#" + row.url
        );
        return;
      }
      this.currentMenu = row.name;
    },
    handleSubMenuClick(row) {
      if (row.url) {
        window.open(
          window.location.origin + window.location.pathname + "#" + row.url
        );
      } else if (row.mode) {
        this.getOtherTicket(window.globalConfig[row.mode]);
      } else if (row.cockpit) {
        const url = window.globalConfig.jsc[row.cockpit];
        window.open(url);
      } else if (row.dialog) {
        this.dialogVisible = true
      } else {
        this.$message.warning("没有配置地址");
      }
    },
    getOtherTicket(config) {
      const {
        ak: configAk,
        sk: configSk,
        url: configUrl,
        suffix: suffix
      } = config;
      const token = getToken();
      unitySkipApp(token, configAk).then(res => {
        if (res.code === 2000) {
          let href = "#";
          if (suffix == null) {
            href = configUrl + "?ticket=" + res.ticket;
          } else {
            href = configUrl + "?ticket=" + res.ticket + "&" + suffix;
          }
          window.open(href);
        } else {
          this.logout();
        }
      });
    }
  }
};
</script>
<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.block_1 {
  height: 100%;
  background-size: 100% 100%;
  width: 100%;
}

.section_1 {
  width: 100%;
  height: 100%;
  background: url("./img/slices/bg.png");
  background-size: 100% 100%;
}

.image_1 {
  width: 333px;
  height: 71px;
}

.text_1 {
  height: 38px;
  -webkit-text-stroke: 1px rgba(60, 74, 95, 1);
  overflow-wrap: break-word;
  color: rgba(68, 83, 105, 1);
  font-size: 40px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  white-space: nowrap;
  line-height: 40px;
}

.box_1 {
  width: 369px;
  height: 56px;
}

.disabled_box {
  background-color: #e8e8e8 !important;
}
.box_2 {
  width: 175px;
  height: 50px;
  background-color: #ebeef5;
  color: #909399;
  border-radius: 25px;
  /* background: url("./img/slices/dect.png") -3px -3px no-repeat; */
  background-size: 181px 56px;
  cursor: not-allowed;
}

.image-text_1 {
  width: 109px;
  height: 26px;
  /* margin: 12px 0 0 32px; */
}

.label_1 {
  width: 26px;
  height: 26px;
}

.text-group_1 {
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.box_3 {
  width: auto;
  height: 50px;
  /* background: url("./img/slices/dect.png") 100% no-repeat; */
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 25px;
  background-size: 100% 100%;
  padding: 0 30px;
  margin-right: 10px;
}

.image-text_2 {
  width: 109px;
  height: 25px;
  /* margin: 15px 0 0 35px; */
}

.label_2 {
  /* width: 25px; */
  /* height: 25px; */
}

.text-group_2 {
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.text_2 {
  width: 70px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 31px 0 0 37px;
}

.thumbnail_1 {
  width: 9px;
  height: 5px;
  margin: 37px 0 0 9px;
}

.image_2 {
  width: 1px;
  height: 30px;
  margin: 25px 0 0 30px;
}

.text_3 {
  width: 212px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(150, 165, 187, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 30px 0 0 19px;
}

.label_3 {
  width: 26px;
  height: 26px;
  margin: 26px 38px 0 628px;
}

.box_5 {
  height: 190px;
  background: url("./img/slices/menu-box.png") 100% no-repeat;
  background-size: 100% 100%;
  width: 100%;
}

.text-wrapper_1 {
  height: 21px;
  margin: 29px 0 0 30px;
}

.text_4 {
  width: 150px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  line-height: 22px;
  cursor: pointer;
  margin-right: 30px;
  transition: all 0.3s;
}

.text_4.active {
  /* color: #3779fd; */
  font-size: 20px;
  font-weight: 600;
  position: relative;
  background: none;
}

.text_4.active::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 0;
  width: 60%;
  margin-left: 20%;
  height: 3px;
  border-radius: 2px;
  background-color: #3779fd;
}

.text_5 {
  width: 71px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 30px;
}

.text_6 {
  width: 70px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 39px;
}

.text_7 {
  width: 70px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 40px;
}

.text_8 {
  width: 53px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 40px;
}

.image-wrapper_1 {
  width: 52px;
  height: 6px;
  margin: 13px 0 0 45px;
}

.image_3 {
  width: 52px;
  height: 6px;
}

.image-text_3 {
  width: 128px;
  height: 19px;
  cursor: pointer;
  transition: all 0.3s;
  margin-right: 20px;
}

.image-text_3:hover {
  transform: translateY(-2px);
}

.thumbnail_2 {
  width: 18px;
  height: 18px;
  margin-top: 1px;
}

.text-group_3 {
  width: 106px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.box_6 {
  background-color: rgba(78, 108, 254, 0);
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(250, 251, 254, 1);
  margin: 5px 0 0 253px;
}

.text_9 {
  width: 229px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin-left: 9px;
}

.box_7 {
  background-color: rgba(78, 108, 254, 0);
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(250, 251, 254, 1);
  margin: 5px 0 0 132px;
}

.text_10 {
  width: 211px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 1px 0 0 9px;
}

.group_2 {
  width: 124px;
  height: 17px;
  margin: 29px 0 28px 30px;
}

.block_2 {
  background-color: rgba(78, 108, 254, 0);
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(250, 251, 254, 1);
  margin-top: 4px;
}

.text_11 {
  width: 105px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}
body * {
  box-sizing: border-box;
  flex-shrink: 0;
}
body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma,
    Arial, PingFang SC-Light, Microsoft YaHei;
}
input {
  background-color: transparent;
  border: 0;
}
button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
}

button:active {
  opacity: 0.6;
}
.van-nav-bar__left:active,
.van-nav-bar__right:active {
  opacity: 1;
}
[class*="van-"]::after {
  border-bottom: 0;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}
.justify-around {
  display: flex;
  justify-content: space-around;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.align-start {
  display: flex;
  align-items: flex-start;
}
.align-center {
  display: flex;
  align-items: center;
}
.align-end {
  display: flex;
  align-items: flex-end;
}

.search-input {
  height: 60px;
  border-radius: 15px;
  overflow: hidden;
  border: 2px solid #3779fd;
}
.search-input .append-wrapper {
  background-color: #fff;
  padding: 10px 20px;
  width: 100%;
  height: 100%;
  display: flex;
}
.search-input ::v-deep .el-input-group__append {
  padding: 0;
}
.search-input ::v-deep .el-input__inner {
  width: 100%;
  height: 100%;
  font-size: 16px;
  border: none !important;
}

.search-input ::v-deep .el-input__inner:focus {
  border: none !important;
}
.search-input ::v-deep .el-input--suffix {
  height: 100%;
  border: none;
}
.search-input ::v-deep .el-input-group__prepend {
  background: #fff;
}

.hoverable-button {
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.hoverable-button:hover {
  filter: brightness(0.95); /* 鼠标悬停时稍微变暗 */
}
</style>
