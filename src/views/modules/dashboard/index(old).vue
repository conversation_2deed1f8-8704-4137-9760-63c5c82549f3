<template>
  <div class="system-container">
    <div class="system-header">
      <div class="header-content">
        <h1>"富春e站" 公益诉讼监督治理中心</h1>
      </div>
    </div>

    <div class="dashboard-container">
      <div class="dashboard-grid">
        <!-- 数据中心 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>数据中心</h3>
              <span class="icon document-icon">
                <el-dropdown @command="handleCommand">
                  <span class="el-dropdown-link" @click.stop>
                    <img class="title-icon" :src="iconObj.slh" alt="更多" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="dataShow">资源查询</el-dropdown-item>
                    <el-dropdown-item>数据中心分驾驶舱</el-dropdown-item>
                    <el-dropdown-item>专项分析数据</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </div>
            <div class="legend">
              <span
                :class="['legend-item', { active: activeType === 'dense' }]"
                @click.stop="switchChartType('dense')"
                >资源</span
              >
              <span class="arrow">⇄</span>
              <span
                :class="['legend-item', { active: activeType === 'special' }]"
                @click.stop="switchChartType('special')"
                >专项</span
              >
            </div>
            <div class="chart" ref="pieChart"></div>
          </div>
        </div>

        <!-- 裁判文书库 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>裁判文书库</h3>
              <span class="icon document-icon">
                <img
                  class="title-icon"
                  :src="iconObj.cpwsk"
                  alt="裁判文书库"
                  @click="getJudicialDocument"
                />
              </span>
            </div>
            <div class="chart" ref="lineChart"></div>
          </div>
        </div>

        <!-- 案发区域监测预警 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>案发区域监测预警</h3>
              <span class="icon location-icon">
                <img
                  @click="getEarlyWarningTicket()"
                  class="title-icon"
                  :src="iconObj.jcyj"
                  alt="案发区域监测预警"
                />
              </span>
            </div>
            <div class="chart" ref="scatterChart"></div>
          </div>
        </div>

        <!-- 监督场景中心 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>监督场景中心</h3>
            </div>
            <div class="scene-content">
              <div class="menu-item" @click="getBdzzqlsjk">
                <div class="menu-icon">
                  <img :src="iconObj.zzqlsjk" alt="剥夺政治权利审监抗" />
                </div>
                <div class="menu-text">剥夺政治权利审监抗</div>
              </div>
              <div class="menu-item" @click="getSdryjjzs">
                <div class="menu-icon">
                  <img :src="iconObj.sdryjzfz" alt="涉赌人员拒执犯罪" />
                </div>
                <div class="menu-text">涉赌人员拒执犯罪</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 办案辅助 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>办案辅助</h3>
            </div>
            <div class="tool-grid">
              <div class="menu-item sentencing-item">
                <div class="menu-icon">
                  <img :src="iconObj.tylxgl" alt="统一量刑管理" />
                </div>
                <span class="icon document-icon">
                  <el-dropdown @command="handleSentencingCommand">
                    <span class="el-dropdown-link">
                      <img class="title-icon" :src="iconObj.slh" alt="更多" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{type: 'tylxgl', action: 'query'}">统一量刑填报</el-dropdown-item>
                      <el-dropdown-item :command="{type: 'tylxgl', action: 'manage'}">统一量刑管理</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
                <div class="menu-text">统一量刑管理</div>
              </div>
              <div class="menu-item" @click="getPjxxcx">
                <div class="menu-icon">
                  <img :src="iconObj.cpxxcx" alt="判决信息查询" />
                </div>
                <div class="menu-text">判决信息查询</div>
              </div>
              <div class="menu-item sentencing-item">
                <div class="menu-icon">
                  <img :src="iconObj.xqjs" alt="刑期计算" />
                </div>
                <span class="icon document-icon">
                  <el-dropdown @command="handleSentencingCommand">
                    <span class="el-dropdown-link">
                      <img class="title-icon" :src="iconObj.slh" alt="更多" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{type: 'xqjs', action: 'query'}">实时计算</el-dropdown-item>
                      <el-dropdown-item :command="{type: 'xqjs', action: 'manage'}">判决预警</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </span>
                <div class="menu-text">刑期计算</div>
              </div>
              <div class="menu-item" @click="getXXts">
                <div class="menu-icon">
                  <img :src="iconObj.xxts" alt="信息推送" />
                </div>
                <div class="menu-text">信息推送</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 绩效评估 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>绩效评估</h3>
              <span class="icon document-icon">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    <img class="title-icon" :src="iconObj.slh" alt="更多" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="getWarningInfoTicket()"
                      >案件质量预警</el-dropdown-item
                    >
                    <el-dropdown-item @click.native="to('/durationStatistics')"
                      >一审公诉案件办案时长情况</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </div>
<!--            <div class="legend">-->
<!--              <span-->
<!--                :class="[-->
<!--                  'legend-item',-->
<!--                  { active: activePerformance === 'trend' }-->
<!--                ]"-->
<!--                @click="switchPerformanceType('trend')"-->
<!--                >趋势</span-->
<!--              >-->
<!--              <span class="arrow">⇄</span>-->
<!--              <span-->
<!--                :class="[-->
<!--                  'legend-item',-->
<!--                  { active: activePerformance === 'frequency' }-->
<!--                ]"-->
<!--                @click="switchPerformanceType('frequency')"-->
<!--                >高频</span-->
<!--              >-->
<!--            </div>-->
            <div class="chart" ref="areaChart"></div>
          </div>
        </div>

        <!-- 数字驾驶舱 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>数字驾驶舱</h3>
              <span class="icon trend-icon">
                <img class="title-icon" :src="iconObj.szjsc" alt="数字驾驶舱" />
              </span>
            </div>
            <div class="pentagon-chart">
              <!-- <img src="@/assets/pentagon.png" alt="五边形图表" /> -->
            </div>
          </div>
        </div>

        <!-- 第三方应用 -->
        <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>第三方应用</h3>
            </div>
            <div class="app-grid">
              <div class="menu-item">
                <div class="menu-icon">
                  <img :src="iconObj.hsxt" alt="护水系统" />
                </div>
                <div class="menu-text">护水系统</div>
              </div>
              <div class="menu-item">
                <div class="menu-icon">
                  <img :src="iconObj.jszhjw" alt="监所智慧纠违" />
                </div>
                <div class="menu-text">监所智慧纠违</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模块-009 -->
        <!--
         <div class="grid-item">
          <div class="card">
            <div class="card-header">
              <h3>模块-009</h3>
              <span class="icon document-icon">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    <img class="title-icon" :src="iconObj.slh" alt="更多" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>子菜单1</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </div>
          </div>
        </div>
        -->
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { unitySkipApp } from "@/api/system/login";
import { getToken, removeToken } from "@/utils/auth";
import rsa from "@/utils/rsa";
import { min } from "moment";
import axios from "axios";

export default {
  name: "Dashboard",
  data() {
    return {
      iconObj: {
        jszhjw: require("./icon/windows-fill.png"),
        hsxt: require("./icon/windows.png"),
        cpwsk: require("./icon/裁判.png"),
        jcyj: require("./icon/地图.png"),
        zzqlsjk: require("./icon/链接,链条.png"),
        sdryjzfz: require("./icon/链接,链条 (1).png"),
        tylxgl: require("./icon/编辑文本.png"),
        cpxxcx: require("./icon/搜索文本-line.png"),
        xqjs: require("./icon/尺子.png"),
        xxts: require("./icon/发送.png"),
        szjsc: require("./icon/统计图.png"),
        slh: require("./icon/省略号.png")
      },
      charts: [],
      activeType: "dense",
      activePerformance: "trend",
      chartData: {
        dense: [
          { value: 50, name: "案件资源" },
          { value: 100, name: "政法资源" },
          { value: 150, name: "政务资源" }
        ],
        special: [
          { value: 80, name: "案件资源" },
          { value: 120, name: "政法资源" },
          { value: 90, name: "政务资源" }
        ]
      },
      radarData: [
        { name: "案件看板", value: 85 },
        { name: "监督能力库", value: 90 },
        { name: "理论调研库", value: 95 },
        { name: "制度规范库", value: 80 },
        { name: "数据中心", value: 88 }
      ],
      warningData: {},
      warningStatistcsData: {},
      wsStatistcsData: {}
    }
  },
  mounted() {
    this.initCharts();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    this.charts.forEach(chart => {
      chart.dispose();
    });
  },
  methods: {
    logout() {
      this.$confirm(
          "登录过期,您可以停留在次页面,或者重新登录",
          "确认退出",
          {
            confirmButtonText: "重新登录",
            cancelButtonText: "取消",
            type: "warning"
          }
      ).then(() => {
        removeToken();
        window.location.href = unityLogin;
      });
    },
    to(path) {
      window.open(
        window.location.origin +
          window.location.pathname +
          "#" + path
      );
    },
    getJudicialDocument() {
      this.getOtherTicket(window.globalConfig.JudicialDocument);
    },
    getDataShowDocument() {
      this.getOtherTicket(window.globalConfig.dataShow);
    },
    getSupervisoryScenario() {
      this.getOtherTicket(window.globalConfig.supervisoryScenario);
    },
    //剥夺政治权利审监抗
    getBdzzqlsjk() {
      this.getOtherTicket(window.globalConfig.bdzzqlsjk);
    },
    //涉赌人员拒绝执行
    getSdryjjzs() {
      this.getOtherTicket(window.globalConfig.sdryjjzs);
    },
    getXXts() {
      this.getOtherTicket(window.globalConfig.xxts);
    },
    getPjxxcx() {
      this.getOtherTicket(window.globalConfig.pjxxcx);
    },
    getOtherTicket(config) {
      const { ak: configAk, sk: configSk, url: configUrl, suffix: suffix} = config;
      const token = getToken();
      unitySkipApp(token, configAk).then(res => {
        if (res.code === 2000) {
          let href = "#"
          if (suffix == null) {
            href = configUrl + "?ticket=" + res.ticket
          } else {
            href = configUrl + "?ticket=" + res.ticket + '&' + suffix
          }
          window.open(href);
        } else {
          this.logout()
        }
      });
    },
    getEarlyWarningTicket() {
      this.getOtherTicket(window.globalConfig.earlyWarning);
    },
    getWarningInfoTicket() {
      this.getOtherTicket(window.globalConfig.warningInfo);
    },
    fuyangSystemJoint(config) {
      const token = getToken();
      const { ak: configAk, sk: configSk, url: configUrl } = config;
      unitySkipApp(token, configAk).then(res => {
        if (res.code === 2000) {
          const encryptedTicket = encodeURIComponent(
            rsa.rsaPublicData(res.ticket)
          );
          const href =
            configUrl +
            "?ak=" +
            configAk +
            "&sk=" +
            configSk +
            "&ticket=" +
            encryptedTicket;
          window.open(href);
        } else {
          this.logout()
        }
      });
    },

    handleResize() {
      this.charts.forEach(chart => {
        chart.resize();
      });
    },
    switchChartType(type) {
      this.activeType = type;
      const pieChart = this.charts[0];
      pieChart.setOption({
        series: [
          {
            data: this.chartData[type],
            center: ["60%", "50%"]
          }
        ]
      });
    },
    handleCommand(command) {
      switch (command) {
        case "dataShow":
          this.getDataShowDocument();
          break;
      }
    },
    findMaxNum(data) {
      console.log(data);

        // 初始化最大值为负无穷大
        let maxNum = -Infinity;

        // 遍历 criminal 数组
        data.criminal.forEach(item => {
            if (item.num > maxNum) {
                maxNum = item.num;
            }
        });

        // 遍历 administrative 数组
        data.administrative.forEach(item => {
            if (item.num > maxNum) {
                maxNum = item.num;
            }
        });

        return maxNum;
    },
    async getWarningData() {
      const { ak: configAk, sk: configSk, url: configUrl } = window.globalConfig.warningInfo;
      const token = getToken();
      const res = await unitySkipApp(token, configAk)
      if (res.code === 2000) {
        await this.getWarningDataList(res.ticket)
      } else {
        this.logout()
      }
      const res2 = await unitySkipApp(token, configAk)
      if (res2.code === 2000) {
        await this.getWarningStatistcsList(res2.ticket)
      } else {
        this.logout()
      }
      const res3 = await unitySkipApp(token, configAk)
      if (res3.code === 2000) {
        await this.getWsStatistcs(res3.ticket)
      } else {
        this.logout()
      }
    },
    async getWarningDataList(ticket) {
      const { data } = await axios.get(window.globalConfig.warningInfo.statistcsApi + "?ticket=" + ticket)
      this.warningData = data
      this.initScatterChart()
    },
    async getWarningStatistcsList(ticket) {
      const { data } = await axios.get(window.globalConfig.warningInfo.statistcsWarningInfoApi + "?ticket=" + ticket)
      this.warningStatistcsData = data
      this.initAreaChart()
    },
    async getWsStatistcs(ticket) {
      const { data } = await axios.get(window.globalConfig.JudicialDocument.statistcsApi + "?ticket=" + ticket)
      this.wsStatistcsData = data
      this.initLineChart()
    },
    handleSentencingCommand(command) {
      switch(command.type + command.action){
        case 'tylxglquery':
          console.log(`点击了统一量刑填报`);
          this.getOtherTicket(window.globalConfig.tylxglquery);
          break;
        case 'tylxglmanage':
          console.log(`点击了统一量刑管理`);
          this.getOtherTicket(window.globalConfig.tylxglmanage);
          break;
        case 'xqjsquery':
          console.log(`点击了实时计算`);
          this.to('/calculationSentence');
          break;
        case 'xqjsmanage':
          console.log(`点击了判决预警`);
          this.getOtherTicket(window.globalConfig.xqjsmanage);
          break;
      }
    },
    async initCharts() {
      await this.getWarningData()
      this.initPieChart()
      // this.initLineChart()
      // this.initScatterChart()
      // this.initAreaChart()
      // this.initRadarChart()
    },
    initRadarChart() {
      // 初始化雷达图
      const radarChart = echarts.init(this.$refs.radarChart);
      this.charts.push(radarChart);
      radarChart.setOption({
        radar: {
          center: ["50%", "58%"],
          radius: "80%",
          indicator: this.radarData.map(item => ({
            name: item.name,
            max: 100,
            color: "#333"
          })),
          shape: "polygon",
          splitNumber: 4,
          axisName: {
            color: "#333",
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              //   color: "#E0E0E0"
            }
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ["#F5F7FA", "#FFFFFF"]
            }
          }
        },
        series: [
          {
            type: "radar",
            data: [
              {
                value: this.radarData.map(item => item.value),
                name: "能力指数",
                symbol: "circle",
                symbolSize: 6,
                lineStyle: {
                  color: "#4B7BE5",
                  width: 2
                },
                areaStyle: {
                  color: "rgba(75, 123, 229, 0.2)"
                },
                itemStyle: {
                  color: "#4B7BE5"
                }
              }
            ]
          }
        ]
      });
    },
    initAreaChart() {
      const areaChart = echarts.init(this.$refs.areaChart);
      this.charts.push(areaChart);
      areaChart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          }
        },
        legend: {
          data: ["案卡填写", "文书编写"],
          // orient: "vertical",
          // left: "5%",
          top: "5%",
          // itemWidth: 10,
          // itemHeight: 10,
          textStyle: {
            color: "#333",
            fontSize: 12,
            padding: [0, 0, 0, 4]
          }
        },
        grid: {
          top: "25%",
          bottom: "",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.warningStatistcsData.month,
          axisLine: {
            lineStyle: {
              //   color: "#E0E0E0"
            }
          }
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              //   color: "#E0E0E0",
              type: "dashed"
            }
          }
        },
        series: [
          {
            name: "案卡填写",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: "rgba(128, 178, 255, 0.6)"
            },
            emphasis: {
              focus: "series"
            },
            data: this.warningStatistcsData.ak
          },
          {
            name: "文书编写",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: "rgba(255, 180, 120, 0.6)"
            },
            emphasis: {
              focus: "series"
            },
            data: this.warningStatistcsData.ws
          }
        ]
      });
    },
    initScatterChart() {
      const scatterChart = echarts.init(this.$refs.scatterChart);
      this.charts.push(scatterChart);
      let maxNum = this.findMaxNum(this.warningData)
      let yMax = maxNum % 2 === 0 ? maxNum : maxNum + 1
      scatterChart.setOption({
        tooltip: {
          trigger: "item"
        },
        legend: {
          data: ["刑事案件", "行政处罚"],
          top: "5%"
        },
        grid: {
          top: "25%",
          bottom: "28%"
        },
        dataZoom: [
          {
            type: 'slider', // 水平滚动条
            xAxisIndex: [0],
            start: 0, // 数据窗口范围的起始百分比
            end: 20,  // 数据窗口范围的结束百分比
            showDetail: false
          },
          {
            type: 'inside', // 支持鼠标滚轮缩放
            xAxisIndex: [0],
            start: 0,
            end: 20
          }
        ],
        xAxis: {
          type: "category",
          data: this.warningData.criminal.map(i => i.street),
          axisLine: {
            lineStyle: {
              //   color: "#E0E0E0"
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            rotate: 45, // 旋转45度
            interval: 0 // 强制显示所有标签
          }
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              type: "dashed"
              //   color: "#E0E0E0"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          min: 0,
          max: yMax
        },
        series: [
          {
            name: "刑事案件",
            type: "scatter",
            symbolSize: 10,
            itemStyle: {
              color: "#FF6B6B"
            },
            data: this.warningData.criminal.map(i => {
              return i.num
            })
          },
          {
            name: "行政处罚",
            type: "scatter",
            symbolSize: 10,
            itemStyle: {
              color: "#FFD93D"
            },
            data: this.warningData.administrative.map(i => {
              return i.num
            })
          }
        ]
      });
    },
    initPieChart() {
      const pieChart = echarts.init(this.$refs.pieChart);
      this.charts.push(pieChart);
      pieChart.setOption({
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c} ({d}%)"
        },

        legend: {
          orient: "vertical",
          left: "5%",
          top: "middle",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#333",
            fontSize: 12,
            padding: [0, 0, 0, 4]
          }
        },
        color: ["#4B7BE5", "#6AA1FF", "#89F4FF"],
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            center: ["60%", "50%"],
            data: this.chartData.dense,
            label: {
              show: true,
              position: "outside",
              formatter: "{b}\n{c}",
              fontSize: 12
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)"
              }
            }
          }
        ]
      });
    },
    initLineChart() {
      const lineChart = echarts.init(this.$refs.lineChart);
      this.charts.push(lineChart);
      lineChart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985"
            }
          }
        },
        legend: {
          data: ["判决书", "起诉书", "不起诉书"],
          top: "5%"
        },
        grid: {
          bottom: "12%",
          top: "25%"
        },
        xAxis: {
          type: "category",
          data: this.wsStatistcsData.month,
          axisLine: {
            lineStyle: {}
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: "判决书",
            data: this.wsStatistcsData.pjs,
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#89F4FF"
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: "起诉书",
            data: this.wsStatistcsData.qqs,
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#4B7BE5"
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: "不起诉书",
            data: this.wsStatistcsData.bqqs,
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#FFD93D"
            },
            lineStyle: {
              width: 2
            }
          }
        ]
      });
    }
  }
};
</script>

<style scoped>
.system-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
}

.system-header {
  background-color: #1e4c7a;
  width: 100%;
  height: 60px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.system-header h1 {
  margin: 0;
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.dashboard-container {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f0f2f5;
  padding: 30px 100px;
  overflow: auto;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.grid-item {
  /* min-height: 240px; */
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  height: 100%;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0px;
  position: relative;
}

.card-header h3 {
  margin: 0;
  color: #000;
  font-size: 16px;
  font-weight: 800;
  text-align: center;
}

.card-header .icon,
.card-header .more {
  position: absolute;
  right: 0;
}

.header-left {
  position: absolute;
  left: 0;
}

.more {
  cursor: pointer;
  color: #999;
}

.icon {
  font-size: 20px;
}

.chart {
  height: 160px;
}

.scene-content,
.tool-grid,
.app-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 12px;
}

.tool-grid {
  grid-template-columns: repeat(2, 1fr);
}

.menu-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px;
  border-radius: 4px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: #e6e8eb;
}

.sentencing-item {
  transition: none;
}

.sentencing-item:hover {
  background-color: #f5f7fa;
}

.menu-item .document-icon {
  position: absolute;
  top: 1px;
  right: 4px;
  z-index: 1;
}

.menu-icon {
  font-size: 24px;
  margin-bottom: 8px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.menu-text {
  font-size: 14px;
  color: #333;
}

.legend {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 800;
  color: rgba(0, 0, 0, 0.8);
  position: absolute;
  top: 50px;
  left: 10px;
  z-index: 1;
}

.legend-item {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.legend-item:hover {
  background-color: #f5f7fa;
}

.legend-item.active {
  color: #4b7be5;
  background-color: #ecf2ff;
}

.arrow {
  color: #999;
  font-size: 12px;
}

.pentagon-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.pentagon-chart img {
  max-width: 80%;
  height: auto;
}
.title-icon {
  cursor: pointer;
  width: 19px;
  height: 19px;
  filter: contrast(0.5) brightness(0.1);
}

.el-dropdown-link {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
