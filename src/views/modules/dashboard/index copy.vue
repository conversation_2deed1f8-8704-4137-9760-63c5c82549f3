<!--
 * @Description:
-->
<template>
  <div class="system-container relative">
    <!-- 头部标题 -->
    <div
      class="header z-50 flex flex-col w-full justify-center items-center mt-20"
    >
      <img src="./img/slices/title_1.png" alt="" width="300px" />
      <span class="sub-title">公益诉讼监督治理中心</span>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container z-50">
      <div class="search-tabs">
        <div
          class="border border-gray-400 text-sm border-solid  tab-item bg-white"
        >
          <img src="./img/icon/深度思考.png" alt="深度思考" />
          <span>深度思考</span>
        </div>
        <div
          class="border border-gray-400 text-sm border-solid tab-item active "
        >
          <img src="./img/icon/站内搜索.png" alt="站内搜索" />
          <span>站内搜索</span>
        </div>
      </div>
      <div class="search-box">
        <el-input
          placeholder="请输入资源名称等关键信息"
          v-model="searchKeyword"
        >
          <el-select
            slot="prepend"
            v-model="searchType"
            placeholder="全部资源"
            class="search-select"
          >
            <el-option
              v-for="item in searchTpyeList"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
          <el-button
            type="primary"
            slot="append"
            class="search-btn"
            @click="handleSearch"
            >确认</el-button
          >
        </el-input>
      </div>
      <div class="menu-container mt-2">
        <el-dropdown
          v-for="(menu, index) in menus"
          :key="index"
          trigger="hover"
        >
          <div class="menu-item text-black">
            <img
              :src="menu.icon"
              :alt="menu.name"
              class="menu-icon"
              v-if="menu.icon"
            />
            <span @click="handleMenu(menu)">{{ menu.name }}</span>
          </div>
          <el-dropdown-menu slot="dropdown" v-if="menu.children">
            <el-dropdown-item
              v-for="(sub, subIndex) in menu.children"
              :key="subIndex"
              @click.native="handleMenuClick(sub)"
            >
              {{ sub.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
import { unitySkipApp } from "@/api/system/login";
import { getToken, removeToken } from "@/utils/auth";

export default {
  name: "Dashboard",
  data() {
    return {
      bg: require("./img/slices/bg.png"),
      searchType: window.globalConfig.resourceSearch.defaultCode,
      searchKeyword: "",
      searchTpyeList: window.globalConfig.resourceSearch.type,
      menus: [
        {
          name: "数据中心",
          icon: require("./img/icon/数据中心.png"),
          children: [
            { name: "数据检索", mode: "dataShow" },
            { name: "判决书库", mode: "JudicialDocument" },
            { name: "阅览室" }
          ]
        },
        {
          name: "场景中心",
          icon: require("./img/icon/场景中心.png"),
          children: [
            { name: "案发区域预警", mode: "earlyWarning" },
            { name: "剥夺政治权利审监抗类案监督", mode: "bdzzqlsjk" },
            { name: "涉赌人员拒执犯罪监督场景", mode: "sdryjjzs" },
            { name: "刑期异常预警", mode: "xqjsmanage" }
          ]
        },
        {
          name: "绩效评估",
          icon: require("./img/icon/绩效评估.png"),
          url: "/durationStatistics"
        },
        {
          name: "办案辅助",
          icon: require("./img/icon/办案辅助.png"),
          children: [
            { name: "统一量刑填报", mode: "tylxglquery" },
            { name: "统一量刑管理", mode: "tylxglmanage" },
            { name: "刑期计算工具", url: "calculationSentence" },
            { name: "信息推送", mode: "xxts" }
          ]
        },
        {
          name: "驾驶舱",
          icon: require("./img/icon/驾驶舱.png")
        }
      ]
    };
  },
  methods: {
    handleSearch() {
      if (this.searchKeyword) {
        const {
          ak: configAk,
          sk: configSk,
          url: configUrl,
          suffix: suffix,
          type: type
        } = window.globalConfig.resourceSearch;
        const token = getToken();
        unitySkipApp(token, configAk).then(res => {
          if (res.code === 2000) {
            let href = "#";
            href =
              configUrl +
              "?query=" +
              this.searchKeyword +
              "&type=" +
              this.searchType +
              "&ticket=" +
              res.ticket;
            // console.log(href)
            window.open(href);
          } else {
            this.logout();
          }
        });
      } else {
        this.$message.warning("请输入关键字");
      }
    },
    handleMenuClick(row) {
      if (row.url) {
        window.open(
          window.location.origin + window.location.pathname + "#" + row.url
        );
      } else if (row.mode) {
        this.getOtherTicket(window.globalConfig[row.mode]);
      } else {
        this.$message.warning("没有配置地址");
      }
    },
    handleMenu(row) {
      if (row.url) {
        window.open(
          window.location.origin + window.location.pathname + "#" + row.url
        );
      }
    },
    getOtherTicket(config) {
      const {
        ak: configAk,
        sk: configSk,
        url: configUrl,
        suffix: suffix
      } = config;
      const token = getToken();
      unitySkipApp(token, configAk).then(res => {
        if (res.code === 2000) {
          let href = "#";
          if (suffix == null) {
            href = configUrl + "?ticket=" + res.ticket;
          } else {
            href = configUrl + "?ticket=" + res.ticket + "&" + suffix;
          }
          window.open(href);
        } else {
          this.logout();
        }
      });
    },
    logout() {
      this.$confirm("登录过期,您可以停留在次页面,或者重新登录", "确认退出", {
        confirmButtonText: "重新登录",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        removeToken();
        window.location.href = unityLogin;
      });
    }
  }
};
</script>

<style scoped>
/* 你可以混合使用 Tailwind 和普通 CSS */
.system-container {
  @apply min-h-screen flex flex-col items-center bg-white py-10;
}

.header {
  @apply text-center mb-10;
}

.main-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #1e4c7a;
}

.search-container {
  width: 800px;
  margin-bottom: 60px;
}

.search-tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.tab-item img {
  width: 20px;
  height: 20px;
}

.tab-item.active {
  background-color: #ecf2ff;
  color: #4b7be5;
  border: 1px solid #4b7be5;
}

.search-box {
  display: flex;
  gap: 10px;
}

.search-select {
  width: 120px;
}

.search-select ::v-deep .el-input__inner {
  border-radius: 5px 0 0 5px !important;
}

.search-input {
  flex: 1;
}

.search-btn {
  width: 100px;
  background-color: #4b7be5;
  border-radius: 0 5px 5px 0 !important;
  height: 36px;
}

.menu-container {
  display: flex;
  gap: 60px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}
.menu-item img {
  width: 16px;
  height: 16px;
}

.menu-icon {
  width: 32px;
  height: 32px;
}

/* Element UI 样式覆盖 */
:deep(.el-dropdown-menu) {
  padding: 8px 0;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 20px;
  font-size: 14px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #ecf2ff;
  color: #4b7be5;
}

::v-deep .el-input-group__prepend,
::v-deep .el-input-group__append {
  border: none;
}

.sub-title {
  height: 38px;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 40px;
  color: #445369;
  text-stroke: 1px #3b495f;
  -webkit-text-stroke: 1px #3b495f;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.block_1 {
  height: 1080px;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/5e2d872f34546998c728de691ad08006)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.section_1 {
  width: 1920px;
  height: 1080px;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/7228488cb337b75d624f65c0bf3cdd4e)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_1 {
  width: 333px;
  height: 71px;
  margin: 157px 0 0 794px;
}

.text_1 {
  width: 392px;
  height: 38px;
  -webkit-text-stroke: 1px rgba(60, 74, 95, 1);
  overflow-wrap: break-word;
  color: rgba(68, 83, 105, 1);
  font-size: 40px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40px;
  margin: 25px 0 0 765px;
}

.box_1 {
  width: 369px;
  height: 56px;
  margin: 94px 0 0 427px;
}

.box_2 {
  width: 175px;
  height: 50px;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/a084e93ae842b3f4ee824883e3e04b72) -3px -3px
    no-repeat;
  background-size: 181px 56px;
  margin-top: 3px;
}

.image-text_1 {
  width: 109px;
  height: 26px;
  margin: 12px 0 0 32px;
}

.label_1 {
  width: 26px;
  height: 26px;
}

.text-group_1 {
  width: 70px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin-top: 5px;
}

.box_3 {
  width: 181px;
  height: 56px;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/a70b7a1b7043778b13f400a6552acb16)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_2 {
  width: 109px;
  height: 25px;
  margin: 15px 0 0 35px;
}

.label_2 {
  width: 25px;
  height: 25px;
}

.text-group_2 {
  width: 70px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin-top: 5px;
}

.box_4 {
  width: 1079px;
  height: 87px;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/3303a3e7d8745001717aecdb1cefd8be)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 8px 0 0 421px;
}

.text_2 {
  width: 70px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 31px 0 0 37px;
}

.thumbnail_1 {
  width: 9px;
  height: 5px;
  margin: 37px 0 0 9px;
}

.image_2 {
  width: 1px;
  height: 30px;
  margin: 25px 0 0 30px;
}

.text_3 {
  width: 212px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(150, 165, 187, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 30px 0 0 19px;
}

.label_3 {
  width: 26px;
  height: 26px;
  margin: 26px 38px 0 628px;
}

.box_5 {
  height: 190px;
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/4aefb726670b34a11ae3cda2d060fbed)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1074px;
  margin: 13px 0 341px 423px;
}

.text-wrapper_1 {
  width: 499px;
  height: 21px;
  margin: 29px 0 0 30px;
}

.text_4 {
  width: 86px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 22px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_5 {
  width: 71px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 30px;
}

.text_6 {
  width: 70px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 39px;
}

.text_7 {
  width: 70px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 40px;
}

.text_8 {
  width: 53px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 3px 0 0 40px;
}

.image-wrapper_1 {
  width: 52px;
  height: 6px;
  margin: 13px 0 0 45px;
}

.image_3 {
  width: 52px;
  height: 6px;
}

.group_1 {
  width: 991px;
  height: 19px;
  margin: 28px 0 0 26px;
}

.image-text_3 {
  width: 128px;
  height: 19px;
}

.thumbnail_2 {
  width: 18px;
  height: 18px;
  margin-top: 1px;
}

.text-group_3 {
  width: 106px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}

.box_6 {
  background-color: rgba(78, 108, 254, 0);
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(250, 251, 254, 1);
  margin: 5px 0 0 253px;
}

.text_9 {
  width: 229px;
  height: 18px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin-left: 9px;
}

.box_7 {
  background-color: rgba(78, 108, 254, 0);
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(250, 251, 254, 1);
  margin: 5px 0 0 132px;
}

.text_10 {
  width: 211px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
  margin: 1px 0 0 9px;
}

.group_2 {
  width: 124px;
  height: 17px;
  margin: 29px 0 28px 30px;
}

.block_2 {
  background-color: rgba(78, 108, 254, 0);
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(250, 251, 254, 1);
  margin-top: 4px;
}

.text_11 {
  width: 105px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(60, 74, 98, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 18px;
}
</style>
