<template>
  <div>
    <el-form
      :model="form"
      label-width="120px"
      :rules="rules"
      ref="addForm"
      :before-colse="close"
    >
      <div class="dialog-header">
        <span>文件上传</span>
        <i class="el-icon-close" @click="close" />
      </div>
      <div class="dialog-container">
      <template>
<!--        <el-form-item label="上传分组">-->
<!--          <treeselect v-model="form.categoryId" :options="options" :normalizer="normalizer" :show-count="true"-->
<!--                      placeholder="选择上级菜单" />-->
<!--        </el-form-item>-->
        <el-form-item label="类目">
<!--          <el-radio-group v-model="form.category" style="width: 100%;">-->
<!--            <el-radio-button label="zdfw">制度规范</el-radio-button>-->
<!--            <el-radio-button label="jdnl">监督能力</el-radio-button>-->
<!--            <el-radio-button label="lldy">理论调研</el-radio-button>-->
<!--          </el-radio-group>-->
          <treeselect v-model="form.category" :options="options" :normalizer="normalizer" :show-count="true"
                      placeholder="选择上级菜单" />
        </el-form-item>
<!--        <el-form-item label="文书种类" v-if="form.fileType === 'doc'">-->
<!--          <el-select v-model="form.type" style="width: 100%;">-->
<!--            <el-option v-for="item in typeDict" :value="item.code" :label="item.name"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="文件上传">
          <div class="upload-container">
            <!-- 文件上传 -->
            <el-upload
              ref="fileUpload"
              class="upload-box"
              :http-request="handleUploadFile"
              :show-file-list="false"
              :auto-upload="true"
              :action="action"
              multiple
            >
              <el-button type="primary" icon="el-icon-upload2">选择文件</el-button>
            </el-upload>

            <!-- 文件夹上传 -->
            <input
              ref="dirInput"
              type="file"
              style="display: none"
              webkitdirectory
              @change="handleDirectoryChange"
            />
            <el-button type="primary" icon="el-icon-folder" @click="triggerDirectorySelect">选择文件夹</el-button>
          </div>

          <!-- 上传进度列表 -->
          <div class="upload-progress" v-if="uploadList.length">
            <div v-for="file in uploadList" :key="file.uid" class="progress-item">
              <div class="file-info">
                <span class="file-name">{{ file.name }}</span>
                <span class="file-status">{{ file.status === 'success' ? '上传成功' : '上传中' }}</span>
              </div>
              <el-progress
                :percentage="file.percentage"
                :status="file.status === 'success' ? 'success' : ''"
              />
            </div>
          </div>
        </el-form-item>
<!--        <el-form-item label="备注" prop="remark">-->
<!--          <el-input-->
<!--            type="textarea"-->
<!--            v-model="form.remark"-->
<!--            placeholder="请输入备注"-->
<!--          ></el-input>-->
<!--        </el-form-item>-->
      </template>
        <el-form-item>
          <el-button type="primary" @click="saveBy">保存</el-button>
          <!--        <el-button type="primary" v-if="!form.id && form.fileType === 'doc'" @click="saveAndAnalysis">保存并解析</el-button>-->
          <!--        <el-button @click="close">确定</el-button>-->
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>
<script>

import { saveFileSource } from "@/api/api/fileSystem"
import Treeselect from "@riophae/vue-treeselect"
import { categoryTreeBy } from "@/api/api/assetCategory"

export default {
  components: { Treeselect },
  model: {
    prop: "data",
    event: "change"
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    typeDict: {
      type: Array,
      default: () => ([])
    },
    apiConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        category: 'root',
        fileList: []
      },
      loading: false,
      options: [],
      rules: {},
      uploadList: [],
      formFileList: [],
      action: API.baseAPI + "/rest/system/file/upload",
      sumFile: 0,
      finishFile: 0,
      uploadStats: {
        success: 0,
        failed: 0,
        total: 0
      }
    };
  },
  watch: {
  },
  mounted() {
    this.init();
  },
  methods: {
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.nodeId,
        label: node.nodeText,
        children: node.children
      };
    },
    refresh() {
      this.handleDialogClose()
      this.$emit("refresh");
    },
    handleDialogClose() {
      this.form = {
        category: 'root',
        fileList: []
      }
      this.formFileList = []
      this.uploadList = []
      this.sumFile = 0
      this.finishFile = 0
      this.uploadStats= {
        success: 0,
        failed: 0,
        total: 0
      }
    },
    close() {
      this.handleDialogClose()
      this.$emit("close");
    },
    // async init() {
    //   const { rows } = await categoryTreeJson({
    //     appId : 'FILE',
    //     categoryId: 'file.sourceCategory',
    //     creatorOnly: 'false'
    //   });
    //   const menu = { nodeId: "root", nodeText: "根目录", children: [] };
    //   menu.children = this.handleTree(rows, "nodeId");
    //   this.options = rows
    // },
    async init() {
      const { rows } = await categoryTreeBy({
        catalogueId: window.globalConfig.catalogueId
      });
      const data = { nodeId: "root", nodeText: "根目录", children: [] };
      data.children = this.handleTree(rows, "nodeId");
      this.options.push(data);
    },
    // 保存
    saveBy() {
      if (this.uploadList.some(file => file.status === 'uploading')) {
        this.$message.warning("请等待文件上传完成");
        return;
      }
      if (this.formFileList.length === 0) {
        this.$message.warning("请先上传文件");
        return;
      }
      this.$refs.addForm.validate(valid => {
        if (valid) {
          this.form.fileList = JSON.stringify(this.formFileList)
          this.form.catalogueId = window.globalConfig.catalogueId
          saveFileSource(this.form).then(res => {
            if (res.state === true) {
              this.$notify({
                message: "保存成功",
                position: "bottom-right",
                type: "success"
              });
            }
            this.close();
          });
        } else {
          return false;
        }
      });
    },
    // 保存并解析
    async saveAndAnalysis() {
      // const { id } = await saveFileSource({
      //   ...this.form
      // })
      // this.form.id = id
      // let find = this.typeDict.find(i => i.code === this.form.type)
      // axios.post(API.baseAPI + this.apiConfig.mapUrl, {
      //   fileName: this.form.fileName,
      //   fileType: find ? find.name : '',
      //   filePath: this.form.path,
      //   fileSourceId: this.form.id
      // }, {
      //   headers: {
      //     Identitytoken: getToken(),
      //     "Content-Type": "multipart/form-data"
      //   }
      // }).then(response => {
      //   saveFileSource({
      //     ...this.form,
      //     transactionId: response.data.transactionId
      //   }).then(res => {
      //     if (res.state === true) {
      //       this.$notify({
      //         title: "操作提示",
      //         message: "保存成功，刷新查看解析结果",
      //         position: "bottom-right",
      //         type: "success"
      //       });
      //     }
      //     this.refresh();
      //   })
      // })
      //   .catch(error => {
      //     this.$message.error("操作失败,请联系管理员");
      //   });
    },
    async beforeUpload(file) {
      console.log(file)
      // let suffix = []
      // switch (this.form.type) {
      //   case "EXCEL":
      //     suffix = ['xls','xlsx']
      //     break
      //   case "TXT":
      //     suffix = ['txt']
      //     break
      //   case "CSV":
      //     suffix = ['csv']
      //     break
      //   case "DB":
      //     suffix = ['db']
      //     break
      //   default:
      //     suffix = []
      // }
      // let isBson = false
      // if (suffix.length > 0) {
      //   suffix.forEach(it => {
      //     isBson = isBson || file.name.includes("." + it);
      //   })
      //   if (!isBson) {
      //     this.$message.error("只能上传" + suffix.join(',') + "文件");
      //     return
      //   }
      // }
      // let data = await getByTitle({
      //   categoryId: this.form.categoryId,
      //   title: file.name
      // });
      // if (data != null && Object.keys(data).length > 0) {
      //   this.$confirm("当前分组下已存在同名文件，继续上传将会覆盖，是否继续上传?", "温馨提示", {
      //     confirmButtonText: "继续",
      //     cancelButtonText: "取消",
      //     type: "warning"
      //   }).then(_ => {
      //     return isBson;
      //   }).catch(_ => {
      //     this.fileList = []
      //     this.$set(this.form, "title", '');
      //     this.$set(this.form, "path", '');
      //     return false
      //   })
      // }
      // return isBson;
      return true
    },
    onSuccess(res, file) {
      if (res.url) {
        this.formFileList.push(res)
        this.$notify({
          title: "操作提示",
          message: "上传文件成功",
          position: "bottom-right",
          type: "success"
        })
      } else {
        this.$notify({
          title: "操作提示",
          message: "上传文件失败",
          position: "bottom-right",
          type: "error"
        })
      }
    },
    beforeRemove(file, fileList) {
      if (file && file.status === "success") {
        return this.$confirm(`确定移除 ${file.name}？`);
      }
    },
    handleRemove(file, fileList) {
      if (file && file.status == "success") {
        this.formFileList = fileList.map(f => f.response)
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`一次只能上传10个文件`);
    },
    handlePreview() {
      let inputDom = null
      this.$nextTick(() => {
        if (document.querySelector('.el-upload__input') != null) {
          inputDom = document.querySelector('.el-upload__input')
          inputDom.webkitdirectory = true
        }
      })
    },
    handleUploadFile({ file }) {
      const fileObj = {
        uid: Date.now() + Math.random(),
        file,
        name: file.name,
        percentage: 0,
        status: 'uploading'
      };
      this.uploadList.push(fileObj);
      this.uploadFile(fileObj);
    },
    // 新增方法：处理文件夹
    async processDirectory(file) {
      const entries = await this.getDirectoryEntries(file)
      for (const entry of entries) {
        if (entry.isFile) {
          const fileObj = await this.getFileFromEntry(entry)
          // 获取相对路径
          const relativePath = entry.fullPath || entry.webkitRelativePath
          const uploadObj = {
            uid: Date.now() + Math.random(),
            file: fileObj,
            name: relativePath,
            percentage: 0,
            status: 'uploading'
          };
          this.uploadList.push(uploadObj);
          this.uploadFile(uploadObj);
        } else if (entry.isDirectory) {
          // 递归处理子文件夹
          await this.processDirectory(entry)
        }
      }
    },
    // 新增方法：获取文件夹内容
    async getDirectoryEntries(file) {
      return new Promise((resolve) => {
        const reader = file.createReader()
        reader.readEntries((entries) => {
          resolve(entries)
        })
      })
    },
    // 新增方法：从文件入口获取文件对象
    async getFileFromEntry(entry) {
      return new Promise((resolve) => {
        entry.file((file) => {
          resolve(file)
        })
      })
    },
    // 统一的上传方法
    uploadFile(fileObj) {
      const formData = new FormData();
      formData.append('file', fileObj.file);
      formData.append('category', this.form.category); // 添加类目参数
      formData.append('verifyRep', true); // 是否校验文件已存在

      // 创建上传请求
      const xhr = new XMLHttpRequest();
      xhr.open('POST', this.action, true);

      // 监听上传进度
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percentage = Math.round((e.loaded * 100) / e.total);
          fileObj.percentage = percentage;
        }
      };

      // 处理上传完成
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            fileObj.status = 'success';
            fileObj.percentage = 100;

            response.absolutePath = response.originalFileName
            response.originalFileName = response.originalFileName.split('/').pop();

            this.formFileList.push(response);
            this.uploadStats.success++;
            this.showUploadStats();
          } catch (error) {
            this.handleUploadError(fileObj, '解析响应失败');
          }
        } else {
          this.handleUploadError(fileObj, '上传失败');
        }
      };

      // 处理上传错误
      xhr.onerror = () => {
        this.handleUploadError(fileObj, '网络错误');
      };

      // 发送请求
      xhr.send(formData);
    },
    // 处理上传错误
    handleUploadError(fileObj, message) {
      fileObj.status = 'error';
      fileObj.percentage = 0;
      this.uploadStats.failed++;
      this.showUploadStats();
    },
    // 触发文件夹选择
    triggerDirectorySelect() {
      this.$refs.dirInput.click();
    },

    // 处理文件夹选择
    handleDirectoryChange(event) {
      const files = Array.from(event.target.files);
      this.uploadStats.total += files.length;
      files.forEach((file) => {
        const fileObj = {
          uid: Date.now() + Math.random(),
          file,
          name: file.webkitRelativePath,
          percentage: 0,
          status: 'uploading'
        };
        this.uploadList.push(fileObj);
        this.uploadFile(fileObj);
      });
      this.$refs.dirInput.value = null;
    },
    // handleFileChange(file) {
    //   console.log('文件已选择:', file);
    //   // 如果需要手动触发上传，可以在这里调用
    //   this.handleUploadFile({ file: file.raw });
    // }
    showUploadStats() {
      const { success, failed, total } = this.uploadStats;
      if (success + failed === total && total > 0) {
        this.$notify({
          title: "上传统计",
          message: `上传完成：成功 ${success} 个，失败 ${failed} 个，共 ${total} 个文件`,
          position: "bottom-right",
          type: success === total ? "success" : "warning"
        });
      }
    }
  }
};
</script>

<style scoped>
.upload-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.upload-box {
  display: inline-block;
}

.upload-progress {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.upload-progress::-webkit-scrollbar {
  width: 6px;
}

.upload-progress::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

.upload-progress::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.file-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.file-name {
  color: #606266;
  font-size: 14px;
}

.file-status {
  color: #909399;
  font-size: 12px;
}
</style>
