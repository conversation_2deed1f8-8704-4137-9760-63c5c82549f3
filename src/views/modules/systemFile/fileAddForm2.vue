<template>
  <div>
    <el-upload
        ref="upload"
        class="upload-box"
        :http-request="handleUpload"
        :show-file-list="false"
        :auto-upload="false"
        multiple
    >
      <el-button type="primary">选择文件</el-button>
    </el-upload>

    <input
        ref="dirInput"
        type="file"
        style="display: none"
        webkitdirectory
        @change="handleDirectoryChange"
    />
    <el-button @click="triggerDirectorySelect">选择文件夹</el-button>

    <div class="upload-progress" v-if="uploadList.length">
      <div v-for="file in uploadList" :key="file.uid" class="progress-item">
        <div>{{ file.name }}</div>
        <el-progress :percentage="file.percentage" :status="file.status === 'success' ? 'success' : ''" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      uploadList: []
    };
  },
  methods: {
    triggerDirectorySelect() {
      this.$refs.dirInput.click();
    },
    handleDirectoryChange(event) {
      const files = Array.from(event.target.files);
      files.forEach((file) => {
        const fileObj = {
          uid: Date.now() + Math.random(),
          file,
          name: file.webkitRelativePath,
          percentage: 0,
          status: 'uploading'
        };
        this.uploadList.push(fileObj);
        this.uploadFile(fileObj);
      });
      // 清空 input 防止连续选择同一文件夹无效
      this.$refs.dirInput.value = null;
    },
    handleUpload({ file }) {
      const fileObj = {
        uid: Date.now() + Math.random(),
        file,
        name: file.name,
        percentage: 0,
        status: 'uploading'
      };
      this.uploadList.push(fileObj);
      this.uploadFile(fileObj);
    },
    uploadFile(fileObj) {
      // 模拟上传请求
      const formData = new FormData();
      formData.append('file', fileObj.file);
      formData.append('path', fileObj.name); // 相对路径，若是单文件就是文件名

      // 模拟延迟上传
      setTimeout(() => {
        fileObj.percentage = 100;
        fileObj.status = 'success';
      }, 1000);

      // 你可以替换为真实的 axios 上传：
      // axios.post('/upload', formData).then(() => {
      //   fileObj.percentage = 100;
      //   fileObj.status = 'success';
      // });
    }
  }
};
</script>

<style scoped>
.upload-box {
  display: inline-block;
  margin-right: 10px;
}
.upload-progress {
  margin-top: 20px;
}
.progress-item {
  margin-bottom: 15px;
}
</style>
