<template>
  <div>
    <el-form
      :model="form"
      label-width="100px"
      :rules="rules"
      ref="addForm"
      :before-colse="handleDialogClose"
    >
      <div
          style="height: 200px">

        <el-form-item label="迁移目录">
          <treeselect v-model="form.parentNodeId" :options="options" :key="treeselectKey" :normalizer="normalizer" :show-count="true"
                      placeholder="选择要迁移的目录" />
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary" @click="saveBy">保存</el-button>
        <el-button @click="handleDialogClose">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>

import Treeselect from "@riophae/vue-treeselect";
import {
  categorySaveBy,
  categoryTreeBy
} from "@/api/api/assetCategory";
import { fileSystemMobileCategory } from "@/api/api/fileSystem"

export default {
  components: {Treeselect},
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: this.value,
      loading: false,
      options: [],
      treeselectKey: new Date().getTime().toString(),
      rules: {
        parentNodeId: [
          { required: true, message: "请选择上级分类", trigger: "change" }
        ],
        nodeText: [
          { required: true, message: "请输入分组名称", trigger: "change" }
        ],
        sort: [{ required: true, message: "请输入排序", trigger: "change" }]
      },
    };
  },
  watch: {
    form: {
      handler(n) {
        this.$emit("change", n);
      },
      deep: true
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.nodeId,
        label: node.nodeText,
        children: node.children
      };
    },
    refresh() {
      this.$emit("refresh");
      this.$emit("close");
    },
    handleDialogClose() {
      this.$emit("close");
    },
    async init() {
      this.options = []
      const { rows } = await categoryTreeBy({
        catalogueId: window.globalConfig.catalogueId
      });
      const data = { nodeId: "root", nodeText: "根目录", children: [] };
      data.children = this.handleTree(rows, "nodeId");
      this.options.push(data);
    },

    // 保存分类
    saveBy() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          fileSystemMobileCategory({
            ids: this.form.ids,
            category: this.form.parentNodeId
          }).then(res => {
            if (res.state === true) {
              this.$notify({
                message: "保存成功",
                position: "bottom-right",
                type: "success"
              });
            }
            this.refresh();
          });
        } else {
          return false;
        }
      });
    },
  }
};
</script>
