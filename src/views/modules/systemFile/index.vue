<template>
  <div style="display: grid;grid-template-columns: 24% 73%;grid-column-gap:2%;">
    <div class="category-sidebar">
      <div class="category-header">
        <h2 style="display: inline-block;">类目</h2>
        <el-button type="text" size="mini" @click="handleAddGroup(null)"
          >新增分组</el-button
        >
      </div>
      <div class="category-tree-container">
        <el-tree
          :expand-on-click-node="false"
          default-expand-all
          :data="categoryList"
          :props="{ children: 'children' }"
          @node-click="handleNodeClick"
          highlight-current
          :current-node-key="curNodeId"
          style="margin-top: 10px;padding-top: 10px; border-top:1px solid #E4E7ED"
        >
        <span
          class="custom-tree-node"
          slot-scope="{ node, data }"
          :class="{ selected: data.nodeId == curNodeId }"
        >
          <span>{{ data.nodeText }}</span>
          <span>
            <el-button
              type="text"
              icon="el-icon-plus"
              size="mini"
              @click.stop="() => handleAddGroupSub(data)"
            />
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              size="mini"
              @click.stop="() => handleAddGroup(data)"
            />
            <el-button
              type="text"
              icon="el-icon-delete"
              size="mini"
              @click.stop="() => handleDeleteGroup(data)"
            />
          </span>
        </span>
      </el-tree>
      </div>
    </div>
    <div style="margin-top: 10px;">
      <el-form inline size="mini">
        <el-form-item label="文件名" v-if="categoryType !== '数字模型' && categoryType !== '文书库'">
          <el-input v-model="keyword" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="模型名称" v-if="categoryType === '数字模型'">
          <el-input v-model="keyword" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            @click.native="getList"
            type="primary"
            v-if="categoryType !== '文书库'"
            >搜索</el-button
          >
          <el-button
            icon="el-icon-refresh"
            @click.native="refresh"
            type="primary"
            v-if="categoryType !== '文书库'"
            >重置</el-button
          >
          <el-button
            type="danger"
            icon="el-icon-delete"
            :disabled="!selectedRows.length"
            @click="handleBatchDelete"
            v-if="categoryType !== '数字模型' && categoryType !== '文书库'"
            >批量删除</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-position"
            :disabled="!selectedRows.length"
            @click="handleBatchMigration"
            v-if="categoryType !== '数字模型' && categoryType !== '文书库'"
            >批量移动文件</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-folder-checked"
            :disabled="!selectedRows.length"
            @click="handleBatchAnalyze"
            v-if="categoryType && categoryType !== '' && categoryType !== '数字模型' && categoryType !== '文书库'"
            >批量解析</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-folder-checked"
            @click="analyzeBycategory"
            v-if="categoryType && categoryType !== '' && categoryType !== '数字模型' && categoryType !== '文书库'"
            >解析目录下所有文件</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
          border
          max-height="800px"
        :data="tableData"
        v-loading="tableLoading"
        row-key="mxmc"
        :reserve-selection="true"
        @selection-change="handleSelectionChange"
        v-show="categoryType !== '数字模型' && categoryType !== '文书库'"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        />
        <el-table-column label="序号" type="index" width="80" align="center">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column
          label="文件名"
          prop="originalFileName"
        />
        <el-table-column
          label="文件大小"
          prop="fileSize"
          width="130"
        >
          <template slot-scope="scope">
            {{formatFileSize(scope.row.fileSize)}}
          </template>
        </el-table-column>
        <el-table-column
          label="是否解析"
          prop="analyze"
          width="130"
          v-if="categoryType && categoryType !== '' && categoryType !== '数字模型' && categoryType !== '文书库'"
        >
          <template slot-scope="scope">
            <el-tag type="info" v-show="scope.row.analyze == 'false'">未解析</el-tag>
            <el-tag type="success" v-show="scope.row.analyze == 'true'" @click="handleAnalyzeData(scope.row)">已解析</el-tag>
            <el-tag type="danger" v-show="scope.row.analyze == 'fail'">解析失败</el-tag>
            <el-tag type="primary" v-show="scope.row.analyze == 'analyzeing'">解析中</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
          width="200"
        />
        <el-table-column label="操作" width="360" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleViewData(scope.row)"
              >预览</el-button
            >
            <el-button type="text" size="mini" @click="downloadBy(scope.row)">
              下载
            </el-button>
            <el-button type="text" size="mini" @click="handlemigrateCategory(scope.row)">
              移动文件
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="analyze(scope.row)"
              v-if="categoryType && categoryType !== '' && categoryType !== '数字模型' && categoryType !== '文书库'"
            >
              {{scope.row.analyze == 'false' ? '解析':'重新解析'}}
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleDelete(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-table
          border
          max-height="800px"
        :data="digitalModelData"
        v-loading="tableLoading"
        row-key="id"
        v-show="categoryType === '数字模型'"
        :expand-row-keys="expandedRows"
        @row-click="handleRowClick"
      >
        <el-table-column type="expand" width="55" align="center">
          <template slot-scope="props">
            <div class="expand-content">
              <h4 style="margin: 10px 0; color: #409eff;">关联文件列表</h4>
              <div v-if="props.row.files == null"
                   style="text-align: center; color: #999; padding: 20px;">
                暂无关联文件
              </div>
              <el-table
                  v-else
                :data="parseFiles(props.row.files)"
                border
                size="mini"
                style="margin: 10px 0;"
              >
                <el-table-column label="序号" type="index" width="60" align="center">
                  <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column label="文件名" prop="originalFileName" min-width="200" />
                <el-table-column label="文件大小" prop="fileSize" width="100" align="center">
                  <template slot-scope="scope">
                    {{ formatFileSize(scope.row.fileSize) }}
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime" width="180" align="center" />
                <el-table-column label="操作" width="250" align="center">
                  <template slot-scope="scope">
                    <el-button
                        type="text"
                        size="mini"
                        @click="handleViewData(scope.row)"
                    >预览</el-button
                    >
                    <el-button
                        type="text"
                        size="mini"
                        @click="downloadBy(scope.row)"
                    >下载</el-button
                    >
                    <el-button
                        type="text"
                        size="mini"
                        @click="handleDeleteExpandFile(scope.row, props.row,props.$index)"
                    >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="80" align="center">
          <template slot-scope="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <el-table-column
          label="模型名称"
          prop="mxmc"
        />
        <el-table-column
          label="模型领域"
          prop="mxly"
        />
        <el-table-column
          label="模型介绍"
          prop="mxjs"
          width="200"
        >
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.mxjs"
              placement="top"
              :disabled="!scope.row.mxjs || scope.row.mxjs.length <= 60"
              effect="dark"
              popper-class="model-intro-tooltip"
            >
              <div class="model-intro-text">
                {{ scope.row.mxjs }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="条线"
          prop="tx"
          width="150"
        />
        <el-table-column
          label="模型数据"
          prop="mxsj"
        >
        <template slot-scope="scope">
          <el-tooltip
              :content="scope.row.mxsj"
              placement="top"
              :disabled="!scope.row.mxsj || scope.row.mxsj.length <= 60"
              effect="dark"
              popper-class="model-intro-tooltip"
          >
            <div class="model-intro-text">
              {{ scope.row.mxsj }}
            </div>
          </el-tooltip>
        </template>
        </el-table-column>
        <el-table-column
          label="数据个数"
          prop="sjgs"
          width="90"
        />
        <el-table-column
          label="上架时间"
          prop="sjsj"
          width="180"
        />
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="uploadDataModel(scope.row.id,scope.$index)"
              >上传</el-button
            >
            <el-button
              type="text"
              size="mini"
              @click="handleDeleteDtatModel(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div v-show="categoryType === '文书库'" class="document-library-container">
        <div class="document-library-content">
          <h1 class="document-library-title">裁判文书库</h1>
          <div class="search-container">
            <el-input
                size="medium"
              v-model="documentSearchKeyword"
              placeholder="请输入文书名称或关键词"
              class="search-input"
              clearable
              @keyup.enter.native="handleDocumentSearch"
            >
            </el-input>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-search"
              @click="handleDocumentSearch"
              class="search-button"
            >
              搜索
            </el-button>
          </div>
        </div>
      </div>
      <div class="block" style="float: right;margin: 16px 0;">
        <el-pagination
            v-if="categoryType !== '文书库'"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.pageNo"
          :page-size="listQuery.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <previewFile
        :row-data="rowData"
        :dialog-visible="previewDialog"
        @close="handleViewDataClose"
      ></previewFile>
      <!--弹框 -->
      <el-dialog
        :visible.sync="dialogVisible"
        :width="width"
        :close-on-click-modal="false"
        :before-close="handleClose"
        top="10vh"
      >
        <div class="dialog-wrapper">
          <div class="dialog-header">
            <span>{{ title }}</span>
            <i class="el-icon-close" @click="handleClose" />
          </div>
          <div class="dialog-container">
            <component
              :is="dialogType"
              v-model="form"
              @refresh="refresh"
              @close="close"
              v-if="dialogVisible"
              style="max-height: 75vh;overflow: auto;padding:0 50px"
            ></component>
          </div>
        </div>
      </el-dialog>
      <!-- 添加解析数据弹出框 -->
      <el-dialog
        title="解析数据"
        :visible.sync="analyzeDataDialogVisible"
        width="50%"
        top="15px"
      >

        <div class="dialog-wrapper">
          <div class="dialog-header">
            <span>解析内容</span>
            <i class="el-icon-close" @click="analyzeDataDialogVisible = false" />
          </div>
          <div class="dialog-container">
            <div v-loading="analyzeDataLoading">
              <pre v-if="analyzeData" style="white-space: pre-wrap; word-wrap: break-word;">{{ analyzeData }}</pre>
              <div v-else>暂无解析数据</div>
            </div>
          </div>
        </div>
      </el-dialog>
      <!-- 文件上传弹框 -->
      <el-dialog
        title="上传文件"
        :visible.sync="uploadDialogVisible"
        width="400px"
        @close="resetUploadDialog"
      >
        <div class="dialog-header">
          <span>文件上传</span>
          <i class="el-icon-close" @click="close" />
        </div>
        <div class="dialog-container">
        <el-upload
            style="margin-bottom: 30px;"
            ref="fileUpload"
          :action="uploadUrl"
          :show-file-list="true"
          :auto-upload="true"
          :http-request="uploadFile"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :file-list="uploadFileList"
        >
          <el-button slot="trigger" type="primary">选择文件</el-button>
          <div slot="tip" class="el-upload__tip">只能上传一个文件</div>
        </el-upload>
        <span slot="footer" class="dialog-footer" style="margin-top: 30px;margin-left: 200px">
          <el-button @click="uploadDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload">保 存</el-button>
        </span>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>

import {
  fileSystemAnalyze,
  fileSystemAnalyzeBycategory,
  fileSystemAnalyzeData,
  fileSystemDelete, fileSystemMobileCategory,
  fileSystemPageList
} from "@/api/api/fileSystem"
import { downloadFile } from "@/utils/MinioDownload"
import PreviewFile from "@/views/modules/systemFile/previewFile.vue"
import { Base64 } from "js-base64"
import CategoryForm from "./CategoryForm.vue";
import migrateCategoryForm from "./migrateCategoryForm.vue";
import { catalogueDeleteBy, categoryDeleteBy, categoryTreeBy } from "@/api/api/assetCategory"
import {
  digitalModelDelete,
  digitalModelDeleteFiles,
  digitalModelPageList,
  digitalModelSaveFiles
} from "@/api/api/digitalModel"
import { getToken } from "@/utils/auth"
import { unitySkipApp } from "@/api/system/login"

export default {
  components: {
    PreviewFile,CategoryForm,migrateCategoryForm
  },
  data() {
    return {
      categoryType: "",
      curNodeId: "root",
      tableData: [],
      categoryList: [],
      digitalModelData: [],
      total: 0,
      tableLoading: false,
      previewDialog: false,
      rowData: {},
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        searchFilters: {},
        filters: {}
      },
      keyword: "",
      documentSearchKeyword: "", // 文书库搜索关键词
      dialogType: "默认组件名",
      title: "默认标题",
      width: "50",
      dialogVisible: false,
      form: {},
      rules: {},
      props: {
        leaf: "isLeaf",
        multiple: true,
        emitPath: false,
        label: "text"
      },
      options: [],
      authLoading: false,
      authVisible: false,
      catalogueId: window.globalConfig.catalogueId,
      groupData: [], //分组列表
      selectedRows: [], // 新增：存储选中的行
      analyzeDataDialogVisible: false,
      analyzeDataLoading: false,
      analyzeData: null,
      expandedRows: [], // 数字模型表格展开状态
      uploadDialogVisible: false,
      uploadRowId: null,
      uploadRowIndex: null,
      uploadFileList: [],
      uploadUrl: API.baseAPI + "/rest/system/file/upload", // 替换为实际上传接口
      uploadDataId: 0,
      formFileList:[]
    };
  },
  mounted() {
    // this.getGroupTree();
    this.getCategoryList()
    this.getList();
  },
  methods: {
    analyze(row) {
      this.$confirm("是否开始解析?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(_ => {
            fileSystemAnalyze({
              type: this.categoryType,
              ids: row.id,
              api: window.globalConfig.analyzeAPI
            }).then(res=>{
              if (res.state) {
                this.$notify({
                  title: "操作提示",
                  message: "已加入解析队列",
                  position: "bottom-right",
                  type: "success"
                });
                this.getList()
              }
            })
          })
          .catch(e => {
            console.log("取消删除", e);
          });
    },
    analyzeBycategory() {
      this.$confirm("是否开始对目录下所有文件进行解析?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(_ => {
            fileSystemAnalyzeBycategory({
              type: this.categoryType,
              category: this.listQuery.filters.category,
              api: window.globalConfig.analyzeAPI
            }).then(res=>{
              if (res.state) {
                this.$notify({
                  title: "操作提示",
                  message: "已加入解析队列",
                  position: "bottom-right",
                  type: "success"
                });
                this.getList()
              }
            })
          })
          .catch(e => {
            console.log("取消删除", e);
          });
    },
    async getCategoryList() {
      this.categoryList = []
      const { rows } = await categoryTreeBy({
        catalogueId: this.catalogueId
      });
      const data = { nodeId: "root", nodeText: "根目录", children: [] };
      data.children = this.handleTree(rows, "nodeId");
      this.categoryList.push(data)
    },
    formatFileSize(size) {
      let sizeName;
      if (1024 * 1024 > size && size >= 1024) {
        sizeName = (size / 1024).toFixed(2) + "KB";
      } else if (1024 * 1024 * 1024 > size && size >= 1024 * 1024) {
        sizeName = (size / (1024 * 1024)).toFixed(2) + "MB";
      } else if (size >= 1024 * 1024 * 1024) {
        sizeName = (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
      } else {
        sizeName = size + "B";
      }
      return sizeName;
    },
    downloadBy(inRow) {
      downloadFile(MinioFileHost + inRow.url,inRow.originalFileName)
    },
    uploadFile(fileObj) {
      const formData = new FormData();
      formData.append('file', fileObj.file);
      formData.append('verifyRep', false); // 是否校验文件已存在


      // 创建上传请求
      const xhr = new XMLHttpRequest();
      xhr.open('POST', this.uploadUrl, true);
      // 处理上传完成
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            response.absolutePath = response.originalFileName
            response.originalFileName = response.originalFileName.split('/').pop();

            this.formFileList.push(response);

          } catch (error) {
            this.handleUploadError(fileObj, '解析响应失败');
          }
        } else {
          this.handleUploadError(fileObj, '上传失败');
        }
      };

      // 处理上传错误
      xhr.onerror = () => {
        this.handleUploadError(fileObj, '网络错误');
      };

      // 发送请求
      xhr.send(formData);
    },
    closeAuth() {
      this.authVisible = false;
    },
    async getDeptAndPersonTree() {
      this.options = await getDeptAndPersonTree();
    },
    submitAuth(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.authLoading = true;
          let form = JSON.parse(JSON.stringify(this.form));
          form.ids =
            "," + this.form.ids.filter(data => data !== "").join(",") + ",";
          if (form.ids === ",,") {
            form.ids = "";
          }

          sendBy({
            ...form
          })
            .then(res => {
              this.authLoading = false;
              this.$notify({
                title: "提示",
                message: res.msg,
                position: "bottom-right",
                type: res.state ? "success" : "error"
              });
              if (res.state) {
                this.closeAuth();
                this.getList();
              }
            })
            .catch(e => {
              this.authLoading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleViewData(row) {
      // this.rowData = row;
      // this.previewDialog = true;
      const previewUrl = PreviewFilePath + encodeURIComponent(Base64.encode(MinioFileHost + row.url))
      window.open(previewUrl)
    },
    handleViewDataClose() {
      this.rowData = {};
      this.previewDialog = false;
    },
    // 分组数据列表
    async getGroupTree() {
      this.groupData = [];
      const { rows } = await categoryTreeBy({
        catalogueId: window.globalConfig.catalogueId
      });
      let data = { nodeId: "root", nodeText: "根目录", children: [] };
      data.children = rows;
      this.groupData.push(data);
    },
    // 添加子级
    handleAddGroupSub(data) {
      this.dialogVisible = true;
      this.dialogType = "categoryForm";
      this.title = "添加分组";
      this.width = "40%";
      this.form = {
        parentNodeId: data.nodeId,
        nodeId: "YLS" + new Date().valueOf(),
        sort: 1
      };
    },
    // 移动文件目录
    handlemigrateCategory(row) {
      this.dialogVisible = true;
      this.dialogType = "migrateCategoryForm";
      this.title = "目录移动文件";
      this.width = "40%";
      this.form = {
        parentNodeId: "root",
        ids: row.id,
      };
    },
    // 移动文件目录
    handleBatchMigration() {

      const ids = this.selectedRows.map(row => row.id).join(',');
      if (!this.selectedRows.length) {
        this.$message.warning('请选择要移动文件的文件');
        return;
      }
      this.dialogVisible = true;
      this.dialogType = "migrateCategoryForm";
      this.title = "目录移动文件";
      this.width = "40%";
      this.form = {
        parentNodeId: "root",
        ids: ids,
      };
    },
    // 添加分组
    handleAddGroup(row) {
      this.dialogVisible = true;
      this.dialogType = "categoryForm";
      this.title = "添加分组";
      this.width = "40%";
      if (row) {
        this.form = JSON.parse(JSON.stringify(row));
      } else {
        this.form = {
          parentNodeId: "root",
          nodeId: "YLS" + new Date().valueOf(),
          sort: 1
        };
      }
    },
    // 删除分组
    handleDeleteGroup(data) {
      this.$confirm("确定删除分组?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(_ => {
          categoryDeleteBy({ ids: data.id }).then(res => {
            if (res.state) {
              this.$message.success("操作成功")
              this.getCategoryList()
            } else {
              this.$message.error(res.msg)
            }
          });
        })
        .catch(e => {
          console.log("取消删除", e);
        });
    },
    close() {
      this.dialogVisible = false;
    },
    refresh() {
      this.keyword = ""
      // this.listQuery = {
      //   pageNo: 1,
      //   pageSize: 10,
      //   searchFilters: {},
      //   filters: {}
      // }
      this.getList();
      this.getCategoryList()
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNo = val;
      this.getList();
    },
    async getList() {
      this.formFileList = []
      this.expandedRows = []
      this.tableLoading = true;
      if (this.categoryType === '数字模型') {

        if (this.keyword) {
          this.listQuery.searchFilters.mxmc = this.keyword;
        } else {
          delete this.listQuery.searchFilters.mxmc;
        }
        let query = JSON.parse(JSON.stringify(this.listQuery))
        query.filters = JSON.stringify(this.listQuery.filters)
        query.searchFilters = JSON.stringify(this.listQuery.searchFilters)


        query.filters = JSON.stringify({})
        this.tableLoading = true;
        const { total, rows } = await digitalModelPageList(query);
        this.digitalModelData = rows;
        this.total = total;
        this.tableLoading = false;
      } else {

        if (this.keyword) {
          this.listQuery.searchFilters.originalFileName = this.keyword;
        } else {
          delete this.listQuery.searchFilters.originalFileName;
        }
        let query = JSON.parse(JSON.stringify(this.listQuery))
        query.filters = JSON.stringify(this.listQuery.filters)
        query.searchFilters = JSON.stringify(this.listQuery.searchFilters)

        const { total, rows } = await fileSystemPageList(query);
        this.tableData = rows;
        this.total = total;
        this.tableLoading = false;
      }
    },
    handleNodeClick(val) {
      this.curNodeId = val.nodeId;
      if (val.nodeId !== 'root') {
        this.listQuery.filters.category = val.nodeId;
        this.categoryType = val.type;
      } else {
        delete this.listQuery.filters.category;
        this.categoryType = ""
      }
      this.getList();
    },
    // 删除文件
    handleDelete(id) {
      this.$confirm("确定删除该数据集?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(_ => {
          fileSystemDelete({ ids: id }).then(res => {
            this.$notify({
              title: "操作提示",
              message: res.msg,
              position: "bottom-right",
              type: res.state ? "success" : "error"
            });
            this.getList();
          });
        })
        .catch(e => {
          console.log("取消删除", e);
        });
    },
    // 新增：处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 新增：批量删除
    handleBatchDelete() {
      if (!this.selectedRows.length) {
        this.$message.warning('请选择要删除的文件');
        return;
      }

      this.$confirm("确定删除选中的文件?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(_ => {
          const ids = this.selectedRows.map(row => row.id).join(',');
          fileSystemDelete({ ids }).then(res => {
            this.$notify({
              title: "操作提示",
              message: res.msg,
              position: "bottom-right",
              type: res.state ? "success" : "error"
            });
            this.getList();
          });
        })
        .catch(e => {
          console.log("取消删除", e);
        });
    },
    handleBatchAnalyze() {
      if (!this.selectedRows.length) {
        this.$message.warning('请选择要解析的文件')
        return
      }

      this.$confirm("确定解析选中的文件?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(_ => {
            const ids = this.selectedRows.map(row => row.id).join(',')
            fileSystemAnalyze({
              type: this.categoryType,
              ids: ids,
              api: window.globalConfig.analyzeAPI
            }).then(res => {
              if (res.state) {
                this.$notify({
                  title: "操作提示",
                  message: "已加入解析队列",
                  position: "bottom-right",
                  type: "success"
                })
                this.getList()
              }
            })
          })
          .catch(e => {
            console.log("取消删除", e)
          })
    },
    async handleAnalyzeData(row) {
      if (row.analyze !== 'true') {
        this.$message.warning('该文件尚未解析完成')
        return
      }

      this.analyzeDataDialogVisible = true
      this.analyzeDataLoading = true

      try {
        const res = await fileSystemAnalyzeData({
          id: row.id
        })

        if (res.state) {
          this.analyzeData = res.msg
        } else {
          this.$message.error(res.msg || '获取解析数据失败')
        }
      } catch (error) {
        console.error('获取解析数据出错:', error)
        this.$message.error('获取解析数据失败')
      } finally {
        this.analyzeDataLoading = false
      }
    },

    // 解析files字符串为数组
    parseFiles(filesString) {
      if (!filesString) return []
      try {
        return JSON.parse(filesString)
      } catch (error) {
        console.error('解析files数据失败:', error)
        return []
      }
    },
    handleDeleteDtatModel(row) {
      console.log(row)
      this.$confirm("确定删除该数据模型?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
      .then(_ => {
        digitalModelDelete({
          ids: row.id
        }).then(res => {
          if (res.state) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
      .catch(e => {
      })
    },

    // 删除展开表格中的文件
    handleDeleteExpandFile(file, parentRow,index) {
      console.log(index)
      // 打印父表格的ID
      this.$confirm(`确定删除文件 "${file.originalFileName}" ?`, "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
      .then(_ => {
        digitalModelDeleteFiles({
          fileId: file.id,
          dataId: parentRow.id
        }).then(res => {
          if (res) {
            this.$message.success('删除成功')
            // this.getList()
            this.$set(this.digitalModelData[index],'files' ,JSON.stringify(res) )
          } else {
            this.$message.error(res.msg)
          }
        })
      })
      .catch(e => {
      });
    },

    // 处理数字模型表格行点击事件
    handleRowClick(row, column, event) {
      // 如果点击的是操作列，不触发展开
      if (column && column.label === '操作') {
        return;
      }

      // 切换展开状态
      const rowKey = row.id;
      const index = this.expandedRows.indexOf(rowKey);

      if (index > -1) {
        // 如果已展开，则收起
        this.expandedRows.splice(index, 1);
      } else {
        // 如果未展开，则展开
        this.expandedRows.push(rowKey);
      }
    },
    uploadDataModel(id,index) {
      this.uploadDialogVisible = true;
      this.uploadRowId = id;
      this.uploadRowIndex = index;
      this.uploadFileList = [];
    },
    uploadExtraData() {
      return { id: this.uploadRowId };
    },
    submitUpload() {
      digitalModelSaveFiles({
        id: this.uploadRowId,
        catalogueId: this.catalogueId,
        fileList: JSON.stringify(this.formFileList)
      }).then(res=>{
        if (res.state){
          const file = JSON.parse(res.msg)
          this.$set(this.digitalModelData[this.uploadRowIndex],'files' ,JSON.stringify(file) )
        }
        this.uploadDialogVisible = false
        this.uploadRowId = null;
        this.uploadRowIndex = null;
        this.formFileList = []
      })
    },
    handleUploadSuccess(response, file, fileList) {
      this.$message.success('上传成功');
      this.uploadDialogVisible = false;
      this.getList();
    },
    handleUploadError(err) {
      this.$message.error('上传失败');
    },
    beforeUpload(file) {
      // 可加文件类型/大小校验
      return true;
    },
    resetUploadDialog() {
      this.uploadFileList = [];
      this.uploadRowId = null;
    },

    getOtherTicket() {
      const {
        ak: configAk,
        sk: configSk,
        url: configUrl,
        suffix: suffix
      } = window.globalConfig.JudicialDocument;
      const token = getToken();
      let href = "#";
      unitySkipApp(token, configAk).then(res => {
        if (res.code === 2000) {
          if (suffix == null) {
            href = configUrl + "?ticket=" + res.ticket;
          } else {
            href = configUrl + "?ticket=" + res.ticket + "&" + suffix;
          }
        }
      });
      return href
    },

    // 文书库搜索方法
    handleDocumentSearch() {
      console.log('文书库搜索关键词:', this.documentSearchKeyword);
      // TODO: 在这里添加文书库搜索的具体逻辑
      // 例如调用API、过滤数据等
      if (this.documentSearchKeyword.trim()) {
        // this.$message.success(`搜索关键词: ${this.documentSearchKeyword}`);
        const url = this.getOtherTicket() + this.documentSearchKeyword;
        console.log(url)
        window.open(url)

      } else {
        this.$message.warning('请输入搜索关键词');
      }
    }
  }
};
</script>
<style scoped>

::v-deep .cell .el-button {
  border-radius: 1px !important;
  padding: 2px 4px !important;

}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.selected {
  color: #409eff;
}
.el-tree-node.is-current > .el-tree-node__content {
  background-color: #f5f7fa;
  color: #409eff;
}
.el-tag {
  cursor: pointer;
}
.el-tag:hover {
  opacity: 0.8;
}

/* 模型介绍文本样式 */
.model-intro-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 4.2em; /* 3行 × 1.4行高 = 4.2em */
  word-break: break-word;
  cursor: pointer;
}

/* 表格内按钮悬停效果 */
::v-deep .el-table .el-button--text {
  color: #409eff;
  transition: color 0.3s ease;
}

::v-deep .el-table .el-button--text:hover {
  color: #337ecc !important;
  background-color: transparent;
}

/* 展开内容样式 */
.expand-content {
  padding-left: 15px;
  padding-right: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 10px;
}

.expand-content h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
}

.expand-content .el-table {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 左侧类目样式 */
.category-sidebar {
  height: calc(100vh - 120px); /* 根据页面布局调整高度 */
  display: flex;
  flex-direction: column;
}

.category-header {
  flex-shrink: 0; /* 头部不缩放 */
  padding-bottom: 10px;
  border-bottom: 1px solid #E4E7ED;
  margin-bottom: 10px;
}

.category-tree-container {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  padding-right: 5px; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.category-tree-container::-webkit-scrollbar {
  width: 6px;
}

.category-tree-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.category-tree-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.category-tree-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 数字模型表格行点击样式 */
.el-table tbody tr {
  cursor: pointer;
}

.el-table tbody tr:hover {
  background-color: #f5f7fa !important;
}

/* 文书库搜索容器样式 */
.document-library-container {
  padding: 60px 20px;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-library-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.document-library-title {
  font-size: 36px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 40px 0;
  letter-spacing: 2px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.search-input {
  flex: 1;
}

.search-input .el-input__inner {
  height: 40px;
  font-size: 14px;
  border-radius: 6px;
}

.search-button {
  height: 40px;
  padding: 0 20px;
  border-radius: 6px;
  font-size: 14px;
}

</style>

<style>
/* 模型介绍tooltip样式 - 全局样式 */
.model-intro-tooltip {
  max-width: 400px !important;
}

.model-intro-tooltip[x-placement^="top"] .popper__arrow,
.model-intro-tooltip[x-placement^="bottom"] .popper__arrow,
.model-intro-tooltip[x-placement^="left"] .popper__arrow,
.model-intro-tooltip[x-placement^="right"] .popper__arrow {
  border-color: #303133 transparent transparent transparent !important;
}

.model-intro-tooltip .el-tooltip__popper,
.model-intro-tooltip.el-tooltip__popper {
  max-width: 400px !important;
  width: 400px !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  line-height: 1.5 !important;
  padding: 10px !important;
  font-size: 12px !important;
}
</style>
