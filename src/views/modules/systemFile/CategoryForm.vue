<template>
  <div >
    <el-form
      :model="form"
      label-width="100px"
      :rules="rules"
      ref="addForm"
      :before-colse="handleDialogClose"
    >
      <el-form-item label="上级菜单">
        <treeselect v-model="form.parentNodeId" :options="options" :key="treeselectKey" :normalizer="normalizer" :show-count="true"
                    placeholder="选择上级菜单" />
      </el-form-item>
      <el-form-item label="分类名称" prop="nodeText">
        <el-input
          v-model="form.nodeText"
          placeholder="请输入分类名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="解析分类" prop="nodeText"
                    placeholder="请选择解析分类">
        <el-select v-model="form.type">
          <el-option value="刑事判决书" label="刑事判决书"></el-option>
          <el-option value="起诉决定书" label="起诉决定书"></el-option>
          <el-option value="不起诉决定书" label="不起诉决定书"></el-option>
          <el-option value="文书库" label="文书库"></el-option>
          <el-option value="理论研究" label="理论研究"></el-option>
          <el-option value="数字模型" label="数字模型"></el-option>
          <el-option value="" label="不可解析"></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="唯一Id" prop="nodeId">-->
<!--        <el-input-->
<!--          :disabled="!!form.id"-->
<!--          v-model="form.nodeId"-->
<!--          placeholder="请输入唯一Id"-->
<!--        ></el-input>-->
<!--      </el-form-item>-->
      <el-form-item label="同级排序" prop="sort">
        <el-input-number v-model="form.sort"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveBy">保存</el-button>
        <el-button @click="handleDialogClose">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>

import Treeselect from "@riophae/vue-treeselect";
import {
  categorySaveBy,
  categoryTreeBy
} from "@/api/api/assetCategory";

export default {
  components: {Treeselect},
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: this.value,
      loading: false,
      options: [],
      treeselectKey: new Date().getTime().toString(),
      rules: {
        parentNodeId: [
          { required: true, message: "请选择上级分类", trigger: "change" }
        ],
        nodeText: [
          { required: true, message: "请输入分组名称", trigger: "change" }
        ],
        sort: [{ required: true, message: "请输入排序", trigger: "change" }]
      },
    };
  },
  watch: {
    form: {
      handler(n) {
        this.$emit("change", n);
      },
      deep: true
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.nodeId,
        label: node.nodeText,
        children: node.children
      };
    },
    refresh() {
      this.$emit("refresh");
      this.$emit("close");
    },
    handleDialogClose() {
      this.$emit("close");
    },
    async init() {
      this.options = []
      const { rows } = await categoryTreeBy({
        catalogueId: window.globalConfig.catalogueId
      });
      const data = { nodeId: "root", nodeText: "根目录", children: [] };
      data.children = this.handleTree(rows, "nodeId");
      this.options.push(data);
    },
    // 保存分类
    saveBy() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          this.form.catalogueId = window.globalConfig.catalogueId
          categorySaveBy(this.form).then(res => {
            if (res.state === true) {
              this.$notify({
                message: "保存成功",
                position: "bottom-right",
                type: "success"
              });
            }
            this.refresh();
          });
        } else {
          return false;
        }
      });
    },
  }
};
</script>
