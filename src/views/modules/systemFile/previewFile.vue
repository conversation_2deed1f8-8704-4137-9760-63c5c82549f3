<template>
  <el-dialog
      :visible="dialogVisible"
      append-to-body
      width="80%"
      top="10vh"
      :before-close="handleDialogClose"
  >
    <div class="dialog-header">
      <span>文件预览</span>
      <i class="el-icon-close" @click="handleDialogClose" />
    </div>
    <div class="dialog-container">
      <template>
        <iframe :src="previewUrl" style="width: 100%;height: 70vh;border: none"/>
      </template>
    </div>
  </el-dialog>
</template>
<script>
let Base64 = require('js-base64').Base64
export default {
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    },
    dialogVisible:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      previewUrl: '',
      loading: false,
    }
  },
  watch: {
    rowData(val) {
      console.log(val)
      if (val) {
        this.init()
      }
    }
  },
  mounted() {
  },
  methods: {
    handleDialogClose() {
      this.loading = false
      this.$emit("close");
    },
    async init() {
      this.loading = true
      this.previewUrl = PreviewFilePath + encodeURIComponent(Base64.encode(MinioFileHost + this.rowData.url)) + '&officePreviewType=pdf'
    }
  }
};
</script>
<style>
.pane-title {
  font-weight: bolder;
  font-size: 18px;
  margin: 10px 0;
}
.pane-title::before {
  width: 10px;
  height: 100%;
  margin-right: 5px;
  color: #2a6599; /* 设置标志颜色 */;
}
</style>
