<template>
  <div class="audio-chat-container">
    <template v-if="!inChat">
      <div class="welcome-area">
        <img src="./img/audio.png" class="welcome-icon" alt="语音助手" />
        <div class="welcome-title">语音转文字助手</div>
        <div class="welcome-desc">请点击下方按钮上传音频文件</div>
        <el-upload
          class="upload-btn"
          :action="uploadUrl"
          :on-success="handleFirstUploadSuccess"
          :before-upload="beforeUpload"
          :data="inChatBody"
          accept=".mp3,.wav,.aac,.m4a,.ogg,.flac,.webm,.amr,.opus,.3gp,.aiff,.oga,.wma,.fac,.weba"
          :show-file-list="false"
          :disabled="uploading"
          :http-request="customRequest"
        >
          <el-button
            type="primary"
            class="long-btn"
            :loading="uploading"
            :disabled="uploading"
            >{{ uploading? '正在上传音频' : '点击上传音频文件'}}
            </el-button
          >
        </el-upload>
      </div>
    </template>
    <template v-else>
      <div class="chat-area" ref="chatArea">
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="['message', message.type]"
        >
          <div class="bubble">
            <template v-if="message.loading && !message.cancelled">
              <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                <div style="flex-grow: 1; margin-right: 10px;">
                  <span class="loading-text">音频文件解析中 ({{ message.percent }}%)</span>
                  <el-progress
                    :percentage="message.percent"
                    :stroke-width="6"
                    :show-text="false"
                    style="margin-top: 5px;">
                  </el-progress>
                </div>

          <i class="el-icon-circle-close delete-icon" @click="cancelPolling(message)"></i>
                <!-- <el-button
                  type="danger"
                  icon="el-icon-close"
                  size="mini"
                  circle
                  @click="cancelPolling(message)"
                  title="取消解析">
                </el-button> -->
              </div>
            </template>
            <template v-else-if="message.type === 'system' && message.cancelled">
              {{ message.content }}
              <el-button type="text" size="mini" @click="retryParsing(message)" style="margin-left: 10px;">重新解析</el-button>
            </template>
            <template v-else-if="message.type === 'system' && !message.loading && message.content && message.content.startsWith('音频在解析时被中断') && message.taskId">
              {{ message.content }}
              <el-button type="text" size="mini" @click="retryParsing(message)" style="margin-left: 10px;">重新解析</el-button>
            </template>
            <template v-else-if="message.type === 'system' && message.isParsedSuccessfully && message.fileName">
              <div>
                <div class="message-title-bar">
                  <span class="file-title" :title="message.fileName">{{ message.fileName }}</span>
                  <div class="action-buttons">
                    <i v-if="message.segments && message.segments.length > 0"
                       :class="['el-icon-chat-line-round', 'switch-view-button', { 'active': message.showSegments }]"
                       @click="toggleViewMode(message)"
                       :title="message.showSegments ? '切换到完整文本' : '切换到分段对话'"></i>
                    <i class="el-icon-document-copy copy-icon-button" @click="copyToClipboard(message.content)" title="复制内容"></i>
                    <i class="el-icon-download download-icon-button" @click="downloadAsText(message.content, message.fileName)" title="下载为txt文件"></i>
                  </div>
                </div>

                <!-- 完整文本显示 -->
                <div v-if="!message.showSegments" class="parsed-content-text">{{ message.content }}</div>

                <!-- 分段对话显示 -->
                <div v-else class="segments-container">
                  <div v-for="(segment, segIndex) in message.segments"
                       :key="segIndex"
                       :class="['segment-message', segment.speaker % 2 === 0 ? 'left' : 'right']">
                    <div class="segment-bubble" :data-speaker="segment.speaker">
                      <div class="speaker-label">说话人 {{ segment.speaker + 1 }}</div>
                      <div class="segment-text">{{ segment.text }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              {{ message.content }}
            </template>
          </div>
          <i v-if="message.type !== 'system' || !message.loading" class="el-icon-delete delete-icon" @click="deleteMessage(index)"></i>
        </div>
      </div>
      <div class="upload-area">
        <div class="button-group">
          <el-upload
            class="upload-btn"
            :action="uploadUrl"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :data="inChatBody"
            accept=".mp3,.wav,.aac,.m4a,.ogg,.flac,.webm,.amr,.opus,.3gp,.aiff,.oga,.wma,.fac,.weba"
            :show-file-list="false"
            :disabled="uploading || isAnyMessageLoading"
            :http-request="customRequest"
          >
            <el-button
              type="primary"
              class="long-btn"
              :loading="uploading"
              :disabled="uploading || isAnyMessageLoading"
              >{{ uploading? '正在上传音频' : '点击上传音频文件'}}</el-button
            >
          </el-upload>
          <el-button
            type="danger"
            icon="el-icon-delete"
            class="clear-btn"
            @click="clearAllMessages"
            :disabled="messages.length === 0 || isAnyMessageLoading"
            >清除所有对话</el-button
          >
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import axios from 'axios';
import { getToken } from "@/utils/auth" // 导入 axios

// 简单的动态点点组件
const DotLoading = {
  name: "DotLoading",
  data() {
    return { dotCount: 1 };
  },
  mounted() {
    this.timer = setInterval(() => {
      this.dotCount = (this.dotCount % 3) + 1;
    }, 500);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  render(h) {
    return h(
      "span",
      Array(this.dotCount)
        .fill(".")
        .join("")
    );
  }
};

export default {
  name: "AudioChat",
  components: { DotLoading },
  data() {
    return {
      inChatBody: { silentThreshold: 95 },
      inChat: false,
      messages: [
        // 对话页初始消息可为空
      ],
      uploadUrl: API.chatAPI + "/jandar-audio/v1/audio/processLine",
      getResultUrlBase: API.chatAPI + "/jandar-audio/v1/audio/getProceedResult", // 新增获取结果的URL基础部分
      uploading: false,
      storageKey: "audioChatMessages", // 用于 localStorage 的键名
      pollingInterval: 1000, // 轮询间隔，单位毫秒
    }
  },
  computed: {
    isAnyMessageLoading() {
      return this.messages.some(msg => msg.loading && !msg.cancelled);
    }
  },
  mounted() {
    this.loadMessages();
    if (this.messages.length > 0) {
      this.inChat = true;
    }
    this.scrollToBottom(); // Scroll on mount
  },
  updated() {
    this.scrollToBottom(); // Scroll on any DOM update related to data changes
  },
  methods: {
    scrollToBottom() {
      this.$nextTick(() => {
        const chatArea = this.$refs.chatArea;
        if (chatArea) {
          chatArea.scrollTop = chatArea.scrollHeight;
        }
      });
    },
    saveMessages() {
      localStorage.setItem(this.storageKey, JSON.stringify(this.messages));
    },
    loadMessages() {
      const savedMessages = localStorage.getItem(this.storageKey);
      if (savedMessages) {
        this.messages = JSON.parse(savedMessages).map(msg => {
          // For messages from older storage that don't have this flag, default to false.
          // New messages will have it explicitly set true/false.
          if (typeof msg.isParsedSuccessfully !== 'boolean') {
            msg.isParsedSuccessfully = false;
          }

          // 为旧数据添加 segments 和 showSegments 默认值
          if (msg.isParsedSuccessfully && !msg.segments) {
            msg.segments = [];
          }
          if (msg.isParsedSuccessfully && typeof msg.showSegments !== 'boolean') {
            msg.showSegments = false;
          }

          if (msg.type === 'system' && msg.loading && (msg.cancelled === undefined || msg.cancelled === false) ) {
            msg.loading = false;
            msg.percent = 0;
            const baseErrorMsg = "音频在解析时被中断";
            msg.content = msg.taskId ? `${baseErrorMsg} (任务ID: ${msg.taskId})` : baseErrorMsg;
            if (msg.timerId) {
              msg.timerId = null;
            }
            msg.isParsedSuccessfully = false; // Explicitly set to false for interrupted messages
          }
          return msg;
        });
        if (this.inChat || this.messages.length > 0) {
           this.scrollToBottom();
        }
      }
    },
    deleteMessage(index) {
      this.messages.splice(index, 1);
      this.saveMessages();
      if (this.messages.length === 0) {
        // 如果清空了所有消息，可以选择是否返回欢迎页
        // this.inChat = false;
        // localStorage.removeItem(this.storageKey); // 或者直接清除 storage
      }
    },
    clearAllMessages() {
      if (this.isAnyMessageLoading) {
        this.$message.warning('有文件正在解析中，无法清除对话。');
        return;
      }
      this.$confirm('确定要清除所有对话记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.messages = [];
        this.saveMessages();
        localStorage.removeItem(this.storageKey);
        this.inChat = false;
        this.$message({
          type: 'success',
          message: '对话已清除!'
        });
      }).catch(() => {
        // 用户取消操作
      });
    },
    beforeUpload(file) {
      const isAudio = file.type.startsWith("audio/");
      if (!isAudio) {
        this.$message.error("只能上传音频文件！");
        return false;
      }
      return true;
    },
    // 欢迎页上传
    handleFirstUploadSuccess(response, file) {
      this.inChat = true;
      this.handleUploadSuccess(response, file);
    },
    // 对话页上传
    handleUploadSuccess(response, file) {
      this.messages.push({
        type: "user",
        content: `音频文件：${file.name}`
      });

      const taskId = (response && response.data) ? response.data : null;

      const loadingMsg = {
        type: "system",
        loading: true,
        content: "音频文件解析中",
        percent: 0,
        taskId: taskId, // 保存taskId
        fileName: file.name, // 添加文件名
        isParsedSuccessfully: false, // 初始化解析成功状态
        cancelled: false, // 初始化cancelled状态
        timerId: null, // 初始化timerId
        segments: [], // 初始化分段数据
        showSegments: false // 初始化显示模式
      };
      this.messages.push(loadingMsg);
      this.saveMessages();

      if (response && response.code === 200 && taskId) {
        setTimeout(() => {
          const currentLoadingMsg = this.messages.find(m => m.taskId === taskId && m.loading);
          if (currentLoadingMsg) {
             this.pollForResult(taskId, currentLoadingMsg);
          } else {
            console.warn("Could not find loading message in array for polling start.");
          }
        }, 1000);
      } else {
        loadingMsg.loading = false;
        const errorMsgBase = (response && response.msg) || "音频处理启动失败";
        loadingMsg.content = taskId ? `${errorMsgBase} (任务ID: ${taskId})` : errorMsgBase;
        loadingMsg.percent = 0;
        // isParsedSuccessfully remains false
        this.saveMessages();
        this.$message.error(loadingMsg.content); // 也通知用户
      }
    },
    cancelPolling(message) {
      if (message && message.loading) {
        message.cancelled = true;
        message.loading = false;
        message.content = "用户已取消解析";
        message.percent = 0;
        if (message.timerId) {
          clearTimeout(message.timerId);
          message.timerId = null;
        }
        message.isParsedSuccessfully = false; // 确保取消时不是成功状态
        this.saveMessages();
        // scrollToBottom will be called by updated hook
        this.$message.info('解析已取消');
      }
    },
    retryParsing(message) {
      if (message && message.taskId) {
        message.cancelled = false;
        message.loading = true;
        message.content = "音频文件解析中";
        message.percent = 0;
        message.isParsedSuccessfully = false; // 重试时重置成功状态
        if (message.timerId) {
            clearTimeout(message.timerId);
            message.timerId = null;
        }
        this.saveMessages();
        setTimeout(() => {
            this.pollForResult(message.taskId, message);
        }, 100);
      } else {
        const errorMsg = '无法重试：任务ID丢失';
        this.$message.error(errorMsg);
        message.content = '重试失败，任务信息不完整'; // taskId 不可用，所以不在这里加
        message.loading = false;
        message.isParsedSuccessfully = false;
        this.saveMessages();
      }
    },
    pollForResult(taskId, loadingMsg) {
      if (loadingMsg.cancelled) {
        console.log(`Polling cancelled for task ${taskId} before request.`);
        if(loadingMsg.loading){
            loadingMsg.loading = false;
            loadingMsg.isParsedSuccessfully = false;
            this.saveMessages();
        }
        return;
      }

      loadingMsg.loading = true;

      axios.get(`${this.getResultUrlBase}/${taskId}`)
        .then(response => {
          if (loadingMsg.cancelled) {
            console.log(`Polling cancelled for task ${taskId} after request returned.`);
            if(loadingMsg.loading){
                loadingMsg.loading = false;
                loadingMsg.isParsedSuccessfully = false;
                this.saveMessages();
            }
            return;
          }

          const resultResponse = response.data;
          if (resultResponse && resultResponse.code === 200 && resultResponse.data && resultResponse.data.proceed) {
            loadingMsg.loading = false;
            loadingMsg.content = resultResponse.data.text;
            // 使用 Vue.set 确保响应式更新
            this.$set(loadingMsg, 'segments', resultResponse.data.segments || []);
            this.$set(loadingMsg, 'showSegments', false);
            loadingMsg.percent = 100;
            loadingMsg.timerId = null;
            loadingMsg.isParsedSuccessfully = true; // 标记为成功解析
            this.saveMessages();
            // 确保Vue能检测到变化
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          } else if (resultResponse && resultResponse.code === 200 && resultResponse.data && !resultResponse.data.proceed) {
            if (resultResponse.data.percent !== undefined) {
              loadingMsg.percent = parseInt(resultResponse.data.percent, 10);
            }
            // isParsedSuccessfully remains false
            loadingMsg.timerId = setTimeout(() => {
              this.pollForResult(taskId, loadingMsg);
            }, this.pollingInterval);
          } else {
            loadingMsg.loading = false;
            const errorMsgBase = (resultResponse && resultResponse.msg) || "获取识别结果失败";
            loadingMsg.content = `${errorMsgBase} (任务ID: ${taskId})`;
            loadingMsg.percent = 0;
            loadingMsg.timerId = null;
            loadingMsg.isParsedSuccessfully = false;
            this.saveMessages();
            this.$message.error(loadingMsg.content);
          }
        })
        .catch(err => {
          if (loadingMsg.cancelled) {
            console.log(`Polling cancelled for task ${taskId} after error.`);
             if(loadingMsg.loading){
                loadingMsg.loading = false;
                loadingMsg.isParsedSuccessfully = false;
                this.saveMessages();
            }
            return;
          }
          console.error("Polling error:", err);
          loadingMsg.loading = false;
          loadingMsg.percent = 0;
          loadingMsg.timerId = null;
          loadingMsg.isParsedSuccessfully = false;
          let errorDetailBase = "获取识别结果时发生错误。";
          if (err.response) {
            errorDetailBase = `服务器错误 ${err.response.status}: ${ (err.response.data && err.response.data.msg) || err.response.statusText || '未知错误'}`;
          } else if (err.request) {
            errorDetailBase = "获取识别结果网络超时或无响应。";
          } else {
            errorDetailBase = `获取识别结果时发生请求错误: ${err.message}`;
          }
          loadingMsg.content = `${errorDetailBase} (任务ID: ${taskId})`;
          this.$message.error(loadingMsg.content);
          this.saveMessages();
        });
    },
    copyToClipboard(text) {
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text)
          .then(() => {
            this.$message.success('内容已复制到剪贴板！');
          })
          .catch(err => {
            console.error('无法复制文本: ', err);
            this.$message.error('复制失败，请手动复制。');
          });
      } else {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-9999px";
        textArea.style.top = "0"; // ensure it's not creating scroll
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          const successful = document.execCommand('copy');
          if (successful) {
            this.$message.success('内容已复制到剪贴板！');
          } else {
            this.$message.error('复制失败，请手动复制。');
          }
        } catch (err) {
          console.error('无法使用 execCommand 复制文本: ', err);
          this.$message.error('复制失败，请手动复制。');
        }
        document.body.removeChild(textArea);
      }
    },
    downloadAsText(content, fileName) {
      try {
        // 创建文本内容
        const textContent = content;

        // 创建 Blob 对象
        const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 生成文件名，去掉原文件扩展名并添加.txt
        const baseFileName = fileName ? fileName.replace(/\.[^/.]+$/, '') : '语音转文字结果';
        link.download = `${baseFileName}.txt`;

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('文件下载成功！');
      } catch (err) {
        console.error('下载文件时发生错误: ', err);
        this.$message.error('下载失败，请重试。');
      }
    },
    toggleViewMode(message) {
      if (message.segments && message.segments.length > 0) {
        // 使用 Vue.set 确保响应式更新
        this.$set(message, 'showSegments', !message.showSegments);
        this.saveMessages();
        this.$message.info(message.showSegments ? '已切换到分段对话模式' : '已切换到完整文本模式');
      }
    },
    // 自定义上传，便于 loading 控制
    customRequest(option) {
      this.uploading = true;
      const formData = new FormData();
      formData.append(option.filename, option.file);
      formData.append("silentThreshold", "95");
      formData.append("transcode", "true");
      formData.append("speakMergeThreshold", "0");

      axios.post(this.uploadUrl, formData)
        .then(response => {
          this.uploading = false;
          option.onSuccess && option.onSuccess(response.data, option.file);
        })
        .catch(err => {
          this.uploading = false;
          let errorMsg = "上传失败"; // customRequest 一般没有 taskId 可用，除非提前生成
          if (err.response && err.response.data && err.response.data.msg) {
              errorMsg = `上传失败: ${err.response.data.msg}`;
          } else if (err.response && err.response.statusText) {
              errorMsg = `上传失败: ${err.response.statusText}`;
          } else if (err.message) {
              errorMsg = `上传失败: ${err.message}`;
          }
          this.$message.error(errorMsg);
          option.onError && option.onError(err);
        });
    }
  }
};
</script>

<style scoped>
.audio-chat-container {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  margin: 20px 0;
  /* background-color: #f5f5f5; */
  /* border: 1px solid #f00; */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.welcome-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px 0 16px;
  margin-bottom: 200px;
}
.welcome-icon {
  width: 150px;
  height: 150px;
  margin-bottom: 18px;
}
.welcome-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.welcome-desc {
  color: #888;
  font-size: 15px;
  margin-bottom: 32px;
}

.chat-area {
  flex: 1;
  overflow-y: auto;
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
}

.message {
  display: flex;
  margin-bottom: 16px;
  align-items: center; /* 让气泡和删除图标垂直居中 */
}

.message.user {
  justify-content: flex-end;
}

.message.system {
  justify-content: flex-start;
}

.bubble {
  max-width: 70%;
  padding: 12px 18px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.6;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  word-break: break-all;
  background: #fff;
  color: #333;
  /* border-bottom-left-radius: 4px; */
}

.delete-icon {
  margin-left: 8px;
  margin-right: 8px; /* 为系统消息也添加右边距，如果它有删除按钮的话 */
  color: #909399;
  cursor: pointer;
  font-size: 16px;
}

.message.user .delete-icon {
  order: -1; /* 将用户消息的删除图标放到气泡左边 */
  margin-left: 0;
  margin-right: 8px;
}

.message.user .bubble {
  background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
  color: #fff;
  border-bottom-right-radius: 4px;
}

.message.system .bubble {
  background: #e9e8e8;
  color: #333;
  border-bottom-left-radius: 4px;
}

.upload-area {
  padding: 20px 16px;
  background-color: #fff;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center; /* 垂直居中按钮组 */
  justify-content: flex-end; /* 将按钮组推到右边 */
}

.button-group {
  display: flex;
  align-items: center;
}

.upload-btn {
  width: auto;
  display: flex;
  justify-content: center;
  /* flex-grow: 1; */ /* 移除 flex-grow，不再让它占据额外空间 */
  margin-right: 10px; /* 和清除按钮之间增加一些间距 */
}

.clear-btn {
  height: 44px; /* 与上传按钮高度一致 */
  font-size: 16px; /* 与上传按钮字体大小一致 */
  border-radius: 22px; /* 与上传按钮圆角一致 */
  letter-spacing: 1px;
  /* flex-shrink: 0; 不压缩此按钮 */
}

.long-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
  letter-spacing: 1px;
}

.loading-text {
  display: inline-block; /* 确保和按钮在同一行时对齐好 */
  margin-right: 5px;
}

.message .bubble .el-button--text { /* 微调"重新解析"按钮样式 */
    padding-top: 0;
    padding-bottom: 0;
    margin-left: 8px;
    line-height: normal;
}
/* 确保圆形关闭按钮有合适的空间，不会过于挤压 */
.message .bubble .el-button.is-circle[icon^="el-icon-"] {
    padding: 7px; /* Element UI mini circle button default is 7px */
}

/* New styles for title and action buttons */
.message-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-title {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  max-width: calc(100% - 70px); /* Adjust based on icon size and margins for two buttons */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.action-buttons {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.copy-icon-button,
.download-icon-button,
.switch-view-button {
  cursor: pointer;
  font-size: 18px;
  color: #409EFF;
  margin-left: 10px;
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.copy-icon-button:hover,
.download-icon-button:hover,
.switch-view-button:hover {
  color: #66b1ff;
}

.download-icon-button {
  color: #67C23A; /* 绿色，区别于复制按钮 */
}

.download-icon-button:hover {
  color: #85ce61;
}

.switch-view-button {
  color: #E6A23C; /* 橙色，区别于其他按钮 */
}

.switch-view-button:hover {
  color: #ebb563;
}

.switch-view-button.active {
  color: #F56C6C; /* 激活状态为红色 */
}

.switch-view-button.active:hover {
  color: #f78989;
}

.parsed-content-text {
  color: #555;
  line-height: 1.7;
  white-space: pre-wrap; /* Preserve line breaks from server */
}

/* 分段对话样式 */
.segments-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background-color: #e9e8e8;
}

.segment-message {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.segment-message:last-child {
  margin-bottom: 0;
}

.segment-message.left {
  justify-content: flex-start;
}

.segment-message.right {
  justify-content: flex-end;
}

.segment-bubble {
  max-width: 75%;
  padding: 10px 14px;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 左侧气泡样式 */
.segment-message.left .segment-bubble {
  border-bottom-left-radius: 4px;
  margin-right: auto;
}

/* 右侧气泡样式 */
.segment-message.right .segment-bubble {
  border-bottom-right-radius: 4px;
  margin-left: auto;
}

/* 默认样式：左侧偶数speaker，右侧奇数speaker */
.segment-message.left .segment-bubble {
  border-left: 3px solid #606266;
}

.segment-message.right .segment-bubble {
  border-right: 3px solid #606266;
}

/* 根据 speaker 设置边框颜色和标签颜色 - 使用更高优先级的选择器 */
.segment-message.left .segment-bubble[data-speaker="0"] { border-left: 3px solid #409EFF !important; } /* 蓝色 */
.segment-bubble[data-speaker="0"] .speaker-label { color: #409EFF; }

.segment-message.right .segment-bubble[data-speaker="1"] { border-right: 3px solid #67C23A !important; } /* 绿色 */
.segment-bubble[data-speaker="1"] .speaker-label { color: #67C23A; }

.segment-message.left .segment-bubble[data-speaker="2"] { border-left: 3px solid #E6A23C !important; } /* 橙色 */
.segment-bubble[data-speaker="2"] .speaker-label { color: #E6A23C; }

.segment-message.right .segment-bubble[data-speaker="3"] { border-right: 3px solid #F56C6C !important; } /* 红色 */
.segment-bubble[data-speaker="3"] .speaker-label { color: #F56C6C; }

.segment-message.left .segment-bubble[data-speaker="4"] { border-left: 3px solid #909399 !important; } /* 灰色 */
.segment-bubble[data-speaker="4"] .speaker-label { color: #909399; }

.segment-message.right .segment-bubble[data-speaker="5"] { border-right: 3px solid #C71585 !important; } /* 深粉色 */
.segment-bubble[data-speaker="5"] .speaker-label { color: #C71585; }

.segment-message.left .segment-bubble[data-speaker="6"] { border-left: 3px solid #20B2AA !important; } /* 浅海绿 */
.segment-bubble[data-speaker="6"] .speaker-label { color: #20B2AA; }

.segment-message.right .segment-bubble[data-speaker="7"] { border-right: 3px solid #FF8C00 !important; } /* 深橙色 */
.segment-bubble[data-speaker="7"] .speaker-label { color: #FF8C00; }

.segment-message.left .segment-bubble[data-speaker="8"] { border-left: 3px solid #8A2BE2 !important; } /* 蓝紫色 */
.segment-bubble[data-speaker="8"] .speaker-label { color: #8A2BE2; }

.segment-message.right .segment-bubble[data-speaker="9"] { border-right: 3px solid #DC143C !important; } /* 深红色 */
.segment-bubble[data-speaker="9"] .speaker-label { color: #DC143C; }

.speaker-label {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 6px;
  opacity: 0.8;
  color: #606266; /* 默认颜色 */
}

.segment-text {
  color: #333;
  line-height: 1.5;
  font-size: 14px;
  word-break: break-word;
}
</style>
