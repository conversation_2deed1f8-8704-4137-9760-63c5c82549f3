<template>
  <el-container>
    <el-header>
      <div class="help-header-wrapper">
        <div class="help-header-info">
          <img
            :src="require('@/assets/logo.png')"
            style="height: 40px;"
            alt="管理系统"
            class="help-header-info-img"
          />
          <div class="help-header-info-line"></div>
          <div class="help-header-info-title">帮助中心</div>
          <div style="margin-left: 30px">
            <el-link :href="homeUrl" target="_self"
              ><i class="el-icon-s-home" />回到首页</el-link
            >
          </div>
        </div>
      </div>
    </el-header>
    <el-main>
      <div class="nav-wrapper">
        <div class="nav-content middle-content">
          <el-menu
            :default-active="$route.path"
            mode="horizontal"
            @select="changeSelect"
          >
            <el-menu-item index="/help">帮助首页</el-menu-item>
            <el-menu-item index="/problems/list">问题汇总</el-menu-item>
          </el-menu>
        </div>
      </div>
      <router-view></router-view>
    </el-main>
    <el-footer>
      <div class="help-footer-wrapper">
        <div class="footer">
          <div class="footer-content">
            <div class="foot"></div>
            <dl></dl>
            <dl>
              <dt>快捷连接</dt>
              <dd>
                <a
                  class="footer-href"
                  :href="homeUrl"
                  target="_blank"
                  rel="noopener noreferrer"
                  >台州公益诉讼指挥软件后台</a
                >
              </dd>
            </dl>
            <dl>
              <dt>技术支持</dt>
              <dd>
                <a
                  class="footer-href"
                  href="javascrpit:void(0)"
                  rel="noopener noreferrer"
                  >浙江建达科技股份有限公司</a
                >
              </dd>
            </dl>
            <dl>
              <dt>客服支持</dt>
              <dd>
                <a
                  class="footer-href"
                  href="javascrpit:void(0)"
                  rel="noopener noreferrer"
                  >xxx</a
                >
              </dd>
            </dl>
          </div>
          <div class="copyright">
            <p class="copyright-info" style="margin: auto">
              Copyright © 2022-{{
                new Date().getFullYear()
              }}
              台州公益诉讼指挥软件 All Rights Reserved.
            </p>
          </div>
        </div>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
// @ is an alias to /src

export default {
  name: "Home",
  components: {},
  data() {
    return {
      activeName: "first",
      homeUrl: process.env.VUE_APP_HOME_URL
    };
  },
  methods: {
    changeSelect(path) {
      this.$router.push({ path: path });
    }
  }
};
</script>

<style scoped>
.help-header-wrapper,
.help-header-wrapper .help-header-info {
  display: flex;
  align-items: center;
}

.help-header-wrapper {
  justify-content: space-between;
  margin: 0 auto;
  width: 1200px;
  height: 80px;
  box-sizing: border-box;
}

img {
  vertical-align: middle;
  border-style: none;
}

.help-header-wrapper .help-header-info .help-header-info-line {
  position: relative;
  height: 20px;
  width: 1px;
  left: 1px;
  top: 3px;
  background: #eee;
  margin-left: 10px;
  margin-right: 10px;
}

.help-header-wrapper .help-header-info .help-header-info-title {
  height: 27px;
  font-size: 18px;
  line-height: 27px;
}

.help-footer-wrapper {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  min-height: 300px;
  margin-top: 60px;
  background: #161e30;
  z-index: 11;
}

.help-footer-wrapper .footer {
  max-width: 1380px;
  margin: 0 auto;
  padding: 0 50px;
}

.help-footer-wrapper .footer .footer-content {
  padding-bottom: 20px;
  display: flex;
  justify-content: space-between;
  padding-top: 80px;
}

.help-footer-wrapper .footer .foot .tel {
  font-size: 30px;
  color: #fff;
  margin-bottom: 5px;
}

.help-footer-wrapper .footer .foot .time {
  font-size: 12px;
  color: #fff;
  opacity: 0.6;
  margin-bottom: 24px;
}

.help-footer-wrapper .footer .foot .qr {
  display: flex;
  text-align: center;
}

.help-footer-wrapper .footer .foot .qr .gzh {
  margin-right: 23px;
}

.help-footer-wrapper .footer .foot .qr p {
  font-size: 12px;
  color: #fff;
  opacity: 0.6;
  line-height: 30px;
}

dd,
dl,
dt,
p,
ul {
  margin: 0;
  padding: 0;
}

dl {
  text-align: left;
}

.help-footer-wrapper .footer .footer-content dt {
  color: #fff;
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 30px;
}

.help-footer-wrapper .footer .footer-content dd {
  line-height: 40px;
  font-size: 14px;
}

.help-footer-wrapper .footer .footer-content dd a {
  color: #fff;
  opacity: 0.6;
}

a {
  text-decoration: none !important;
  cursor: pointer !important;
  color: #2a75ed;
}

.help-footer-wrapper .footer .footer-content .media-report,
.help-footer-wrapper .footer .footer-content .media-report a {
  max-width: 300px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.help-footer-wrapper .footer .footer-content dt {
  color: #fff;
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 30px;
}

.help-footer-wrapper .footer .footer-content dd {
  line-height: 40px;
  font-size: 14px;
}

.help-footer-wrapper .footer .footer-content .media-report a {
  display: block;
}

.help-footer-wrapper .footer .footer-content .media-report,
.help-footer-wrapper .footer .footer-content .media-report a {
  max-width: 300px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.help-footer-wrapper .footer .footer-content dd a {
  color: #fff;
  opacity: 0.6;
}

a {
  text-decoration: none !important;
  cursor: pointer !important;
  color: #2a75ed;
}

.help-footer-wrapper .footer .footer-content dd {
  line-height: 40px;
  font-size: 14px;
}

.help-footer-wrapper .footer .bottom {
  padding: 5px 0;
  display: flex;
}

.help-footer-wrapper .footer .bottom .contact-way {
  line-height: 29px;
  margin-right: 32px;
}

.help-footer-wrapper .footer .bottom .contact-way li {
  font-size: 14px;
  color: #fff;
  opacity: 0.6;
}

li,
ul {
  list-style: none;
}

.help-footer-wrapper .footer .copyright {
  height: 35px;
  line-height: 35px;
  font-size: 12px;
  color: #fff;
  opacity: 0.3;
  display: flex;
  justify-content: space-between;
}

.nav-wrapper {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #f6f6f6;
}

.nav-wrapper .nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.middle-content {
  margin: 0 auto;
  width: 1180px;
}

.main-page .content .problem-item .left div p:nth-child(2) {
  width: 144px;
  padding-top: 10px;
  font-size: 12px;
  color: #888;
}
</style>
<style>
.el-footer {
  padding: 0 !important;
}

.el-main {
  padding: 20px 0px !important;
}

.el-menu.el-menu--horizontal {
  border-bottom: none !important;
}

.el-menu--horizontal > .el-menu-item {
  height: 50px !important;
  line-height: 50px !important;
  background-color: #f6f6f6 !important;
}
</style>
