<template>
  <div>
    <div class="help-center-content middle-content" style="min-height: 15px;">
      <div class="ant-spin-nested-loading">
        <div class="ant-spin-container">
          <div class="main-page">
            <div>
              <div class="classify-title">
                常见问题
                <a href="#/problems/list">更多</a>
              </div>
              <div class="content">
                <a class="problem-item" href="#/problems/content/1-1-1">
                  <div class="left">
                    <span>
                    </span>
                    <div>
                      <p>线索管理</p>
                      <p></p>
                    </div>
                  </div>
                  <img :src="require('@/assets/icon_arrow.png')" alt="">
                </a>

                <a class="problem-item" href="#/problems/content/2-2-1">
                  <div class="left">
                    <span>
                    </span>
                    <div><p>监督管理</p>
                      <p></p></div>
                  </div>
                  <img :src="require('@/assets/icon_arrow.png')" alt=""></a>


                <a class="problem-item" href="#/problems/content/3-1-1">
                  <div class="left">
                    <span>
<!--                        <People theme="multi-color" size="30"-->
<!--                                :fill="['#333' ,'#2F88FF' ,'#FFF' ,'#43CCF8']"/>-->
                    </span>
                    <div><p>专项模型</p>
                      <p></p></div>
                  </div>
                  <img :src="require('@/assets/icon_arrow.png')" alt="">
                </a>

                <a class="problem-item" href="#/problems/content/4-1-1">
                  <div class="left">
                    <span>
                    </span>
                    <div><p>数据管理</p>
                      <p></p></div>
                  </div>
                  <img :src="require('@/assets/icon_arrow.png')" alt="">
                </a>
                <a class="problem-item" href="#/problems/content/5-1-1">
                  <div class="left">
                    <span>
<!--                        <People theme="multi-color" size="30"-->
<!--                                :fill="['#333' ,'#2F88FF' ,'#FFF' ,'#43CCF8']"/>-->
                    </span>
                    <div><p>权重管理</p>
                      <p></p></div>
                  </div>
                  <img :src="require('@/assets/icon_arrow.png')" alt="">
                </a>
                <a class="problem-item" href="#/problems/content/6-1-1">
                  <div class="left">
                    <span>
                    </span>
                    <div><p>系统管理</p>
                      <p></p></div>
                  </div>
                  <img :src="require('@/assets/icon_arrow.png')" alt="">
                </a>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

  export default {
    name: "index",
    components: {
    }

  }
</script>

<style scoped>

  .middle-content {
    margin: 0 auto;
    width: 1180px;
  }

  .ant-spin-nested-loading {
    position: relative;
  }

  .ant-spin-container {
    position: relative;
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
  }

  .main-page {
    min-height: 100px;
  }

  .main-page .classify-title {
    position: relative;
    padding: 40px 0 20px;
    font-size: 24px;
    color: #353535;
    text-align: center;
  }

  .main-page .classify-title a {
    position: absolute;
    font-size: 16px;
    color: #105cfb;
    right: 0;
  }

  a {
    text-decoration: none !important;
    cursor: pointer !important;
    color: #2a75ed;
  }

  .main-page .content {
    display: flex;
    flex-wrap: wrap;
  }

  .main-page .content .problem-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 320px;
    height: 160px;
    padding: 0 20px 0 40px;
    background: #f6f6f6;
    margin: 0 20px 20px 0;
  }

  .main-page .content .problem-item, .main-page .content .special-topic {
    transition: all .2s linear;
  }

  .main-page .content .problem-item .left {
    display: flex;
  }

  img {
    vertical-align: middle;
    border-style: none;
  }

  .main-page .content .problem-item .left div {
    padding-left: 20px;
  }

  .main-page .content .problem-item .left div p:first-child {
    font-size: 18px;
    color: #333;
  }

  .main-page .content .problem-item .left div p:nth-child(2) {
    width: 144px;
    padding-top: 10px;
    font-size: 12px;
    color: #888;
  }

  .main-page .content .problem-item:nth-child(3n) {
    margin-right: 0;
  }

  .main-page .content .problem-item .left img {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    overflow: hidden;
  }

  .main-page .content .problem-item:hover, .main-page .content .special-topic:hover {
    box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%), 0 1px 5px 0 rgb(0 0 0 / 12%), 0 3px 1px -2px rgb(0 0 0 / 20%);
  }

  dd, dl, dt, li, p, ul {
    text-align: left;
    margin: 0;
    padding: 0;
  }
</style>
