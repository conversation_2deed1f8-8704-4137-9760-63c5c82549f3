import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css';
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css

import '@/styles/style.scss'   //全局样式
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import App from './App'
import store from './store'
import router from './router'
import permission from './directive/permission'
import less from 'less'

import '@/iconfont/iconfont.css' // icon
import './permission' // permission control
import './utils/error-log' // error log
import { parseTime, resetForm, addDateRange, selectDictLabel, download, handleTree, openLoadingCustomColor, openLoading } from "@/utils/core"
import * as filters from './filters' // global filters
import { Message } from 'element-ui';
import { Request } from './libs/request'
// import AmapVue from '@amap/amap-vue'
import { preventReClick } from '@/utils/preventReClick' //防止重复点击
import md5 from 'js-md5';
import JSEncrypt from 'jsencrypt';
import moment from 'moment'
// 引入水印文件地址
import watermark from '@/utils/watermark.js'

import SvgIcon from '@/components/SvgIcon'// svg component

// register globally
Vue.component('svg-icon', SvgIcon)

Vue.filter('datefmt', function (input, fmtstring) {
  return moment.unix(input).format(fmtstring)
})

Vue.prototype.$getRsaCode = function (str) { // 注册方法
  let pubKey = `MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJlru6U8sD2FgKOK4nX/Ip8L9a+YXjZPdMBXczhHWsniOWl17IgoXw19R/k6T+PlMMMEIuO2/v8Ty0HFYOQTASkCAwEAAQ==`;// rsa 公钥
  let encryptStr = new JSEncrypt();
  encryptStr.setPublicKey(pubKey); // 设置 加密公钥
  let data = encryptStr.encrypt(str.toString());  // 进行加密
  return data;
}

//高德
// AmapVue.config.version = '2.0'; // 默认2.0，这里可以不修改
// AmapVue.config.key = 'de330aef8252d50d9ca1e2db041bbf2d';
// AmapVue.config.plugins = [
//   'AMap.ToolBar',
//   'AMap.MoveAnimation',
//   // 在此配置你需要预加载的插件，如果不配置，在使用到的时候会自动异步加载
// ];
import VueScaleResize from 'vue-scale-resize'
Vue.use(VueScaleResize)


import eIconPicker from 'e-icon-picker';
import "e-icon-picker/lib/symbol.js"; //基本彩色图标库
import 'e-icon-picker/lib/index.css'; // 基本样式，包含基本图标
import 'font-awesome/css/font-awesome.min.css'; //font-awesome 图标库
import 'element-ui/lib/theme-chalk/icon.css'; //element-ui 图标库

Vue.use(eIconPicker, { FontAwesome: true, ElementUI: true, eIcon: true, eIconSymbol: true });


import AButton from '@/components/aComponents/aButton/index.vue'
import AInput from '@/components/aComponents/aInput/index.vue'
import APagination from '@/components/aComponents/aPagination'

Vue.component('AButton', AButton)
Vue.component('AInput', AInput)
Vue.component('APagination', APagination)


// Vue.use(VueAliplayerV2);
// Vue.use(AmapVue);
Vue.use(permission)
Vue.use(less)
// Vue.use(ViewUI)
Vue.prototype.Message = Message
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.openLoadingCustomColor = openLoadingCustomColor
Vue.prototype.openLoading = openLoading
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.fileHostURL = process.env.VUE_APP_FILE_HOST_URL
Vue.prototype.fileUploadURL = process.env.VUE_APP_FILE_UPLOAD_URL
Vue.prototype.filePreview = process.env.VUE_APP_FILE_PREVIEW
Vue.prototype.problemH5 = process.env.VUE_APP_H5_PROBLEM
Vue.prototype.treeInfoH5 = process.env.VUE_APP_H5_TREEINFO
Vue.prototype.ruinsInfoH5 = process.env.VUE_APP_H5_RUINSINFO
Vue.prototype.activitiConfigURL = process.env.VUE_APP_ACTIVITI_CONFIG
Vue.prototype.activitiBaseURL = process.env.VUE_APP_ACTIVITI_BASE
Vue.prototype.md5 = md5;


// 区分正式版/测试版本
Vue.prototype.ENV = process.env.VUE_APP_ENV


Vue.prototype.isRedTheme = true
Vue.prototype.$watermark = watermark
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */
// import { mockXHR } from '../mock'
// if (process.env.NODE_ENV === 'production') {
//   mockXHR()
// }

Vue.prototype.Request = Request

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

// import VueLazyLoad from 'vue-lazyload'
// Vue.use(VueLazyLoad)

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

import OverlayScrollbars from 'overlayscrollbars'

Vue.directive("scrollBar", {
  inserted(el, binding, vnode) {
    const { callbacks = {} } = binding.value || {};

    // os-host-flexbox
    // ['flex','inline-flex'].includes(window.getComputedStyle(el, null).display)

    // const nodes = Array.from(el.childNodes)
    // const div = document.createElement('div');
    // div.append(...nodes)
    // div.style.height="100%"
    // el.appendChild(div);

    el.ins = OverlayScrollbars(el, {
      autoUpdate: true,
      nativeScrollbarsOverlaid: {
        // initialize: false
      },
      overflowBehavior: {
        x: 'hidden'
      },
      scrollbars: {
        autoHide: "leave",
        autoHideDelay: 100
      },
      callbacks
    })
  },
  // update(el) {
  //   return el.ins.update()
  // },
  componentUpdated(el, binding, vnode, oldVnode) {
    return el.ins.update()

  },
  unbind(el) {
    el.ins.destroy()
  }
})


new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

import './styles/tailwind.css'
