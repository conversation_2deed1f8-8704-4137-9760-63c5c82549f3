import cookies from 'js-cookie'
import { Config } from '../../../config/config.js'
import qs from 'qs'
import { WebStorage } from 'vue-ls/src/storage/WebStorage.js'

const util = {
  cookies: cookies,
  qs: qs,
  localStorage: new WebStorage(localStorage),
  sessionStorage: new WebStorage(sessionStorage)
}

// 窗口关闭、刷新时触发,往unloadEvents数组添加待执行的function即可
util.unloadEvents = []
window.onbeforeunload = function(e) {
  util.unloadEvents.forEach(item => {
    if (util.isFunction(item)) item(e)
  })
}

// 设置网页标题
util.title = function(title) {
  const cPre = Config.webTitlePrefix
  const cSuf = Config.webTitleSuffix
  const pre = cPre[0] && title ? cPre.join('') : cPre[0]
  const suf = cSuf[0] && title ? [...cSuf].reverse().join('') : cSuf[0]
  window.document.title = pre + title + suf
}

// 获取浏览器当前地址域名，包含“http://”或“https://”
util.getHost = function() {
  const host = window.location.host
  const href = window.location.href
  const regex = new RegExp('.*' + host)
  return href.match(regex)[0]
}

// 获取浏览器当前地址除域名外的部分，包含“?”和“#”
util.getUri = function() {
  const host = window.location.host
  const href = window.location.href
  const regex = new RegExp('.*' + host + '(.*)')
  return href.match(regex)[1]
}

// 获取原字符串中第start个tag至第end个tag间的子串
// start，end为负时从后面数起，start不填默认为第0个即字符串开头起，end不填默认为倒数第0个即字符串末尾止
util.subStrByTag = function(str, tag, start = 0, end) {
  return str.split(tag).slice(start, end).join(tag)
}

// UUID生成
util.getUUID = function() {
  let d = new Date().getTime()
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (d + Math.random() * 16) % 16 | 0
    d = Math.floor(d / 16)
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
}

// 判断是否为number
util.isNumber = function(obj) {
  return Object.prototype.toString.call(obj).toLowerCase() === '[object number]'
}

// 判断是否为string
util.isString = function(obj) {
  return Object.prototype.toString.call(obj).toLowerCase() === '[object string]'
}

// 判断是否为array
util.isArray = function(obj) {
  return Object.prototype.toString.call(obj).toLowerCase() === '[object array]'
}

// 判断是否为json对象
util.isJSON = function(obj) {
  return typeof (obj) === "object" && Object.prototype.toString.call(obj).toLowerCase() === "[object object]" && !obj.length
}

// 判断是否为function
util.isFunction = function(obj) {
  return Object.prototype.toString.call(obj).toLowerCase() === '[object function]'
}

// 判断是否为date
util.isDate = function(obj) {
  return Object.prototype.toString.call(obj).toLowerCase() === '[object date]'
}

// 日期格式化
util.dateFormat = function(date, fmt) {
  date = util.isDate(date) ? date : new Date(date)
  const o = {
    'M+': date.getMonth() + 1, 'd+': date.getDate(), 'h+': date.getHours(), 'm+': date.getMinutes(),
    's+': date.getSeconds(), 'q+': Math.floor((date.getMonth() + 3) / 3), 'S': date.getMilliseconds()
  }
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) { fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length))) }
  }
  return fmt
}

export { util as Util }
