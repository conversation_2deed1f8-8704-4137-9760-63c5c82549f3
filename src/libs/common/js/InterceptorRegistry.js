import pathToRegexp from 'path-to-regexp/index'

/** 默认拦截器(定义其他拦截器时请继承该类) */
class Interceptor {
  /**
   * 前置拦截处理
   * @param resolve {Function} 调用resolve()表明允许通过
   * @param reject {Function} 调用reject(error)表明不允许通过,error会传递给结束处理类的error方法中
   * @param object {Object}
   */
  preHandle(resolve, reject, object) {
    resolve()
  }
}

/** 默认结束处理(定义其他结束处理时请继承该类) */
class InterceptorRegistryFinish {
  /**
   * 允许通过后做些什么
   * @param object {Object} 注册器注入的额外的信息
   */
  success(object) {
  }

  /**
   * 不允许通过后做些什么
   * @param error {!Function} 错误信息
   * @param object {Object}  注册器注入的额外的信息
   */
  error(error, object) {
    console.error(error)
  }

  /**
   * 无论通过与否都要执行的方法
   * @param object {Object} 注册器注入的额外的信息
   */
  finally(object) {
  }
}

/** 拦截注册器 */
const DONING = Symbol('注册器状态:准备中'); const DONE = Symbol('注册器状态:完成')
const STATE = Symbol('私有变量名:注册器状态')
const PATH = Symbol('私有变量名:string/*当前路径*/')
const OBJECT = Symbol('私有变量名:object/*请求携带的参数*/')
const RULES = Symbol('私有变量名:{interceptor:class/*拦截器*/, include:[p1, ...]/*拦截路径*/, exclude:[p1, ...]/*排除路径*/}')
const GEXCLUDE = Symbol('私有变量名:[path1, path2, ...]/*全局排除路径*/')
const PATHFILTER = Symbol('私有变量名:f(path, include, exclude)/*判断路径是否需要被拦截*/')

class InterceptorRegistry {
  /**
   * 构造函数
   * @param path {string} 当前路径
   * @param object {Object} 注册器注入给拦截器的额外的信息
   */
  constructor(path, object) {
    this[STATE] = DONING
    this[PATH] = path
    this[OBJECT] = object
    this[RULES] = []
    this[GEXCLUDE] = []
  }

  /**
   * 设置全局不拦截的路径
   * @param paths {string} path1,path2...pathN
   * @returns {InterceptorRegistry}
   */
  globalExclude(...paths) {
    const gExclude = this[GEXCLUDE]
    gExclude.push(...paths)
    return this
  }

  /**
   * 新增拦截器
   * @param interceptor {Interceptor} 拦截器
   * @returns {InterceptorRegistry}
   */
  add(interceptor) {
    const rules = this[RULES]
    rules[rules.length] = { interceptor: interceptor, include: [], exclude: [] }
    return this
  }

  /**
   * 设置当前拦截器要拦截的路径集合
   * @param paths {string} path1,path2...pathN
   * @returns {InterceptorRegistry}
   */
  include(...paths) {
    const rules = this[RULES]
    if (rules[rules.length - 1]) {
      rules[rules.length - 1].include.push(...paths)
    }
    return this
  }

  /**
   * 设置当前拦截器不拦截的路径集合
   * @param paths {string} path1,path2...pathN
   * @returns {InterceptorRegistry}
   */
  exclude(...paths) {
    const rules = this[RULES]
    if (rules[rules.length - 1]) {
      rules[rules.length - 1].exclude.push(...paths)
    }
    return this
  }

  /**
   * 设置结束处理,并开始运行拦截器
   * @param finish {InterceptorRegistryFinish} 结束处理类
   */
  finish(finish = new InterceptorRegistryFinish()) {
    if (this[STATE] === DONE) return
    const t = this; const path = t[PATH]; const object = t[OBJECT]; const gExclude = t[GEXCLUDE]; const rules = t[RULES]
    const pathFilter = t[PATHFILTER]; let p = Promise.resolve()
    if (pathFilter(path, ['(.*)'], gExclude)) {
      rules.forEach((item) => {
        if (pathFilter(path, item.include, item.exclude)) {
          p = p.then(() => {
            return new Promise(function(resolve, reject) {
              item.interceptor.preHandle(resolve, reject, object)
            })
          })
        }
      })
    }
    p.then(() => {
      finish.success(object)
    }).catch(error => {
      finish.error(error, object)
    }).finally(() => {
      finish.finally(object)
    })
  }

  /**
   * 判断当前path是否需要被拦截
   * @param path {string} 需要判断的路径
   * @param include {Array} 要拦截的路径数组
   * @param exclude {Array} 不拦截的路径数组
   * @returns {boolean}
   */
  [PATHFILTER](path, include, exclude) {
    let isInclude = false; let isExclude = false
    if (include.length === 0) include[0] = '(.*)'
    for (let i = 0; i < include.length; i++) {
      if (pathToRegexp(include[i]).test(path)) {
        isInclude = true
        break
      }
    }
    for (let i = 0; i < exclude.length; i++) {
      if (pathToRegexp(exclude[i]).test(path)) {
        isExclude = true
        break
      }
    }
    return isInclude && !isExclude
  }
}

export { Interceptor, InterceptorRegistryFinish, InterceptorRegistry }
