import axios from 'axios/index'
import store from '@/store'
import { getToken } from '@/utils/auth'

/** RequestFactory请求工厂类 */
const m = ['get', 'post', 'delete', 'put']

class RequestFactory {
  /**
   * 生产请求对象方法
   * @param config 该请求对象的全局配置
   * @param methods {Array} 需要自动创建的method快捷方式
   * @param prefixUrls {{}} 需要自动创建的url前缀快捷方式
   * @returns function(JSON): Promise<any | any> 请求对象
   */
  static getRequest({ config = {}, methods = m, prefixUrls = {}} = {
    config: {},
    methods: m,
    prefixUrls: {}
  }) {
    const timeout = 30000; const source = axios.CancelToken.source()
    if (!config.timeout) config.timeout = timeout
    if (!config.cancelToken) config.cancelToken = source.token
    let preHandle = function(config) {
      return Promise.resolve(config)
    }
    const afterHandle = {
      success: function(res) {
        return Promise.resolve(res)
      },
      error: function(error) {
        return Promise.reject(error)
      }
    }

    /**
     * 工厂生产出来的请求对象
     * @param config {JSON}
     * @returns {Promise<any>}
     */
    function request(config) {
      if (config.url.match(/{[^/]+?}/g)) {
        config.url.match(/{[^/]+?}/g).forEach(item => {
          if (config.uriVariables[item.replace(/[{}]/g, '')]) {
            config.url = config.url.replace(item, config.uriVariables[item.replace(/[{}]/g, '')])
          } else throw new Error('Not enough variable values available to expand ' + item.replace(/[{}]/g, '\''))
        })
      }
      delete config.uriVariables
      return preHandle(config).then(res => {
        return request.instance(res || config)
      }).then(res => {
        return afterHandle.success(res)
      }).catch(error => {
        return afterHandle.error(error)
      })
    }

    /** axios实例,可根据官方文档配置,https://www.npmjs.com/package/axios */
    request.instance = axios.create(config)

    // request拦截器
    request.instance.interceptors.request.use(
      config => {
        // do something before request is sent
        if (store.getters.token) {
          // 让每个请求携带token为自定义key 请根据实际情况自行修改
          config.headers['_ut'] = getToken()
        }
        return config
      },
      error => {
        // do something with request error
        console.log(error) // for debug
        return Promise.reject(error)
      }
    )
    /**
     * 取消该实例发出的所有请求,视为请求响应失败
     * @param message
     */
    request.cancel = function(message) {
      source.cancel(message)
    }
    /**
     * 请求的前置拦截,参数形如:function (next, config) {}(此拦截先于instance的前置拦截)
     * @param func {Function}
     */
    request.beforeEach = function(func) {
      preHandle = function(config) {
        return new Promise(function(next, reject) {
          setTimeout(reject, timeout, '请求发起超时')
          func(next, config)
        }).then(res => {
          return Promise.resolve(res || config)
        })
      }
    }
    request.afterEach = {
      /**
       * 请求响应成功结果拦截,参数形如:function (next, res) {}(此拦截后于instance的后置拦截)
       * @param func {Function}
       */
      success: function(func) {
        afterHandle.success = function(response) {
          return new Promise(function(next) {
            func(next, response)
          }).then(res => {
            return Promise.resolve(res || response)
          })
        }
      },
      /**
       * 请求响应失败结果拦截,参数形如:function (next, error) {}(此拦截后于instance的后置拦截)
       * @param func {Function}
       */
      error: function(func) {
        afterHandle.error = function(error) {
          return new Promise(function(next) {
            func(next, error)
          }).then(res => {
            return Promise.reject(res || error)
          })
        }
      }
    }
    // 添加method快捷请求方法
    methods.forEach(item => {
      request[item] = function(config) {
        config.method = item
        return request(config)
      }
    })
    // 添加prefixUrls快捷请求方法
    Object.entries(prefixUrls).forEach(([key, val]) => {
      request[key] = function(config) {
        config.url = val + config.url
        return request(config)
      }
      methods.forEach(item => {
        request[key][item] = function(config) {
          config.url = val + config.url
          config.method = item
          return request(config)
        }
      })
    })
    return request
  }
}

export { RequestFactory }
// function pro(config) {
//     return new Promise((resolve, reject) => {
//         config.success = res => resolve(res);
//         config.fail = error => reject(error);
//         wx.request(config)
//     });
// }
