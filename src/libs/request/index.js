import { Config } from '../../config/config'
import { RequestFactory } from '../common/js/RequestFactory.js'
import { InterceptorRegistry } from '../common/js/InterceptorRegistry.js'
import { RequestFinish } from '../interceptors/request'

/** 请求对象,配置拦截处理 */
const request = RequestFactory.getRequest({
  config: { baseURL: 'http://*************:9001/ipcp-portal' },
  prefixUrls: Config.request.prefixUrls
})

// 请求发起前拦截
request.beforeEach((next, config) => {
  const registry = new InterceptorRegistry(config.url, { next, config })
  registry.finish(new RequestFinish())
})
// 请求响应成功结果拦截
request.afterEach.success((next, res) => {
  next()
})
// 请求响应失败结果拦截
request.afterEach.error((next, error) => {
  next()
})

export { request as Request }
