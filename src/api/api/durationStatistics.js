/*
 * @Description:
 */
import request from '@/utils/request'
const baseURL = API.instanceAPI


// 获取部门和员额
export function getDepartmentAndUser() {
    return request({
        baseURL,
        headers: {
            'sign': 'zmkm'
        },
        url: '/api/case-duration/groups',
        method: 'GET',
    })
}

//获取统计数据
export function getStatisticsData(data) {
    return request({
        baseURL,
        headers: {
            'sign': 'zmkm'
        },
        url: '/api/case-duration/statistics/data',
        method: 'POST',
        data: data
    })
}

// 获取表头标题
export function getTableHeader() {
    return request({
        baseURL,
        headers: {
            'sign': 'zmkm'
        },
        url: '/api/case-duration/titles',
        method: 'GET',
    })
}
// 得到详情数据
export function getDetailTableData(code,data) {
    return request({
        baseURL,
        headers: {
            'sign': 'zmkm'
        },
        url:`/api/case-duration/detail/${code}`,
        method: 'POST',
        data: data
    })
}
// 得到详情数据
export function getDetailTableLeader(data) {
    return request({
        baseURL,
        headers: {
            'sign': 'zmkm'
        },
        url:`/api/case-duration/leader`,
        method: 'POST',
        data: data
    })
}
