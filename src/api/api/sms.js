import request from '@/utils/request'

// 获取部门和员额
export function smsPageList(param) {
  return request({
    url: '/rest/sms/case/page',
    method: 'POST',
    data: param
  })
}
// 获取部门和员额
export function smsInfoPageList(param) {
  return request({
    url: '/rest/sms/listByPage',
    method: 'POST',
    data: param
  })
}
// 获取部门和员额
export function autoSendEnable(param) {
  return request({
    url: '/rest/sms/autoSend/enable',
    method: 'POST',
    data: param
  })
}

// 获取部门和员额
export function smsSave(param) {
  return request({
    url: '/rest/sms/save',
    method: 'POST',
    data: param
  })
}
