import request from '@/utils/request'

// 获取部门和员额
export function digitalModelPageList(param) {
  return request({
    url: '/rest/digital/model/listByPage',
    method: 'POST',
    data: param
  })
}


// 获取部门和员额
export function digitalModelDelete(param) {
  return request({
    url: '/rest/digital/model/delete',
    method: 'POST',
    data: param
  })
}


// 获取部门和员额
export function digitalModelDeleteFiles(param) {
  return request({
    url: '/rest/digital/model/delete/files',
    method: 'POST',
    data: param
  })
}


// 获取部门和员额
export function digitalModelSaveFiles(param) {
  return request({
    url: '/rest/digital/model/save/files',
    method: 'POST',
    data: param
  })
}
