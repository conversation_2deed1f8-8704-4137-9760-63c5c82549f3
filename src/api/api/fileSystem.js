import request from '@/utils/request'

// 字典保存操作
export function saveFileSource(data) {
  return request({
    url: '/rest/system/file/save',
    method: 'post',
    data: data
  })
}


// 获取部门和员额
export function fileSystemPageList(param) {
  return request({
    url: '/rest/system/file/listByPage',
    method: 'POST',
    data: param
  })
}


// 获取部门和员额
export function fileSystemDelete(param) {
  return request({
    url: '/rest/system/file/delete',
    method: 'POST',
    data: param
  })
}


// 解析
export function fileSystemAnalyze(param) {
  return request({
    url: '/rest/system/file/analyze',
    method: 'POST',
    data: param
  })
}
// 解析
export function fileSystemAnalyzeBycategory(param) {
  return request({
    url: '/rest/system/file/analyzeBycategory',
    method: 'POST',
    data: param
  })
}


// 解析
export function fileSystemAnalyzeData(param) {
  return request({
    url: '/rest/system/file/analyzeData',
    method: 'POST',
    data: param
  })
}

// 解析
export function fileSystemMobileCategory(param) {
  return request({
    url: '/rest/system/file/mobile/category',
    method: 'POST',
    data: param
  })
}
