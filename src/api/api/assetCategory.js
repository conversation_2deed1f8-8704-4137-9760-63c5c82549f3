/*
 * @Description:
 */
/*
 * @Description:
 */
import request from '@/utils/request'
const qs = require("qs");

// ################ 资源目录 ########################

export function cataloguePageBy(params) {
    return request({
        url: `/rest/core/dataasset/catalogue/listByPage`,
        method: 'GET',
        params
    })
}

export function catalogueListBy(params) {
    return request({
        url: `/rest/core/dataasset/catalogue/list`,
        method: 'GET',
        params
    })
}


export function catalogueSaveBy(data) {
    return request({
        url: `/rest/core/dataasset/catalogue/save`,
        headers: {
          'Content-Type': 'application/json'
        },
        method: 'post',
        data: data,
    })
}

export function catalogueDeleteBy(data) {
    return request({
        url: `/rest/core/dataasset/catalogue/delete`,
        method: 'post',
        data: qs.stringify(data),
    })
}

// ################ 类目接口 ########################

export function categoryTreeBy(params) {
    return request({
        url: `/rest/core/dataasset/category/tree`,
        method: 'GET',
        params
    })
}

export function categoryTreeForView(params) {
  return request({
    url: `/rest/core/dataasset/category/view/tree`,
    method: 'GET',
    params
  })
}


export function categoryListBy(params) {
  return request({
    url: `/rest/core/dataasset/category/list`,
    method: 'GET',
    params
  })
}

export function categorySaveBy(data) {
    return request({
        url: `/rest/core/dataasset/category/save`,
        method: 'post',
        data: qs.stringify(data),
    })
}
export function categoryDeleteBy(data) {
    return request({
        url: `/rest/core/dataasset/category/delete`,
        method: 'post',
        data: qs.stringify(data),
    })
}

// ################ 资产接口 ########################

export function assetPageBy(params) {
  return request({
    url: `/rest/core/dataAsset/listByPage`,
    method: 'GET',
    params
  })
}

export function assetTagPageToView(params) {
  return request({
    url: `/rest/core/dataAsset/view/listByPage`,
    method: 'GET',
    params
  })
}

export function assetPublish(params) {
  return request({
    url: `/rest/core/dataAsset/publish`,
    method: 'GET',
    params
  })
}

export function assetClosePublish(params) {
  return request({
    url: `/rest/core/dataAsset/closePublish`,
    method: 'GET',
    params
  })
}

export function assetSaveBy(data) {
  return request({
    url: `/rest/core/dataAsset/save`,
    headers: {
      'Content-Type': 'application/json'
    },
    method: 'post',
    data: data,
  })
}

export function assetSaveDataBy(data) {
  return request({
    url: `/rest/core/dataAsset/save/asset`,
    headers: {
      'Content-Type': 'application/json'
    },
    method: 'post',
    data: data,
  })
}

export function assetDeleteBy(data) {
  return request({
    url: `/rest/core/dataAsset/delete`,
    method: 'post',
    data: qs.stringify(data),
  })
}

export function startDownloadAsset(id) {
  return request({
    url: `/rest/core/dataAsset/download/${id}`,
    method: 'GET'
  })
}

export function startDownloadAssetTable(params) {
  return request({
    url: `/rest/core/dataAsset/download/table`,
    method: 'GET',
    params
  })
}


export function startDownloadAssetFile(id) {
  return request({
    url: `/rest/core/dataAsset/download/file/${id}`,
    method: 'GET',
    responseType: 'blob'
  })
}

export function listenDownloadAsset(data) {
  return request({
    url: '/rest/core/dataAsset/download/listen',
    method: 'POST',
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
    },
    data: qs.stringify(data)
  })
}

export function downloadAttachment(id) {
  return request({
    url: `/rest/base/files/download/${id}`,
    method: 'GET',
    responseType: 'blob'
  })
}

export function assetTemplateAnalysis(data) {
  return request({
    url: `/rest/core/dataAsset/analysis`,
    method: 'POST',
    data: qs.stringify(data)
  })
}


export function assetTemplateDownload() {
  return request({
    url: `/rest/core/dataAsset/template/download`,
    method: 'GET',
    responseType: 'blob'
  })
}

export function assetTemplateList(params) {
  return request({
    url: `/rest/core/dataAsset/analysis/list`,
    method: 'GET',
    params
  })
}

export function assetTemplateReImport(data) {
  return request({
    url: `/rest/core/dataAsset/reImport/list`,
    method: 'POST',
    data: qs.stringify(data)
  })
}


export function assetTemplateListenAnalysis(params) {
  return request({
    url: `/rest/core/dataAsset/listen/analysis`,
    method: 'GET',
    params
  })
}

export function assetTemplateSave(data) {
  return request({
    url: `/rest/core/dataAsset/save/list`,
    method: 'POST',
    data: qs.stringify(data)
  })
}
export function assetTemplateDropList(data) {
  return request({
    url: `/rest/core/dataAsset/drop/list`,
    method: 'POST',
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
    },
    data: qs.stringify(data)
  })
}


// ################ 资源标签 ########################

export function assetTagPageBy(params) {
  return request({
    url: `/rest/core/dataasset/tag/listByPage`,
    method: 'GET',
    params
  })
}

export function assetTagListBy(params) {
  return request({
    url: `/rest/core/dataasset/tag/list`,
    method: 'GET',
    params
  })
}


export function assetTagSaveBy(data) {
  return request({
    url: `/rest/core/dataasset/tag/save`,
    method: 'post',
    data: qs.stringify(data),
  })
}

export function assetTagDeleteBy(data) {
  return request({
    url: `/rest/core/dataasset/tag/delete`,
    method: 'post',
    data: qs.stringify(data),
  })
}

// ################ 整库编目表变更记录 ########################

// export function assetTableChangeHistory(params) {
//   return request({
//     url: `/rest/core/dataAsset/table/record/list`,
//     method: 'GET',
//     params
//   })
// }

export function assetTableChangeHistory(params) {
  return request({
    url: `/rest/core/dataAsset/metadata/record/page`,
      method: 'GET',
      params
  })
}
