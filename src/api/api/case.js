/*
 * @Description:
 */
/*
 * @Description:
 */
/*
 * @Description:
 */
import request from '@/utils/request'
const qs = require("qs");


export function getCaseGroupList(params) {
    return request({
        url: '/rest/core/case/list/group',
        method: 'get',
        params: params,
    })
}

export function getCaseList(params) {
    return request({
        url: '/rest/core/case/list',
        method: 'get',
        params: params,
    })
}

export function listCaseByPage(params) {
    return request({
        url: '/rest/core/case/listByPage',
        method: 'get',
        params: params,
    })
}

export function getCaseDetail(id) {
  return request({
    url: `/rest/core/case/${id}`,
    method: 'get'
  })
}

export function saveCase(data) {
  return request({
    url: '/rest/core/case/save',
    method: 'post',
    headers: {
      "Content-Type": "application/json",
    },
    data
  })
}

export function deleteCase(params) {
  return request({
    url: `/rest/core/case/delete`,
    method: 'get',
    params: params,
  })
}

export function getStreet(params) {
  return request({
    url: `/rest/core/case/street`,
    method: 'get',
    params: params,
  })
}

export function reasonGroup(params) {
  return request({
    url: `/rest/core/case/reason/group`,
    method: 'get',
    params: params,
  })
}


export function listReason(params) {
  return request({
    url: `/rest/core/case/reason/list`,
    method: 'get',
    params: params,
  })
}

export function exportCaseInfo(params) {
  return request({
    url: '/rest/core/case/export',
    method: 'GET',
    responseType: 'blob',
    params
  })
}


export function saveCaseRecord(data) {
  return request({
    url: '/rest/core/case/record/save',
    method: 'post',
    headers: {
      "Content-Type": "application/json",
    },
    data
  })
}

export function pageCaseRecord(params) {
  return request({
    url: `/rest/core/case/record/listByPage`,
    method: 'get',
    params: params,
  })
}

export function listCaseByRecord(params) {
  return request({
    url: `/rest/core/case/record/case/list`,
    method: 'get',
    params: params,
  })
}


export function deleteCaseRecord(params) {
  return request({
    url: `/rest/core/case/record/delete`,
    method: 'get',
    params: params,
  })
}
