import request from '@/utils/request'
import fileRequest from "@/utils/fileRequest"

// 查询日志分页列表
export function pageLog(data) {
  return request({
    url: '/log/page',
    method: 'POST',
    data: data
  })
}
// 查询日志详细
export function getLog(id) {
  return request({
    url: `/log/${id}`,
    method: 'get'
  })
}

// 删除日志
export function delLog(id) {
  return request({
    url: `/log/${id}`,
    method: 'delete'
  })
}

// 新增or修改日志
export function saveLog(data) {
  return request({
    url: '/log/save',
    method: 'POST',
    data: data
  })
}

export function listLog() {
  return request({
    url: '/log/list',
    method: 'get'
  })
}

export function listLogByQuery(data) {
  return request({
    url: '/log/query/list',
    method: 'POST',
    data: data
  })
}

export function logExport() {
  return fileRequest({
    url: '/log/export',
    method: 'get'
  })
}

export function logExportDp() {
  return fileRequest({
    url: '/log/exportDp',
    method: 'get'
  })
}
