import request from '@/utils/request'


/**
 * 专项活动数据同步进度查询接口
 * @param {*} id
 * @returns
 */
export function modelJobStatus(id) {
  return request({
    url: '/business/model/jobStatus/' + id,
    method: 'POST'
  })
}

/**
 * 导出Excel
 * @param {模型id} id
 */
export function downloadExcel(id) {
  return request({
    url: '/business/model/downloadExcel/' + id,
    method: 'GET',
    responseType: 'blob'
  })
}


export function ackAllClue() {
  return request({
    url: '/user/message/allread',
    method: 'get',
  })
}

export function getExcludeTodoStatusByClueIds (data) {
  return request({
    url: '/message/confirm/exclude',
    method: 'post',
    data: data
  })
}

export function getCooperationStatusByClueIds (data) {
  return request({
    url: '/cooperation/confirm/message',
    method: 'post',
    data: data
  })
}


export function getMsgPage (data) {
  return request({
    url: '/user/message/page',
    method: 'post',
    data : data
  })
}

export function getGroupList (data) {
  return request({
    url: '/user/message/groupList',
    method: 'post',
    data : data
  })
}

export function countBy (data) {
  return request({
    url: '/user/message/countBy',
    method: 'post',
    data : data
  })
}



export function getMsgList (data) {
  return request({
    url: '/user/message/query/list',
    method: 'post',
    data : data
  })
}

export function updateReadMessageByIds (data) {
  return request({
    url: '/user/message/read',
    method: 'post',
    data : data
  })
}
