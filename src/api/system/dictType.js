import request from '@/utils/request'

// 删除字典类型管理
export function delDictType(id) {
  return request({
    url: '/dictType/' + id,
    method: 'post'
  })
}

// 获取全部字典类型含有字典分类类型项
export function getAllDict(data) {
  return request({
    url: '/dictType/listAll',
    method: 'get',
    // params: data
  })
}

// 字典保存操作
export function dictTypeSave(data) {
  return request({
    url: '/dictType/save',
    method: 'post',
    data: data
  })
}

// 根据code获取列表
export function listAllDictType(code) {
  return request({
    url: '/dictType/listAllDictType/' + code,
    method: 'post',
  })
}

