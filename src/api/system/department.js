import request from '@/utils/request'

// 查询系统部门分页列表
export function pageDepartment(data) {
  return request({
    url: '/department/page',
    method: 'POST',
    data: data
  })
}
// 查询系统部门详细
export function getDepartment(id) {
  return request({
    url: `/department/${id}`,
    method: 'get'
  })
}

// 删除系统部门
export function delDepartment(id) {
  return request({
    url: `/department/${id}`,
    method: 'delete'
  })
}

// 新增or修改系统部门
export function saveDepartment(data) {
  return request({
    url: '/department/save',
    method: 'POST',
    data: data
  })
}

// 修改系统部门名称
export function updateNameDepartment(data) {
  return request({
    url: '/department/updateName',
    method: 'POST',
    data: data
  })
}

export function listDepartment() {
  return request({
    url: '/department/list',
    method: 'get'
  })
}

export function listDepartmentByQuery(data) {
  return request({
    url: '/department/query/list',
    method: 'POST',
    data: data
  })
}

// 获取部门树
export function treeDepartment() {
  return request({
    url: `/department/tree`,
    method: 'get'
  })
}

// 获取所属部门下的部门树
export function treeSelfDepartment() {
  return request({
    url: `/department/tree/self`,
    method: 'get'
  })
}

// 获取子部门部门树
export function treeDepartmentById(departmentId) {
  return request({
    url: `/department/tree/` + departmentId,
    method: 'get'
  })
}

// 获取子部门部门树
export function treeDepartmentByCode(departmentCode) {
  return request({
    url: `/department/tree/code/` + departmentCode,
    method: 'get'
  })
}
