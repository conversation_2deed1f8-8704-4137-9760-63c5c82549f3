import request from '@/utils/request'

// 删除字典项
export function delDict(id) {
  return request({
    url: '/dict/' + id,
    method: 'post',
    dataType: 'json'
  })
}

// 获取全部字典项
export function listAll() {
  return request({
    url: '/dict/listAll',
    method: 'post'
  })
}


/**
 * 字典项保存
 * @RequestBody DictDO对象 data
 */
export function dictSave(data) {
  return request({
    url: '/dict/save',
    method: 'post',
    data: data
  })
}

/**
 * 多选删除字典项
 * @PathVariable ids
 */
export function deleteAll(ids) {
  return request({
    url: '/dict/deleteAll/' + ids,
    method: 'post'
  })
}

/**
 * 通过字典类型编码查询字典项接口
 * @param {字典类型编码} code
 */
 export function fetchDictByCode(code,param) {
  return request({
    url: '/dict/listDictClassificationType',
    method: 'POST',
    data: code,
    params:param
  })
}
