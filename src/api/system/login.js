import request from '@/utils/request'
import unityRequest from "@/utils/unityRequest"
import md5 from 'md5';

// 用户登陆后使用
export function getInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function detail() {
  return request({
    url: '/user/detail',
    method: 'get'
  })
}

export function unityTicketToken(ticket) {
  return unityRequest({
    url: '/api/v1/auth/ticketValidation',
    method: 'POST',
    data: {
      ...sign(),
      ...ticket
    }
  })
}
export function unityLogin(params) {
  const ak = window.globalConfig.unityLogin.AK
  const sk = window.globalConfig.unityLogin.SK
  const requestTime = new Date().getTime()
  const sign = md5(ak + sk + requestTime)
  return unityRequest({
    baseURL: window.globalConfig.unityLogin.API,
    method: 'POST',
    headers: {
      sign: sign,
      requesttime: requestTime,
      ak: '5E59177673BBD0E',
    },
    data: params
  })
}

export function unitySkipApp(token,proxyappKey) {
  return unityRequest({
    url: '/api/v1/auth/getTicketByToken',
    method: 'POST',
    data: {
      ...sign(),
      token: token,
      proxyappKey: proxyappKey
    }
  })
}

export function sign() {
  const requestTime = new Date().getTime()
  const ak = API.ak
  const sk = API.sk
  const sign = md5(ak + sk + requestTime)
  return {
    requestTime: requestTime,
    appKey: ak,
    sign: sign
  }
}
