import request from '@/utils/request'

export function listRole() {
  return request({
    url: '/role/list',
    method: 'get'
  })
}


export function saveRole(data) {
  return request({
    url: '/role/save',
    method: 'post',
    data: data
  })
}

export function getRoleListByPage(data) {
  return request({
    url: '/role/page',
    method: 'post',
    data: data
  })
}

export function getListByRoleUserParm(data) {
  return request({
    url: '/role/user/role/parm',
    method: 'post',
    data: data
  })
}

export function removeRole(id) {
  return request({
    url: '/role/' + id,
    method: 'delete'
  })
}



export function getRole(id) {
  return request({
    url: '/role/' + id,
    method: 'get'
  })
}
