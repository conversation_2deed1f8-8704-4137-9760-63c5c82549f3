import store from '@/store'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import { Message, MessageBox } from 'element-ui'

// 创建axios实例
const service = axios.create({
  baseURL: API.unityAPI, // api的base_url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 100000000 // 请求超时时间
})

// respone拦截器
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * 通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
   * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
   */
  response => {
    const res = response.data

    if (res.statusCode === undefined) {
      return response.data
    }

    // if the custom code is not 20000, it is judged as an error.
    if (response.status === 200 && response.headers["content-type"] === 'application/msword') {
      return response;
    }

    if (response.status === 200 && response.headers["content-type"] === 'application/x-zip-compressed') {
      return response;
    }

    if (res.statusCode !== 200 && response.headers["content-type"] !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {

      if (res.statusCode === 400) {
        Message({
          message: res.data || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(new Error(res.data || 'Error'))
      }
      if (res.statusCode === 401) {
        // to re-login
        MessageBox.confirm('登录过期,您可以停留在次页面,或者重新登录', '确认退出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      if (res.statusCode === 403) {
        MessageBox.confirm('您无权访问该系统,请联系管理员', '403', {
          confirmButtonText: '回到登录页',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            window.location.href = unityLogin
          })
        }).catch(()=>{
          store.dispatch('user/resetToken').then(() => {
            window.location.href = unityLogin
          })
        })
        // this.$router.push('/403')
        // store.dispatch('user/resetS').then(() => {
        //   // console.log("407")
        //   location.reload()
        // })
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else if (response.headers["content-type"] === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
      return response
    } else {
      return res
    }
  },
  error => {
    console.log(error) // for debug
    if (error.response) {
      if (error.response.data instanceof Blob) {
        let reader = new FileReader()
        reader.onload = e => {
          console.log(e.target.result)
          Message({
            message: e.target.result,
            type: 'error',
            duration: 5 * 1000
          })
        }
        reader.readAsText(error.response.data)
      } else {
        Message({
          message: error.response.data,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else {
      Message({
        message: error.message,
        type: 'error',
        duration: 5 * 1000
      })
    }
    return Promise.reject(error)
  }
)

export default service
