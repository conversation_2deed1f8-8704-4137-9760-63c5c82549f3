import * as XLSX from 'sheetjs-style'

// 计算列宽（考虑中文字符）
export function calculateColWidth(data, field) {
  let maxWidth = field.label.length; // 初始宽度为表头长度

  data.forEach(item => {
    const value = item[field.prop];
    if (value !== null && value !== undefined) {
      const str = value.toString();
      // 中文字符按2个宽度计算
      const chineseChars = str.match(/[^\x00-\xff]/g) || [];
      const width = str.length + chineseChars.length;
      if (width > maxWidth) maxWidth = width;
    }
  });

  // 限制最小和最大宽度
  return Math.min(Math.max(maxWidth + 2, field.label.length + 4), 20);
}

// 计算单元格最大宽度，考虑中文字符
export function calculateWidth(str) {
  if (!str) return 10; // 默认宽度
  const chinese = str.match(/[^\x00-\xff]/g) || [];
  return Math.min(str.length + chinese.length + 2, 20); // 限制最大宽度
}

/**
 * @param data 数据
 * @param headNum 表头数量
 * @returns {WorkSheet}
 */
export function wsInit(data, headNum) {
  const ws = XLSX.utils.aoa_to_sheet(data);

  const borderStyle = {
    top: { style: 'thin', color: { rgb: '000000' } },
    bottom: { style: 'thin', color: { rgb: '000000' } },
    left: { style: 'thin', color: { rgb: '000000' } },
    right: { style: 'thin', color: { rgb: '000000' } }
  };

  // 设置表头样式
  const headerStyle = {
    font: { bold: true, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "409eff" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: borderStyle
  };

  // 设置数据单元格样式
  const dataStyle = {
    alignment: { horizontal: "center", vertical: "center" },
    border: borderStyle
  };

  // 获取工作表范围
  const range = XLSX.utils.decode_range(ws['!ref']);

  // 遍历所有行和列，确保每个单元格都有样式
  for(let R = range.s.r; R <= range.e.r; ++R) {
    for(let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({r:R, c:C});

      // 如果单元格不存在，先创建它
      if(!ws[cellAddress]) {
        ws[cellAddress] = { t: 's', v: '' }; // 创建空单元格
      }

      // 应用样式
      if(R < headNum) {
        ws[cellAddress].s = headerStyle;
      } else {
        ws[cellAddress].s = dataStyle;
      }
    }
  }

  return ws;
}
