import store from '@/store'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import { Message, MessageBox } from 'element-ui'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // api的base_url
  responseType: 'blob',
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000 // 请求超时时间
})

// request拦截器
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      // 让每个请求携带token为自定义key 请根据实际情况自行修改
      config.headers['_ut'] = getToken()
      // config.headers['_s'] = getToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// respone拦截器
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * 通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
   * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
   */
  response => {
    // if the custom code is not 20000, it is judged as an error.
    if (response.status === 401) {
      // to re-login
      MessageBox.confirm('登录过期,您可以停留在次页面,或者重新登录', '确认退出', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
    }
    else  if (response.status === 407) {
      store.dispatch('user/resetS').then(() => {
        // console.log("407")
        location.reload()
      })
    }
    else if (response.status === 200) {
      let fileName = ''
      let contentDisposition = response.headers['content-disposition']
      if (contentDisposition) {
        // 正则获取filename的值
        let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        let matches = filenameRegex.exec(contentDisposition)
        if (matches != null && matches[1]) {
          fileName = matches[1].replace(/['"]/g, '')
        }
        // 通过 URLEncoder.encode(pFileName, StandardCharsets.UTF_8.name()) 加密编码的, 使用decodeURI(fileName) 解密
        fileName = decodeURI(fileName)
      }
      if ('msSaveOrOpenBlob' in navigator) {//兼容ie
        let data = response.data;//获取响应
        let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        let blob = new Blob([response.data], { type: 'application/vnd.ms-excel;charset=gb2312' });
        let url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.style.display = 'none';
        link.download = fileName
        link.href = url;
        link.click()
        window.URL.revokeObjectURL(url);
      }
    }
  },
  error => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
