// fileDownloader.js

// 导出一个异步函数用于下载文件
export async function downloadFile(url, customFileName = null) {
    try {
        // 尝试从URL中提取文件名作为默认值
        const urlParts = url.split('/');
        const fileNameFromUrl = decodeURIComponent(urlParts[urlParts.length - 1]);

        // 如果提供了自定义文件名，则使用自定义文件名
        const fileName = customFileName || fileNameFromUrl;

        // 获取文件内容
        const response = await fetch(url); // 使用fetch API代替axios，更轻量级
        if (!response.ok) throw new Error('Network response was not ok');

        // 创建一个带有通用类型的 Blob 对象
        const blob = await response.blob();

        // 创建一个指向该 Blob 的 URL
        const fileUrl = window.URL.createObjectURL(blob);

        // 使用辅助函数下载文件，同时指定文件名
        createDownloadLink(fileUrl, fileName);

        // 清理
        setTimeout(() => window.URL.revokeObjectURL(fileUrl), 1000); // 释放对象 URL
    } catch (error) {
        console.error('下载失败:', error);
    }
}

// 辅助函数：创建并触发下载链接
function createDownloadLink(url, fileName) {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName; // 设置下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
