/**
 * 使用浏览器下载链接
 * @param src 地址
 * @param fileName 下载的文件名
 */
import request from "@/utils/request"

function down (src, fileName) {
  let x = new XMLHttpRequest();
  x.open("GET", src, true);
  x.responseType = 'blob';
  x.onload = function(e) {
    let url = window.URL.createObjectURL(x.response)
    let a = document.createElement('a');
    a.href = url
    a.download = fileName
    a.click()
  }
  x.send();
}

function downByQuery (src, fileName) {
  let from = new FormData()
  from.set("url", src)
  from.set("name", fileName)
  request({
    url: '/upload/download',
    baseURL: 'localhost:8099',
    method: 'POST',
    data: from
  }).then(res=>{
    console.log(res)
  })
}

export default  {
  down,
  downByQuery
}
