import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import Home from "@/views/Home";

let homePath = undefined
homePath = '/select'

// fetchTree({}).then(res => {
//   let item = res.data.find(f => f.title === '首页' && f.route !== null && f.menuType === 'C')
//   // homePath = '/' + item.path
// })

export const constantRoutes = [
  {
    path: '/help',
    redirect: '/help',
    component: Home,
    name: 'Help',
    hidden: true,
    children: [{
      path: '/help',
      component: () => import('@/views/main/index/index')
    }]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/403',
    component: () => import('@/views/error-page/403'),
    hidden: true
  },
  {
    path: '/index',
    component: () => import('@/views/modules/dashboard/index'),
    name: 'index',
    meta: { title: '首页', icon: 'guide', affix: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: () => {
      return 'index'
    },
    children: [
      {
        path: 'durationStatistics',
        component: () => import('@/views/modules/durationStatistics/index'),
        name: 'durationStatistics',
        meta: { title: '一审公诉案件办案时长统计 ', icon: 'guide', affix: true }
      },
      {
        path: 'tsfx.vue',
        component: () => import('@/views/modules/tsfx.vue/index'),
        name: 'tsfx.vue',
        meta: { title: '态势分析 ', icon: 'guide', affix: true }
      },
      {
        path: 'warningInfo',
        component: () => import('@/views/modules/warningInfo/index'),
        name: 'warningInfo',
        meta: { title: '案件质量预警 ', icon: 'guide', affix: true }
      },
      {
        path: 'calculationSentence',
        component: () => import('@/views/modules/calculationSentence/index'),
        name: 'calculationSentence',
        meta: { title: '刑期计算 ', icon: 'guide', affix: true }
      },
      {
        path: 'audioChat',
        component: () => import('@/views/modules/audioChat/index'),
        name: 'audioChat',
        meta: { title: '音频对话', icon: 'guide', affix: true }
      },
      {
        path: 'fileSystem',
        component: () => import('@/views/modules/systemFile/index'),
        name: 'fileSystem',
        meta: { title: '文件下载', icon: 'guide', affix: true }
      },
      {
        path: 'sms',
        component: () => import('@/views/modules/sms/index'),
        name: 'sms',
        meta: { title: '消息推送', icon: 'guide', affix: true }
      }
    ]
  },

]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
