import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

// const whiteList = ['/login', '/auth-redirect'] // no redirect whitelist
router.beforeEach(async(to, from, next) => {
  console.log('Navigation started:', { to: to.path, from: from.path })

  // start progress bar
  NProgress.start()

  if (to.path === '/durationStatistics') {
    next()
    NProgress.done()
  } else {
    // set page title
    document.title = getPageTitle(to.meta.title)
    const hasTicket = to.query.ticket

    if (hasTicket) {
      console.log('Has ticket:', hasTicket)
      try {
        await store.dispatch('user/unityTicketToToken', { 'ticket': hasTicket })
          .then(res => {
            console.log(res)
            store.dispatch('user/setToken', res)
            // 使用 replace: true 避免浏览器历史记录堆积
            next({ path: '/index', replace: true })
            // 确保函数在这里结束
          }).catch(err => {
            console.log('Ticket error:', err.message)
            next('/login')
          })
      } catch (error) {
        console.error('Ticket processing error:', error)
        next('/login')
      }
    } else {
      const hasToken = getToken()
      console.log('Has token:', hasToken)
      if (hasToken) {
        if (to.path === '/login') {
          // if is logged in, redirect to the home page
          next({ path: '/index', replace: true })
          NProgress.done()
        } else {
          try {
            // 简化逻辑，直接允许路由跳转
            next()
          } catch (error) {
            console.error('Route error:', error)
            await store.dispatch('user/resetToken')
            Message.error(error || '出现错误')
            window.location.href = unityLogin
          }
        }
      } else {
        console.log('No token, redirecting to SSO')
        window.location.href = unityLogin

      }
    }
  }

})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
