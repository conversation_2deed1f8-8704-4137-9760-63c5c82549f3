<!--
 * @Description: 
-->
<template>
  <div id="app" :class="classObj">
    <router-view />
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "App",
  computed: {
    ...mapState({
      style: state => state.app.style
    }),
    classObj() {
      return {
        style2: this.style === 2,
        style1: this.style === 1
      };
    }
  },
  mounted() {
    document.title = "首页";
    window.addEventListener(
      "mousewheel",
      function(event) {
        if (event.ctrlKey === true || event.metaKey) {
          event.preventDefault();
        }
      },
      { passive: false }
    );
  }
};
</script>

<style>
* {
  font-family: MicrosoftYaHei, "微软雅黑", "思源黑体", "方正黑体", "黑体";
}

table .el-button--text {
  position: relative;
}

table .el-button--text + .el-button--text:before {
  content: "";
  display: block;
  width: 1px;
  height: 50%;
  background-color: #dcdfe6;
  position: absolute;
  left: -9px;
  top: 25%;
}
</style>

<style lang="less">
.isRedTheme {
  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item-active {
    color: var(--ui-hl-color);
    border-color: var(--ui-hl-color);
    font-weight: 600;
  }

  .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item:hover {
    color: var(--ui-hl-color);
    border-color: var(--ui-hl-color);
  }

  .ivu-menu-light.ivu-menu-vertical
    .ivu-menu-item-active:not(.ivu-menu-submenu) {
    color: var(--ui-hl-color);
    border-right: 2px solid var(--ui-hl-color);
    z-index: 2;
  }

  .ivu-menu-light.ivu-menu-vertical
    .ivu-menu-item:hover:not(.ivu-menu-submenu) {
    color: var(--ui-hl-color);
    border-right: 2px solid var(--ui-hl-color);
    z-index: 2;
  }
}
</style>
