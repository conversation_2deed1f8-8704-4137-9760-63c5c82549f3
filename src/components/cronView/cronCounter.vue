<!--
 * @Description: 获取cron表达式
-->





<template>
  <div>
    <el-scrollbar style="height: 100%">
      <el-form label-width="80px" style="padding: 10px">
        <el-form-item label="Cron">
          <span style="color: #E6A23C; font-size: 12px;">corn从左到右（用空格隔开）：秒 分 小时 月份中的日期 月份 星期中的日期 年份</span>
          <el-input v-model="value" auto-complete="off">
            <el-button slot="append" v-if="!showCronBox" icon="el-icon-arrow-up" @click="showCronBox = true"></el-button>
            <el-button slot="append" v-else icon="el-icon-arrow-down" @click="showCronBox = false"></el-button>
          </el-input>
          <el-collapse-transition>
            <div class="cron" :val="value_" v-show="showCronBox">
              <el-tabs v-model="activeName">
                <el-tab-pane label="秒" name="s">
                  <second-and-minute v-model="sVal" lable="秒"></second-and-minute>
                </el-tab-pane>
                <el-tab-pane label="分" name="m">
                  <second-and-minute v-model="mVal" lable="分"></second-and-minute>
                </el-tab-pane>
                <el-tab-pane label="时" name="h">
                  <hour v-model="hVal" lable="时"></hour>
                </el-tab-pane>
                <el-tab-pane label="日" name="d">
                  <day v-model="dVal" lable="日"></day>
                </el-tab-pane>
                <el-tab-pane label="月" name="month">
                  <month v-model="monthVal" lable="月"></month>
                </el-tab-pane>
                <el-tab-pane label="周" name="week">
                  <week v-model="weekVal" lable="周"></week>
                </el-tab-pane>
                <el-tab-pane label="年" name="year">
                  <year v-model="yearVal" lable="年"></year>
                </el-tab-pane>
              </el-tabs>
              <!-- table -->
              <el-table :data="tableData" size="mini" border style="width: 100%;">
                <el-table-column prop="sVal" label="秒">
                </el-table-column>
                <el-table-column prop="mVal" label="分">
                </el-table-column>
                <el-table-column prop="hVal" label="时">
                </el-table-column>
                <el-table-column prop="dVal" label="日">
                </el-table-column>
                <el-table-column prop="monthVal" label="月">
                </el-table-column>
                <el-table-column prop="weekVal" label="周">
                </el-table-column>
                <el-table-column prop="yearVal" label="年">
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-transition>
        </el-form-item>
        <el-form-item label="测试">
          <el-button size="mini" type="primary" @click="runCron">获取结果</el-button><br>
          <span style="font-size: 10px">最近10次运行时间:</span>
          <div style="border: 1px solid #e8e8e8;height: auto;min-height: 100px;width: 300px;padding: 5px">
            <el-empty :image-size="100" v-if="cronTestData.length === 0"></el-empty>
            <p v-else style="margin: -10px 5px" v-for="item in cronTestData">
              {{ item }}
            </p>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
  </div>
</template>

<script>
import SecondAndMinute from './counter/secondAndMinute'
import hour from './counter/hour'
import day from './counter/day'
import month from './counter/month'
import week from './counter/week'
import year from './counter/year'

export default {
  props: {
    value: {
      type: String
    }
  },
  data() {
    return {
      //
      activeName: 's',
      sVal: '*',
      mVal: '*',
      hVal: '*',
      dVal: '*',
      monthVal: '*',
      weekVal: '?',
      yearVal: '',
      cronTestData: [],
      showCronBox: false
    }
  },
  watch: {
    'value'(a, b) {
      this.updateVal()
    }
  },
  computed: {
    tableData() {
      return [{
        sVal: this.sVal,
        mVal: this.mVal,
        hVal: this.hVal,
        dVal: this.dVal,
        monthVal: this.monthVal,
        weekVal: this.weekVal,
        yearVal: this.yearVal
      }]
    },
    value_() {
      if (!this.dVal) {
        this.dVal = '*'
      }
      if (!this.weekVal) {
        this.weekVal = '?'
      }
      if (!this.sVal) {
        this.sVal = '*'
      }
      if (!this.mVal) {
        this.mVal = '*'
      }
      if (!this.hVal) {
        this.hVal = '*'
      }
      if (!this.monthVal) {
        this.monthVal = '*'
      }
      if (!this.dVal && !this.weekVal) {
        return ''
      }
      if (this.dVal === '?' && this.weekVal === '?') {
        this.$message.error('日期与星期不可以同时为“不指定”')
      }
      if (this.dVal !== '?' && this.weekVal !== '?') {
        this.$message.error('日期与星期必须有一个为“不指定”')
      }
      let v
      if (this.yearVal != null) {
        v = `${this.sVal} ${this.mVal} ${this.hVal} ${this.dVal} ${this.monthVal} ${this.weekVal} ${this.yearVal}`
      } else {
        v = `${this.sVal} ${this.mVal} ${this.hVal} ${this.dVal} ${this.monthVal} ${this.weekVal}`
      }
      if (v !== this.value) {
        this.$emit('input', v)
      }
      return v
    }
  },
  methods: {
    updateVal() {
      if (!this.value) {
        return
      }
      let arrays = this.value.split(' ')
      this.sVal = arrays[0]
      this.mVal = arrays[1]
      this.hVal = arrays[2]
      this.dVal = arrays[3]
      this.monthVal = arrays[4]
      this.weekVal = arrays[5]
      if (arrays.length > 5) {
        this.yearVal = arrays[6]
      }
    },
    initVal() {
      this.showCronBox = false
      this.cronTestData = []
      this.activeName = 's'
      let cron = '00 00 00 * * ?'
      let arrays = cron.split(' ')
      this.sVal = arrays[0]
      this.mVal = arrays[1]
      this.hVal = arrays[2]
      this.dVal = arrays[3]
      this.monthVal = arrays[4]
      this.weekVal = arrays[5]
      if (arrays.length > 5) {
        this.yearVal = arrays[6]
      }
    },
    runCron() {
      // runCron({"cron": this.value}).then(res=>{
      //   if (res.statusCode === 200) {
      //     this.cronTestData = res.data
      //   } else {
      //     this.$confirm(res.data,{
      //       confirmButtonText: "确认",
      //       cancelButtonText: "取消",
      //       type: "warning"
      //     }).then(() => {
      //     }).catch(() => {});
      //   }
      // })
    }
  },
  created() {
    this.updateVal()
  },
  components: {
    SecondAndMinute, hour, day, month, week, year
  }
}
</script>

<style lang="css">
.cron {
  text-align: left;
  padding: 10px;
  background: #fff;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12), 0 0 6px 0 rgba(0, 0, 0, .04);
}
</style>
