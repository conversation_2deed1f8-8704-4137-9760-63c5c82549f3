<template>
  <div style="background:#fff">
    <Rules v-on="$listeners" :item="rootItem" :position="0" style="padding: 10px;" ref="Rules" @addCondition="addCondition" @addConditionBlock="addConditionBlock" />
  </div>
</template>

<script>
  import Rules from './Rules'
  // import { getFields } from '@/api/xc'
  export default {
    name: 'CustomRules',
    provide() {
      return {
        _addCondition: this.addCondition,
        _addConditionBlock: this.addConditionBlock,
        _deleteConditionBlock: this.deleteConditionBlock,
        _deleteCondition: this.deleteCondition,
        _ruleList: () => this.ruleList,
        _fieldnameList: () => this.fieldList || this.fieldnameList,
        _fieldOperationList: () => this.fieldOperationList,
        _list: () => this.list,
        _readonly: () => this.readonly
      }
    },
    components: {
      Rules,
    },
    props: ['ruleList','fieldList' ,'fieldOperationList', 'value', 'readonly', 'simple'],
    data() {
      return {
        list: [],
        fieldnameList: [],
        rootItem: { id: 0 }
      }
    },
    mounted() {
      this.getList(this.value)
    },
    watch: {
      value(newVal){
        if (newVal) {
          console.log(newVal)
          this.getList(newVal)
        }
      }
    },
    methods: {
      // getFields(index) {
      //   getFields({ index }).then(res => {
      //     this.fieldnameList = res.data.map(item => ({ type: item.type, label: item.key, value: item.key }))
      //   })
      // },
      getList(val) {
        let value = val
        let list = []
        const getV = ({ connectRelation, children }, parentId) => {//修改查看时反解析

          let id = Date.now() * Math.random() + Math.random()
          if (!children) return
          children.forEach((item, index) => {
            if (item.connectRelation) {
              id = id + index + 'Block'
              list.push({
                type: 'addConditionBlock',
                id,
                parentId,
                _$: {
                  connectRelation: item.connectRelation
                }
              })
              getV(item, id)
            } else {//form
              id = id + index
              list.push({
                type: 'addCondition',
                id,
                parentId,
                _$: { id: item.id}
              })
            }
          })
          if (!parentId) {
            this.$set(this.rootItem, '_$', { connectRelation })
          }

        }
        getV(value, 0)
        this.list = list
      },
      getResult() {
        let Rules = this.$refs.Rules
        return Rules.getResult()
      },
      deleteCondition(_item) {
        this.list = this.list.filter(item => (item.id != _item.id))
      },
      addCondition(item) {
        this.list.push({
          type: 'addCondition',
          id: Date.now(),
          parentId: item.id
        })
      },
      addConditionBlock(item) {
        let id = Date.now() + 'Block'
        this.addCondition({ id })
        this.list.push({
          type: 'addConditionBlock',
          id,
          parentId: item.id
        })
      },
      deleteConditionBlock(_item) {
        this.list = this.list.filter(item => (item.id != _item.id) && (item.parentId != _item.id))
      }
    }
  }
</script>
