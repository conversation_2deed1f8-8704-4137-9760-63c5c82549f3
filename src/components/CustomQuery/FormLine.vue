<template>
  <div class="FormLine" v-loading="fieldnameChangeLoading">
    <template v-if="readonly">
      <div class="view">
        <span>{{ getFieldnameLabelById(id)||getFieldnameLabel(fieldname) }}</span>
        <el-tag type="info" size="mini" style="margin:0 5px">{{ getOperator<PERSON>abel(operator) }}</el-tag>
        <span style="flex:1">{{ getDictLabel(fieldname, value) }}</span>
      </div>

    </template>
    <template v-else>
      <el-select @change="onFieldChange" :disabled="readonly" filterable style="width:200px;margin-right:8px" size="mini" v-model="id">
        <el-option placeholder="请选择字段" v-for="item in options" :key="item.id" :label="item.label" :value="item.id">
        </el-option>
      </el-select>
      <el-select @change="onOpChange" :disabled="readonly" style="width:140px;margin-right:8px" size="mini" v-model="operator">
        <el-option placeholder="请选择操作符" v-for="item in operatorList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <!-- <el-input-number :disabled="readonly" size="mini" v-if="currentType=='number'" v-model="value" :precision="2" :step="1"></el-input-number> -->
      <template v-if="isInput">
        <el-select @paste.native.capture="handlePaste($event,'selector')" @focus="focusSelectValue()" ref="selector" @keydown.native="onKeyDown($event,'selector')" no-data-text="请输入后回车或空格添加多项" v-if="isMultiple" class="value-input" :disabled="readonly" size="mini" v-model="value" :multiple="isMultiple" filterable allow-create default-first-option placeholder="请输入后回车或空格添加多项">
        </el-select>
        <el-input class="value-input" :disabled="readonly" v-else placeholder="请输入值" size="mini" v-model="value"></el-input>
      </template>
      <!-- <el-autocomplete :disabled="readonly" v-if="isQuery" size="mini" v-model="value" :fetch-suggestions="fetchSuggestions" placeholder="请输入值"></el-autocomplete> -->
      <el-select @paste.native.capture="handlePaste($event,'querySelect')" ref="querySelect" @keydown.native="onKeyDown($event,'querySelect')" class="value-input" :disabled="readonly" size="mini" :key="+isMultiple" :remote-method="remoteMethod" :loading="remoteLoading" remote v-if="isQuery" v-model="value" :multiple="isMultiple" filterable allow-create default-first-option placeholder="请选择或输入">
        <el-option v-for="(item,index) in valueForList" :key="`${item.value}-${index}`" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-select class="value-input" :key="+isMultiple" filterable :disabled="readonly" :multiple="isMultiple" v-if="isSelect" placeholder="请选择值" size="mini" v-model="value">
        <el-option :key="item.value" :value="item.value" :label="item.label" v-for="(item) in valueForList"></el-option>
      </el-select>
      <el-date-picker class="value-input" size="mini" :disabled="readonly" v-if="isDate" v-model="value" align="right" type="date" placeholder="选择日期" :picker-options="pickerOptions">
      </el-date-picker>
      <el-cascader :key="+isMultiple" class="value-input" size="mini" :disabled="readonly" v-if="isCascader" v-model="value" :options="cascaderOptions" :show-all-levels="false" :props="{  multiple: isMultiple,checkStrictly: true  }" clearable></el-cascader>
      <!-- <el-input :disabled="readonly" v-if="isDate" placeholder="请输入值" size="mini" v-model="value"></el-input> -->
      <el-button v-if="!readonly" style="margin-left:8px" @click="_deleteCondition(item)" size="mini" type="danger" icon="el-icon-close"></el-button>
    </template>
  </div>
</template>

<script>
  import {
    getDate
  } from '@/libs/tools'
  // import { ruleKeywordsSearch, ruleReasonSearch, getCourtList } from '@/api/xc'
  let ValueCache = {}
  const Map = {
    string: ['text', 'keyword'],
    date: ['date'],
    boolean: ['boolean'],
    number: ['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float']
  }
  export default {
    name: 'FormLine',
    components: {},
    inject: ['_deleteCondition', '_list', '_fieldnameList', '_fieldOperationList', '_readonly'],
    props: ['item'],
    data() {
      return {
        id: '',
        cascaderOptions: [],
        pickerOptions: {
          shortcuts: [{
            text: '一周前',
            onClick(picker) {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', start);
            }
          }, {
            text: '一个月前',
            onClick(picker) {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', start);
            }
          }, {
            text: '三个月前',
            onClick(picker) {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', start);
            }
          }]
        },
        remoteLoading: false,
        valueForList: [],
        multiple: ['incontains', 'in', 'inContains', 'inContainsAnd'],
        fieldnameChangeLoading: false,
        value: '',
        operator: '',
        fieldname: '',
        tableName: '',
        constraintOpList: null,
        cascaderFieldnameMap: [{ fieldname: 'xxx', api: () => this.getCourtList() }],
        queryFieldnameMap: [
          // {
          //   fieldname: 'keywords', api: (query) => {
          //     return this.keywordsQuery(query)
          //   }
          // },
          // {
          //   fieldname: 'tags', api: (query) => {
          //     return this.keywordsQuery(query)
          //   }
          // },
          // {
          //   fieldname: 'transfer_case', api: (query) => {
          //     return this.reasonQuery(query)
          //   }
          // }
        ],
        selectFieldnameMap: [
          // { fieldname: 'assess_level', code: 'ASSESS_LEVEL' },
          // { fieldname: 'process_status', code: 'PROCESS_STATUS' },
          // { fieldname: 'case_type', code: 'CASE_TYPE' }
        ],
        dateFieldnameMap: [
          // {
          //   fieldname: 'case_time', formart: (v) => {
          //     return v ? getDate(v, 'year0') : v
          //   }
          // }
        ],
        inputFieldnameMap: [
          // { fieldname: 'dep_case_num' },
          // { fieldname: 'unified_case_num' },
          // { fieldname: 'admin_area_code' },
        ],
        dictTypes: this.$store.getters.dictTypes,
        dicts: this.$store.getters.dicts
      }
    },
    mounted() {
      this.initTypeMap(this.options)
      if (this.item && this.item._$) {
        const { value, operator, fieldname, id, tableName } = this.item._$
        this.operator = operator
        this.fieldname = fieldname
        this.value = value
        this.tableName = tableName
        let find = this.options.find(find => find.value == fieldname)
        this.id = id || (find && find.id)
        // if (this.id) {
        //   this.onFieldChange(this.id)
        // }
        if (!this.readonly) {
          if (this.isSelect) {
            let item = this.selectFieldnameMap.find(find => find.fieldname == fieldname)
            if (item.code) {
              this.getDictByCode(fieldname, item.code)
            }
          }
          if (this.isQuery) {
            this.remoteMethod('')
          }
          this.initConstraintOpList(find)

        }
        if (this.isMultiple) {
          this.value = value ? value.split(',') : []
        }
      }
    },
    methods: {
      handlePaste(event, s) {
        event.clipboardData.getData('Text')
        this.$nextTick(_ => {
          window.setTimeout(_ => {
            this.isMultiple && this.addCurrentInput(s)
          }, 100)
        })
      },
      getCourtList() {
        // getCourtList({ region: '浙江省' }).then(res => {
        //   res = res.data || []
        //   // res.sort((a, b) => a.sortCode - b.sortCode)
        //   let list = []
        //   res.forEach(item => {
        //     const { courtDependency, sortCode, parentUuid, uuid, children } = item
        //     let parent = res.find(find => find.uuid == parentUuid)
        //     if (parent) {
        //       parent.children = parent.children || []
        //       let i = {
        //         sortCode,
        //         label: courtDependency,
        //         value: courtDependency,
        //       }
        //       if (children) {
        //         i.children = children
        //       }
        //       parent.children.push(i)
        //     } else {
        //       list.push({
        //         sortCode,
        //         label: courtDependency,
        //         value: courtDependency,
        //         children
        //       })
        //     }
        //   })
        //   let sort = (list) => {
        //     list.sort((a, b) => a.sortCode - b.sortCode)
        //     list.forEach(item => {
        //       if (item.children) {
        //         sort(item.children)
        //       }
        //     })
        //   }
        //   sort(list)
        //   this.cascaderOptions = list
        // })
      },
      focusSelectValue() {
        // this.$refs.selector.$refs.input.blur = () => {
        //   this.addCurrentInput()
        // }
      },
      addCurrentInput(s) {
        let selector = this.$refs[s || 'selector']
        selector.selectOption()
      },
      onKeyDown(event, s) {

        const { keyCode } = event
        if (keyCode == 188 || keyCode == 32) {
          event.preventDefault()
          this.addCurrentInput(s)
        }

      },
      remoteMethod(query) {
        this.remoteLoading = true
        this.queryFieldnameMap.find(find => find.fieldname == this.fieldname)
          .api(query).then(res => {
            this.remoteLoading = false
            this.valueForList = res
          })
      },
      // fetchSuggestions(query, cb) {
      //   this.queryFieldnameMap.find(find => find.fieldname == this.fieldname)
      //     .api(query).then(res => {
      //       cb(res)
      //     })
      // },
      keywordsQuery(q) {
        return new Promise((resolve) => {
          // ruleKeywordsSearch({ q }).then(res => {
          //   resolve(res.map(item => ({ label: item.name, value: item.name })))
          // })
        })
      },
      reasonQuery(q) {
        return new Promise((resolve) => {
          // ruleReasonSearch({ q }).then(res => {
          //   resolve(res.map(item => ({ label: item.name, value: item.name })))
          // })
        })
      },
      onFieldChange(id) {
        let find = this.options.find(find => find.id == id)
        const fieldCode = this.fieldname = find.value
        this.tableName = find.tableName
        this.value = ''

        this.valueForList = []
        this.fieldnameChangeLoading = true
        if (ValueCache[fieldCode]) {
          this.valueForList = ValueCache[fieldCode]
        } else {
          if (this.isSelect) {
            let item = this.selectFieldnameMap.find(find => find.fieldname == fieldCode)
            if (item.code) {
              this.getDictByCode(fieldCode, item.code)
            }
          }
          if (this.isQuery) {
            this.remoteMethod('')
          }
          if (this.isCascader) {
            let item = this.cascaderFieldnameMap.find(find => find.fieldname == fieldCode)
            item.api && item.api()
          }
        }

        this.initConstraintOpList(find)
      },
      initConstraintOpList(find) {
        let opList = []
        if (find.operators) {
          opList = this._fieldOperationList().filter(operator => find.operators.split(',').indexOf(operator.value) > -1)
        } else {
          opList = [{ label: '等于', value: 'eq' }]
        }
        this.fieldnameChangeLoading = false
        this.constraintOpList = opList
      },
      getDictLabel(fieldname, value) {
        let type = fieldname.toLocaleUpperCase()
        let find = this.dictTypes.find(dictType => dictType.code === type)
        if (find) {
          let temp = this.dicts.filter(dict => dict.dictTypeId === find.id)
          ValueCache[fieldname] = temp.map(item => ({ value: item.code, label: item.name }))
          let result = ValueCache[fieldname].find(item => item.value === value)
          return result.label
        } else {
          return value
        }
      },
      getOperatorLabel(op) {
        let list = this._fieldOperationList() || this.operatorList
        let find = list.find(find => find.value == op)
        if (find) {
          return find.label
        }
        return op
      },
      getFieldnameLabel(fieldname) {
        let find = this.options.find(find => find.value == fieldname)
        if (find) {
          return find.label
        }
        return fieldname
      },
      getFieldnameLabelById(id) {
        let find = this.options.find(find => find.id == id)
        if (find) {
          return find.label
        }
      },
      onOpChange() {
        if (!this.readonly) {
          if (this.isMultiple) {
            this.value = []
          } else {
            this.value = ''
          }
        }
      },
      getDictByCode(fieldName, code) {
        let find = this.dictTypes.find(dictType => dictType.code === code)
        if (find) {
          let temp = this.dicts.filter(dict => dict.dictTypeId === find.id)
          this.valueForList = ValueCache[fieldName] = temp.map(item => ({ value: item.code, label: item.name }))
        }
      },
      initTypeMap(list) {
        let inputTemp = []
        let selectTemp = []
        list.forEach(option => {
          let type = option.value.toLocaleUpperCase()
          let isDict = this.dictTypes.some(dictType => {
            if (dictType.code === type)
              return true
          })
          if (isDict) {
            selectTemp.push({
              fieldname: option.value,
              code: type
            })
          } else {
            inputTemp.push({ fieldname: option.value })
          }
        })
        this.inputFieldnameMap = inputTemp
        this.selectFieldnameMap = selectTemp
      }
    },
    computed: {
      // isRemoteQuery() {
      //   return this.isSelect && !!this.selectFieldnameMap.find(find => find.fieldname == this.fieldname).api
      // },
      formart() {
        let find = [].concat(this.inputFieldnameMap, this.queryFieldnameMap, this.dateFieldnameMap, this.selectFieldnameMap)
          .find(find => find.fieldname == this.fieldname)
        if (find) {
          return find.formart
        }
      },
      isInput() {
        return !!this.inputFieldnameMap.find(find => find.fieldname == this.fieldname)
      },
      isQuery() {
        return !!this.queryFieldnameMap.find(find => find.fieldname == this.fieldname)
      },
      isDate() {
        return !!this.dateFieldnameMap.find(find => find.fieldname == this.fieldname)
      },
      isCascader() {
        return !!this.cascaderFieldnameMap.find(find => find.fieldname == this.fieldname)
      },
      isSelect() {
        return !!this.selectFieldnameMap.find(find => find.fieldname == this.fieldname)
      },
      isMultiple() {
        return !!this.multiple.includes(this.operator)
      },
      operatorList() {
        return (this.constraintOpList && this.constraintOpList.length) ? this.constraintOpList : (this._fieldOperationList() || [{ label: '等于', value: '=' }])
      },
      currentType() {
        let find = this.options.find(find => find.value == this.fieldname)
        let currentType
        if (find) {
          for (let key in Map) {
            if (key == find.type) currentType = key
            if (Map[key].includes(find.type)) {
              currentType = key
            }
          }
        }
        if (this.isDate) {
          return 'date'
        }
        return currentType

      },
      // 字段数组
      options() {
        return this._fieldnameList() || []
      },
      readonly() {
        return this._readonly() || false
      }
    },
    watch: {
      value(val) {
        if (this.isMultiple && val.length) {
          let list = []
          let flag = false
          val.forEach(item => {
            let arr = item.split(/,|，|\s/)
            if (arr.length > 1) {
              flag = true
              arr.forEach(i => {
                i = i.trim()
                if (i) {
                  !list.includes(i) && list.push(i)
                }
              })
            } else {
              !list.includes(arr[0]) && list.push(arr[0])
            }
          })
          if (flag) {
            this.value = list
          }
        }
      },
      operatorList(val) {
        if (val) {
          if (!val.find(find => find.value == this.operator)) {
            this.operator = val[0] && val[0].value
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .value-input {
    flex: 1;
  }
  .FormLine {
    display: flex;
  }
  .view {
    display: flex;
    align-items: center;
  }
</style>
