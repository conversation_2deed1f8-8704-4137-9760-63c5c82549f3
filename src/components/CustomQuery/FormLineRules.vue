<template>
  <div class="FormLine" v-loading="fieldnameChangeLoading">
    <template>
      <el-select @change="onFieldChange" placeholder="请选择规则" :disabled="readonly" filterable style="width:200px;margin-right:8px" size="mini" v-model="id">
        <el-option  v-for="item in options" :key="item.id" :label="item.ruleName" :value="item.id">
        </el-option>
      </el-select>
      <el-popover
        popper-class="popper"
        placement="right"
        trigger="click">
        <CustomQuery :readonly="true" :value="CustomQueryValue.ruleExpression" :field-operation-list="_fieldOperationList()" :field-list="_fieldnameList()" ></CustomQuery>
        <el-button slot="reference" @click="viewTheRules"  v-show="id" style="margin-left:8px" size="mini" type="primary">查看规则</el-button>
      </el-popover>

      <el-button v-if="!readonly" style="margin-left:8px" @click="_deleteCondition(item)" size="mini" type="danger" icon="el-icon-close"></el-button>
    </template>
  </div>
</template>

<script>
  import {
    getDate
  } from '@/libs/tools'
  import CustomQuery from '@/components/CustomQuery/CustomQuery'
  // import { ruleKeywordsSearch, ruleReasonSearch, getCourtList } from '@/api/xc'
  let ValueCache = {}
  const Map = {
    string: ['text', 'keyword'],
    date: ['date'],
    boolean: ['boolean'],
    number: ['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float']
  }
  export default {
    name: 'FormLineRules',
    components: {
      CustomQuery
    },
    inject: ['_deleteCondition', '_list', '_ruleList', '_fieldOperationList', '_readonly' ,'_fieldnameList'],
    props: ['item'],
    data() {
      return {
        id: '',
        cascaderOptions: [],
        pickerOptions: {
          shortcuts: [{
            text: '一周前',
            onClick(picker) {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', start);
            }
          }, {
            text: '一个月前',
            onClick(picker) {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', start);
            }
          }, {
            text: '三个月前',
            onClick(picker) {
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', start);
            }
          }]
        },
        remoteLoading: false,
        valueForList: [],
        multiple: ['incontains', 'in', 'inContains', 'inContainsAnd'],
        fieldnameChangeLoading: false,
        value: '',
        operator: '',
        rulename: '',
        rlueValue: undefined,
        ruleExpression: '',
        tableName: '',
        constraintOpList: null,
        cascaderFieldnameMap: [{ rulename: 'xxx', api: () => this.getCourtList() }],
        queryFieldnameMap: [
          // {
          //   rulename: 'keywords', api: (query) => {
          //     return this.keywordsQuery(query)
          //   }
          // },
          // {
          //   rulename: 'tags', api: (query) => {
          //     return this.keywordsQuery(query)
          //   }
          // },
          // {
          //   rulename: 'transfer_case', api: (query) => {
          //     return this.reasonQuery(query)
          //   }
          // }
        ],
        selectFieldnameMap: [
          // { rulename: 'assess_level', code: 'ASSESS_LEVEL' },
          // { rulename: 'process_status', code: 'PROCESS_STATUS' },
          // { rulename: 'case_type', code: 'CASE_TYPE' }
        ],
        dateFieldnameMap: [
          // {
          //   rulename: 'case_time', formart: (v) => {
          //     return v ? getDate(v, 'year0') : v
          //   }
          // }
        ],
        inputFieldnameMap: [
          // { rulename: 'dep_case_num' },
          // { rulename: 'unified_case_num' },
          // { rulename: 'admin_area_code' },
        ],
        dictTypes: this.$store.getters.dictTypes,
        dicts: this.$store.getters.dicts,
        CustomQueryValue: {}

      }
    },
    mounted() {
      this.initTypeMap(this.options)
      if (this.item && this.item._$) {
        const { value, operator, rulename, id, tableName, ruleExpression } = this.item._$
        this.operator = operator
        this.rulename = rulename
        this.value = value
        this.ruleExpression = ruleExpression
        this.tableName = tableName
        let find = this.options.find(find => find.value == rulename)
        this.id = id || (find && find.id)
        // if (this.id) {
        //   this.onFieldChange(this.id)
        // }
        if (!this.readonly) {
          if (this.isSelect) {
            let item = this.selectFieldnameMap.find(find => find.rulename == rulename)
            if (item.code) {
              this.getDictByCode(rulename, item.code)
            }
          }
          if (this.isQuery) {
            this.remoteMethod('')
          }
          this.initConstraintOpList(find)

        }
        if (this.isMultiple) {
          this.value = value ? value.split(',') : []
        }
      }
    },
    methods: {
      handlePaste(event, s) {
        event.clipboardData.getData('Text')
        this.$nextTick(_ => {
          window.setTimeout(_ => {
            this.isMultiple && this.addCurrentInput(s)
          }, 100)
        })
      },
      viewTheRules() {
        let find = this.options.find(v => v.id === this.id)
        if (find) {
          this.CustomQueryValue = JSON.parse(JSON.stringify(find))
          this.CustomQueryValue.ruleExpression = this.getRuleData(JSON.parse(find.ruleExpression))
          // this.$emit('showRule', find)
        }
      },
      getRuleData(ruleExpression) {
        let o = {}
        for (let key in ruleExpression) {
          o.connectRelation = key
          let children = ruleExpression[key]
          if (!(children instanceof Array)) {
            children = [children]
          }
          o.children = children.map(item => {
            if (('and' in item) || ('or' in item)) {
              return this.getRuleData(item)
            } else {
              return { id: item.id, fieldname: item.field, value: item.value, operator: item.op, tableName: item.tableName }
            }
          })
        }
        return o
      },
      getCourtList() {
        // getCourtList({ region: '浙江省' }).then(res => {
        //   res = res.data || []
        //   // res.sort((a, b) => a.sortCode - b.sortCode)
        //   let list = []
        //   res.forEach(item => {
        //     const { courtDependency, sortCode, parentUuid, uuid, children } = item
        //     let parent = res.find(find => find.uuid == parentUuid)
        //     if (parent) {
        //       parent.children = parent.children || []
        //       let i = {
        //         sortCode,
        //         label: courtDependency,
        //         value: courtDependency,
        //       }
        //       if (children) {
        //         i.children = children
        //       }
        //       parent.children.push(i)
        //     } else {
        //       list.push({
        //         sortCode,
        //         label: courtDependency,
        //         value: courtDependency,
        //         children
        //       })
        //     }
        //   })
        //   let sort = (list) => {
        //     list.sort((a, b) => a.sortCode - b.sortCode)
        //     list.forEach(item => {
        //       if (item.children) {
        //         sort(item.children)
        //       }
        //     })
        //   }
        //   sort(list)
        //   this.cascaderOptions = list
        // })
      },
      focusSelectValue() {
        // this.$refs.selector.$refs.input.blur = () => {
        //   this.addCurrentInput()
        // }
      },
      addCurrentInput(s) {
        let selector = this.$refs[s || 'selector']
        selector.selectOption()
      },
      onKeyDown(event, s) {

        const { keyCode } = event
        if (keyCode == 188 || keyCode == 32) {
          event.preventDefault()
          this.addCurrentInput(s)
        }

      },
      remoteMethod(query) {
        this.remoteLoading = true
        this.queryFieldnameMap.find(find => find.rulename == this.rulename)
          .api(query).then(res => {
            this.remoteLoading = false
            this.valueForList = res
          })
      },
      // fetchSuggestions(query, cb) {
      //   this.queryFieldnameMap.find(find => find.rulename == this.rulename)
      //     .api(query).then(res => {
      //       cb(res)
      //     })
      // },
      keywordsQuery(q) {
        return new Promise((resolve) => {
          // ruleKeywordsSearch({ q }).then(res => {
          //   resolve(res.map(item => ({ label: item.name, value: item.name })))
          // })
        })
      },
      reasonQuery(q) {
        return new Promise((resolve) => {
          // ruleReasonSearch({ q }).then(res => {
          //   resolve(res.map(item => ({ label: item.name, value: item.name })))
          // })
        })
      },
      onFieldChange(id) {
        let find = this.options.find(find => find.id == id)
        const fieldCode = this.rulename = find.ruleName
        this.value = ''
        this.ruleExpression = find.ruleExpression
        this.valueForList = []
        this.fieldnameChangeLoading = true
        if (ValueCache[fieldCode]) {
          this.valueForList = ValueCache[fieldCode]
        }
        this.initConstraintOpList(find)
      },
      initConstraintOpList(find) {
        let opList = []
        if (find.operators) {
          opList = this._fieldOperationList().filter(operator => find.operators.split(',').indexOf(operator.value) > -1)
        } else {
          opList = [{ label: '等于', value: 'eq' }]
        }
        this.fieldnameChangeLoading = false
        this.constraintOpList = opList
      },
      getDictLabel(rulename, value) {
        // let type = rulename.toLocaleUpperCase()
        let find = this.dictTypes.find(dictType => dictType.code === type)
        if (find) {
          let temp = this.dicts.filter(dict => dict.dictTypeId === find.id)
          ValueCache[rulename] = temp.map(item => ({ value: item.code, label: item.name }))
          let result = ValueCache[rulename].find(item => item.value === value)
          return result.label
        } else {
          return value
        }
      },
      getOperatorLabel(op) {
        let list = this._fieldOperationList() || this.operatorList
        let find = list.find(find => find.value == op)
        if (find) {
          return find.label
        }
        return op
      },
      getFieldnameLabel(rulename) {
        let find = this.options.find(find => find.value == rulename)
        if (find) {
          return find.label
        }
        return rulename
      },
      getFieldnameLabelById(id) {
        let find = this.options.find(find => find.id == id)
        if (find) {
          return find.label
        }
      },
      onOpChange() {
        if (!this.readonly) {
          if (this.isMultiple) {
            this.value = []
          } else {
            this.value = ''
          }
        }
      },
      getDictByCode(rulename, code) {
        let find = this.dictTypes.find(dictType => dictType.code === code)
        if (find) {
          let temp = this.dicts.filter(dict => dict.dictTypeId === find.id)
          this.valueForList = ValueCache[rulename] = temp.map(item => ({ value: item.code, label: item.name }))
        }
      },
      initTypeMap(list) {
        let inputTemp = []
        let selectTemp = []
        list.forEach(option => {
          let type = option.value
          let isDict = this.dictTypes.some(dictType => {
            if (dictType.code === type)
              return true
          })
          if (isDict) {
            selectTemp.push({
              rulename: option.value,
              code: type
            })
          } else {
            inputTemp.push({ rulename: option.value })
          }
        })
        this.inputFieldnameMap = inputTemp
        this.selectFieldnameMap = selectTemp
      }
    },
    computed: {
      formart() {
        let find = [].concat(this.inputFieldnameMap, this.queryFieldnameMap, this.dateFieldnameMap, this.selectFieldnameMap)
          .find(find => find.rulename == this.rulename)
        if (find) {
          return find.formart
        }
      },
      operatorList() {
        return (this.constraintOpList && this.constraintOpList.length) ? this.constraintOpList : (this._fieldOperationList() || [{ label: '等于', value: '=' }])
      },
      currentType() {
        let find = this.options.find(find => find.value == this.rulename)
        let currentType
        if (find) {
          for (let key in Map) {
            if (key == find.type) currentType = key
            if (Map[key].includes(find.type)) {
              currentType = key
            }
          }
        }
        return currentType
      },
      // 字段数组
      options() {
        return this._ruleList() || []
      },
      readonly() {
        return this._readonly() || false
      }
    },
    watch: {
      value(val) {
        if (this.isMultiple && val.length) {
          let list = []
          let flag = false
          val.forEach(item => {
            let arr = item.split(/,|，|\s/)
            if (arr.length > 1) {
              flag = true
              arr.forEach(i => {
                i = i.trim()
                if (i) {
                  !list.includes(i) && list.push(i)
                }
              })
            } else {
              !list.includes(arr[0]) && list.push(arr[0])
            }
          })
          if (flag) {
            this.value = list
          }
        }
      },
      operatorList(val) {
        if (val) {
          if (!val.find(find => find.value == this.operator)) {
            this.operator = val[0] && val[0].value
          }
        }
      }
    }
  }
</script>

<style lang="scss">
  .popper {
    background: #eaeaea;
    padding: 10px;
  }
  .value-input {
    flex: 1;
  }
  .FormLine {
    display: flex;
  }
  .view {
    display: flex;
    align-items: center;
  }
</style>
