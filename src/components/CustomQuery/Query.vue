<template>
  <div :class="{query:true,odd:position%2}">

    <div :key="item.id" v-for="(item,index) in showList">
      <FormLine ref="FormLine" :item="item" v-if="item.type=='addCondition'"></FormLine>
      <Query :position="position+1" ref="Query" :item="item" v-if="item.type=='addConditionBlock'">
      </Query>
      <el-divider v-if="index<(showList.length-1)">{{options.find(find=>find.value==value).label}}</el-divider>
    </div>
    <div v-if="!readonly" :style="{marginTop:showList.length?'10px':0,textAlign:showList.length?'right':'left'}">

      <el-select style="width:60px;margin-right:8px" size="mini" v-model="value">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-button @click="addCondition" size="mini" type="primary">增加条件</el-button>
      <el-button @click="addConditionBlock" size="mini" type="primary">增加条件块</el-button>
      <el-button type="danger" v-if="item.id!==0" @click="deleteConditionBlock" size="mini">删除条件块</el-button>
    </div>
  </div>
</template>

<script>
  import FormLine from './FormLine'
  import Query from './Query'
  export default {
    name: 'Query',
    inject: ['_readonly', '_addCondition', '_addConditionBlock', '_deleteConditionBlock', '_list'],
    components: { Query, FormLine },
    props: ['item', 'position'],
    data() {
      return {
        value: 'and',
        options: [{ label: '且', value: 'and' }, { label: '或', value: 'or' }],
      }
    },
    created() {
      if (this.item && this.item._$) {
        const { connectRelation } = this.item._$
        this.value = connectRelation
      }
    },
    methods: {
      getResult() {
        let obj = { connectRelation: this.value, children: [] }
        if (this.showList.length) {
          let Query = this.$refs.Query
          let FormLine = this.$refs.FormLine
          if (FormLine) {
            FormLine.forEach(item => {
              const {
                value,
                operator,
                tableName,
                fieldname,
                currentType,
                formart,
                isMultiple,
                name,
                id
              } = item
              if (fieldname && operator) {
                obj.children.push({
                  currentType,
                  origin: value,
                  name,
                  id,
                  value: formart ? formart(String(value)) : String(value),
                  operator,
                  fieldname,
                  isMultiple,
                  tableName
                })
              }

            })
          }
          if (Query) {
            Query.forEach(item => {
              obj.children.push(item.getResult())
            })
          }
        }
        console.log(obj)
        return obj
      },
      addCondition() {
        this._addCondition(this.item)
      },
      addConditionBlock() {
        this._addConditionBlock(this.item)
      },
      deleteConditionBlock() {
        this._deleteConditionBlock(this.item)
      }
    },
    computed: {
      showList() {
        return this._list().filter(item => (item.parentId === this.item.id))
      },
      readonly() {
        return this._readonly() || false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .query {
    padding: 10px;
    border: 1px solid #d3d3d3;
    background: #fff;

    ::v-deep .el-divider__text {
      background: #ffd128;
    }

    &.odd {
      background: #efefef;
    }

    &:hover {
      border-color: #409eff;
    }
  }
</style>
