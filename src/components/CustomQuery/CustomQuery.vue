<!--
 * @Description: 定制化查询条件
-->

<template>
  <div style="background:#fff">
    <Query :item="rootItem" :position="0" style="padding: 10px;" ref="Query" @addCondition="addCondition"
      @addConditionBlock="addConditionBlock" />
  </div>
</template>

<script>
import Query from './Query'
// import { getFields } from '@/api/xc'
export default {
  name: 'CustomQuery',
  provide() {
    return {
      _addCondition: this.addCondition,
      _addConditionBlock: this.addConditionBlock,
      _deleteConditionBlock: this.deleteConditionBlock,
      _deleteCondition: this.deleteCondition,
      _fieldnameList: () => this.fieldList || this.fieldnameList,
      _fieldOperationList: () => this.fieldOperationList,
      _list: () => this.list,
      _readonly: () => this.readonly
    }
  },
  components: {
    Query,
  },
  props: ['fieldList', 'fieldOperationList', 'value', 'readonly', 'simple'],
  data() {
    return {
      list: [],
      fieldnameList: [],
      rootItem: { id: 0 }
    }
  },
  watch: {
    value(val) {
      this.show()
    }
  },
  created() {
    if (this.value) {
      this.show()
    }
  },
  methods: {
    show() {
      let value = this.value
      let list = []
      const getV = ({ connectRelation, children }, parentId) => {//修改查看时反解析

        let id = Date.now() * Math.random() + Math.random()
        if (!children) return
        children.forEach((item, index) => {
          if (item.connectRelation) {
            id = id + index + 'Block'
            list.push({
              type: 'addConditionBlock',
              id,
              parentId,
              _$: {
                connectRelation: item.connectRelation
              }
            })
            getV(item, id)
          } else {//form
            id = id + index
            list.push({
              type: 'addCondition',
              id,
              parentId,
              _$: { id: item.id, fieldname: item.fieldname, value: item.value, operator: item.operator, tableName: item.tableName }
            })
          }
        })
        if (!parentId) {
          this.$set(this.rootItem, '_$', { connectRelation })
        }

      }
      getV(value, 0)
      this.list = list
    },
    // getFields(index) {
    //   getFields({ index }).then(res => {
    //     this.fieldnameList = res.data.map(item => ({ type: item.type, label: item.key, value: item.key }))
    //   })
    // },
    getResult() {
      let Query = this.$refs.Query
      return Query.getResult()
    },
    deleteCondition(_item) {
      this.list = this.list.filter(item => (item.id != _item.id))
    },
    addCondition(item) {
      this.list.push({
        type: 'addCondition',
        id: Date.now(),
        parentId: item.id
      })
    },
    addConditionBlock(item) {
      let id = Date.now() + 'Block'
      this.addCondition({ id })
      this.list.push({
        type: 'addConditionBlock',
        id,
        parentId: item.id
      })
    },
    deleteConditionBlock(_item) {
      this.list = this.list.filter(item => (item.id != _item.id) && (item.parentId != _item.id))
    }
  }
}
</script>
