<template>
  <div :class="{Rules:true,odd:position%2}">

    <div :key="item.id" v-for="(item,index) in showList">
      <FormLineRules v-on="$listeners" ref="FormLineRules" :item="item" v-if="item.type=='addCondition'"></FormLineRules>
      <Rules v-on="$listeners" :position="position+1" ref="Rules" :item="item" v-if="item.type=='addConditionBlock'">
      </Rules>
      <el-divider v-if="index<(showList.length-1)">{{options.find(find=>find.value==value).label}}</el-divider>
    </div>
    <div v-if="!readonly" :style="{marginTop:showList.length?'10px':0,textAlign:showList.length?'right':'left'}">

      <el-select v-if="!readonly" style="width:60px;margin-right:8px" size="mini" v-model="value">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-button v-if="!readonly" @click="addCondition" size="mini" type="primary">增加条件</el-button>
      <el-button v-if="!readonly" @click="addConditionBlock" size="mini" type="primary">增加条件块</el-button>
      <el-button v-if="!readonly && item.id!==0" type="danger" @click="deleteConditionBlock" size="mini">删除条件块</el-button>
    </div>
  </div>
</template>

<script>
  import FormLineRules from './FormLineRules'
  import Rules from './Rules'
  export default {
    name: 'Rules',
    inject: ['_readonly', '_addCondition', '_addConditionBlock', '_deleteConditionBlock', '_list', '_ruleList'],
    components: { Rules, FormLineRules },
    props: ['item', 'position'],
    data() {
      return {
        value: 'and',
        options: [{ label: '且', value: 'and' }, { label: '或', value: 'or' }],
      }
    },
    created() {
      if (this.item && this.item._$) {
        const { connectRelation } = this.item._$
        this.value = connectRelation
      }
    },
    methods: {
      getResult() {
        let obj = { connectRelation: this.value, children: [] }
        if (this.showList.length) {
          let Rules = this.$refs.Rules
          let FormLineRules = this.$refs.FormLineRules
          if (FormLineRules) {
            FormLineRules.forEach(item => {
              if (item.id) {
                let find = this._ruleList().find(val => val.id === item.id)
                obj.children.push({
                  id: find.id,
                  ruleExpression: find.ruleExpression,
                  rulename: find.ruleName
                })
              }

            })
          }
          if (Rules) {
            Rules.forEach(item => {
              obj.children.push(item.getResult())
            })
          }
        }
        console.log(obj)
        return obj
      },
      addCondition() {
        this._addCondition(this.item)
      },
      addConditionBlock() {
        this._addConditionBlock(this.item)
      },
      deleteConditionBlock() {
        this._deleteConditionBlock(this.item)
      }
    },
    computed: {
      showList() {
        return this._list().filter(item => (item.parentId === this.item.id))
      },
      readonly() {
        return this._readonly() || false
      }
    }
  }
</script>

<style lang="scss" scoped>
  .Rules {
    padding: 10px;
    border: 1px solid #d3d3d3;
    background: #fff;

    ::v-deep .el-divider__text {
      background: #ffd128;
    }

    &.odd {
      background: #efefef;
    }

    &:hover {
      border-color: #409eff;
    }
  }
</style>
