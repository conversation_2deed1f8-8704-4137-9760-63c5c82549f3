<!--
 * @Description: 统一上传文件组件
-->



<template>
  <div>
    <el-upload class="upload-demo" :headers="headers" :action="uploadURL" :on-remove="handleRemove"
      :on-success="handleSuccess" :before-upload="handleBeforeUpload" :before-remove="beforeRemove"
      :on-error="handleError" multiple :limit="limit" :accept="accept" :file-list="this.fileList"
      :on-exceed="handleExceed">
      <el-button size="mini" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">文件不能超过{{ this.maxSize }}MB</div>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "index",
  props: {
    limit: {
      type: Number,
      default: undefined
    },
    accept: {
      type: String,
      default: undefined
    },
    maxSize: {
      type: Number,
      default: 5
    },
    loadFileList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      fileList: [],
      headers: { '_ut': getToken() },
      uploadURL: this.fileUploadURL
    }
  },
  mounted() {
  },
  watch: {
    loadFileList: {
      immediate: true,
      handler(val, old) {
        this.fileList = val
      },
      deep: true
    }
  },
  methods: {
    handleError(err, file, fileList) {
      this.$message.error(err)
    },
    handleRemove(file, fileList) {
      console.log(fileList)
      this.fileList = fileList;
    },
    handleSuccess(response, file, fileList) {
      console.log(fileList)
      this.fileList = fileList;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ` + this.limit + ` 个文件。`);
    },
    beforeRemove(file, fileList) {
      if (file && file.status === "success") {
        return this.$confirm(`确定移除 ${file.name}？`);
      }
    },
    handleBeforeUpload(file) {
      console.log('文件类型:', file.type)
      console.log("文件大小:", file.size)
      if (file.size > this.maxSize * 1024 * 1024) {
        this.$message.error("文件不能超过" + this.maxSize + "MB!")
        return false
      }
    },
    getFileArr() {
      console.log(this.fileList)
      return this.fileList.map(res => {
        console.log(res)

        if (res.response == null) {
          return { name: res.name, url: res.url, type: res.type }
        } else {
          let name = res.response.data.name
          let url = res.response.data.url
          let type = res.raw.type
          if (type === undefined || type === '') {
            type = 'unknown'
          }
          return { name: name, url: url, type: type }
        }
      })
    }
  }
}
</script>

<style scoped></style>
