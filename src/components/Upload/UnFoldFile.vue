<template>
    <div class="gy-unfoldfile">
        <div class="con" :class="{unfold:isOpen}" :style="{maxHeight:maxHeight+'px'}" ref="con">
            <div v-if="i.name !==undefined && i.url !== undefined" class="gy-fileList" v-for="(i,index) in fileList">
                <el-link @click.stop="preView(i.name,i.url)" type="primary" :style="{maxWidth:maxWidth+'px'}">
                    {{i.name}}
                </el-link>
                <div @click.stop="downloadFile(i.name,prefixURL + i.url,index)" :class="{loading: showLoading[index]}">
                    下载&nbsp;
                    <div class="gy-is-loading" v-if="showLoading[index]"><svg class="icon" viewBox="0 0 1024 1024"
                            version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path fill="currentColor"
                                d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z" />
                        </svg></div>
                </div>
            </div>
        </div>
        <div v-if="showOpen">
            <div v-if="!isOpen" class="btn" :style="{marginTop:maxHeight*2/3+'px',lineHeight:maxHeight/3+'px'}" @click.stop="isOpen=!isOpen">展 开
            </div>
            <div v-else class="unbtn" @click.stop="isOpen=!isOpen">收起</div>
        </div>
      <el-dialog
        title="预览"
        :visible.sync="fileDialogVisible"
        @close="closeFileDialog"
        width="80%"
        top="15px"
        append-to-body
      >
        <div class="header">
          <span>预览</span>
          <i class="el-icon-close iccon-btn-close" @click="closeFileDialog" />
        </div>
        <div class="content-wrapper">
          <iframe :src="filePreUrl" width="100%" height="90%"></iframe>
        </div>
      </el-dialog>
    </div>
</template>

<script>
    import previewFile from "@/utils/previewFile";
    import { getDepartment } from "@/api/system/department"
    // import downloadFile from "@/utils/downloadFile";
    export default {
        name: "UnFoldFile",
        props: {
            maxHeight: Number,
            maxWidth: Number,
            fileList: Array,
        },
        data() {
            return {
                showOpen: false,
                isOpen: true,
                showLoading: [],
                prefixURL: this.fileHostURL,
                fileDialogVisible: false,
                filePreUrl: undefined
            }
        },
        watch: {
            fileList() {
                this.check()
            }
        },
        mounted() {
            this.$nextTick(() => {
                this.fileList.forEach((value, index) => {
                    this.showLoading[index] = false
                })
                this.check()
            })
        },
        methods: {
            // 预览文件函数
            preView(name, url) {
              this.fileDialogVisible = true
              let Base64 = require('js-base64').Base64
              let preViewUrl = process.env.VUE_APP_FILE_PREVIEW
              let txt = ''
              getDepartment(this.$store.state.user.departmentId).then(res => {
                if (res.statusCode === 200) {
                  txt += (res.data.name + '-')
                }
                var suffix = name.substring(name.lastIndexOf(".") + 1);
                switch (suffix) {
                  case 'txt':
                  case 'pdf':
                  case 'json':
                  case 'doc':
                  case 'docx':
                  case 'xls':
                  case 'xlsx':
                  case 'sql':
                  case 'ppt':
                  case 'pptx':
                  case 'mp3':
                  case 'mp4':
                  case 'jpg':
                  case 'png':
                    txt += this.$store.state.user.realName
                    this.filePreUrl = preViewUrl + encodeURIComponent(Base64.encode(this.fileHostURL + url)) + '&watermarkTxt=' + txt
                    // previewFile.preview(this.fileHostURL + url, name)
                    break
                  default:
                    this.$message.warning("无法预览该文件类型")
                }
              })
            },
            closeFileDialog(){
              this.filePreUrl = undefined
              this.fileDialogVisible = false
            },

            // 下载文件函数
            downloadFile(name, url, index) {
                if (!this.showLoading[index]) {
                    const that = this
                    that.$set(that.showLoading, index, true)
                    let x = new XMLHttpRequest();
                    x.open("GET", url, true);
                    x.responseType = 'blob';
                    x.onload = function (e) {
                        let Url = window.URL.createObjectURL(x.response)
                        let a = document.createElement('a');
                        a.href = Url
                        a.download = name
                        a.click()
                    }
                    x.onreadystatechange = function () {
                        if (x.readyState == 4) {
                            that.$set(that.showLoading, index, false)
                        }
                    }
                    x.send();
                }
            },
            // 检察高度函数
            check() {
                this.isOpen = true
                this.showOpen = false
                this.$nextTick(() => {
                    let height = this.$refs.con.offsetHeight;
                    if (height > this.maxHeight) {
                        this.showOpen = true
                        this.isOpen = false
                    }
                    else {
                        this.showOpen = false
                        this.isOpen = true
                    }
                })
            }
        }
    }
</script>

<style lang="scss" scoped>

::v-deep .el-dialog__body {
  padding: 0 !important;
  height: 90vh;
}

::v-deep .el-dialog__header {
  padding: 0px !important;
  padding-bottom: 0px !important;
  display: none !important;
}

.header {
  background-color: #4080f0;
  height: 40px;
  color: #fff;
  padding: 12px 16px;

  .iccon-btn-close {
    float: right;
    font-size: 18px;
    color: #c4dcf4;
    transition: all 0.3s;

    &:hover {
      color: #fff;
      cursor: pointer;
    }
  }
}

.content-wrapper {
  padding: 15px 35px 15px 15px;
  overflow: hidden;
  height: 95vh;

  .form {
    margin-top: 15px;

    .form-items-wrapper {
      border: 1px solid #e9eaec;
      padding: 16px 16px 0;
      overflow: hidden;

      .el-form-item {
        float: left;
        width: 50%;

        .el-select {
          width: 100%;
        }
      }
    }

    ::v-deep .el-form-item__label {
      font-size: 14px;
      font-weight: bold;
    }
  }

  .uploads {
    margin: 15px 0 15px 20px;

    ::v-deep .el-upload,
    ::v-deep .el-upload-dragger {
      width: 100%;
    }
  }
}
    // 展开样式
    .gy-unfoldfile {
        position: relative;
        > .con {
            overflow: hidden;
            text-overflow: ellipsis;
        }
        > .con.unfold {
            overflow: visible;
            max-height: none !important;
        }
        > div > .btn,
        > div > .unbtn {
            font-size: 14px;
            cursor: pointer;
            color: #409eff;
        }
        > div > .btn {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            text-align: center;
            backdrop-filter: blur(100px);
        }
        > div > .unbtn {
            float: right;
        }
    }
    // 文件下载列表样式
    .gy-fileList {
        width: 100%;
        display: flex;
        > a {
            flex-shrink: 1;
        }
        > a > span {
            overflow: hidden;
            text-align: start;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        > div {
            display: flex;
            align-items: center;
            line-height: 25px;
            margin-left: 1rem;
            font-size: 13px;
            cursor: pointer;
            color: #000;
            flex-shrink: 0;
            border-bottom: 1px solid transparent;
            &:hover {
                border-bottom-color: #000;
            }
            &.loading {
                cursor: wait;
            }
        }
    }
    .gy-is-loading {
        height: 1em;
        width: 1em;
        line-height: 1em;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        position: relative;
        fill: currentColor;
        color: inherit;
        font-size: inherit;
        animation: rotating 3s linear infinite;
    }
    @keyframes rotating {
        0% {
            transform: rotate(0);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>
