<!--
 * @Description: 日期范围选择器
-->



<template>
  <div>
    <span v-if="types.length > 0" class="span-time-select">
      <el-popover placement="top" trigger="hover">
        <el-radio-group v-model="timeType" @change="radioChange">
          <el-radio v-for="(radio, index) in types" :key="index" :label="radio.value" :style="{
            display: 'block',
            paddingTop: index === 0 ? '0' : '10px'
          }">
            {{ radio.label }}
          </el-radio>
        </el-radio-group>
        <label style="color: rgb(45, 140, 240);" slot="reference">{{ timeTypeLabel }}</label>
      </el-popover>
      - 筛选
    </span>
    <el-radio-group v-model="timeShortcut" size="small" @change="timeShortcutChange">
      <el-radio-button label="all">全部</el-radio-button>
      <el-radio-button label="today">今日</el-radio-button>
      <el-radio-button label="oneMonth">一个月</el-radio-button>
      <el-radio-button label="threeMonth">三个月</el-radio-button>
    </el-radio-group>
    <el-date-picker v-model="timeRange" type="daterange" range-separator="-" start-placeholder="开始日期"
      end-placeholder="结束日期" size="small" value-format="yyyy-MM-dd" @change="timeRangeChange" />
  </div>
</template>

<script>
let moment = require('moment')
moment.locale('zh-cn')

export default {
  name: 'DateRangeSelect',
  props: {
    types: {
      type: Array,
      default: () => {
        return [
          // { label: '', value: '' }
        ]
      }
    }
  },
  data() {
    return {
      timeType: '',
      timeTypeLabel: '',
      timeRange: [],
      timeShortcut: 'all'
    }
  },
  watch: {
    types: {
      handler(newValue) {
        if (newValue && newValue.length > 0) {
          this.timeType = newValue[0].value
          this.timeTypeLabel = newValue[0].label
        }
      },
      immediate: true
    }
  },
  methods: {
    toDefault() {
      this.timeRange = []
      this.timeShortcut = 'all'
    },
    timeShortcutChange(shortcut) {
      let range = []
      let now = moment().format('yyyy-MM-DD')
      range.push(now)
      if (shortcut === 'all') {
        range = null
      } else if (shortcut === 'today') {
        range.push(now)
      } else if (shortcut === 'oneMonth') {
        let before = moment().subtract(1, 'months').format('yyyy-MM-DD')
        range.unshift(before)
      } else if (shortcut === 'threeMonth') {
        let before = moment().subtract(3, 'months').format('yyyy-MM-DD')
        range.unshift(before)
      }
      this.timeRangeChange(range)
      this.timeRange = JSON.parse(JSON.stringify(range))
    },
    timeRangeChange(range) {
      this.$emit('change', range, this.timeType)
    },
    radioChange(value) {
      let temp = this.types.find(type => type.value === value)
      this.timeTypeLabel = temp.label
      this.$emit('change', this.timeRange, value)
    }
  }
}
</script>

<style lang="scss" scoped>
.span-time-select {
  font-size: 12px;
  font-weight: 600;
  margin-right: 5px;
}

::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
  background: #fff;
  color: #4080f0;
  border-color: #4080f0
}

.el-range-editor--small {
  width: 250px;

  ::v-deep .el-range-separator {
    padding-left: 2px;
  }
}
</style>
