<!--
 * @Description: 
-->
<template>
    <div>
        <!-- <el-upload class="upload-demo" :action="uploadUrl" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
            :headers="headers" :on-exceed="handleExceed" :limit="limit" :file-list="fileList" :on-success="handleSuccess"
            accept=".jpg,.jpeg,.png,.GIF,.JPG,.PNG">
            <el-button size="small" type="primary">点击上传</el-button>
        </el-upload> -->
        <el-button v-if="dialogImageUrl" @click="clearImg" type="text">清除封面</el-button>
        <el-upload class="upload-demo" :action="uploadUrl" :show-file-list="false" :on-success="handleSuccess"
            :headers="headers" :on-preview="handlePictureCardPreview" accept=".jpg,.jpeg,.png,.GIF,.JPG,.PNG">
            <img v-if="dialogImageUrl" :src="dialogImageUrl" class="cover">
            <i v-else slot="default" class="el-icon-plus upload-demo-icon"></i>
        </el-upload>
        <!-- 预览图片 -->
        <el-dialog :visible.sync="imageDialogVisible" @close="closeDialog" :modal="modal">
            <div class="header">
                <span>预览</span>
                <i class="el-icon-close iccon-btn-close" @click="closeDialog" />
            </div>
            <div class="content-wrapper">
                <img width="100%" :src="dialogImageUrl" alt="">
            </div>
        </el-dialog>

    </div>
</template>
<script>
import { getToken } from '@/utils/auth'

export default {
    props: {
        data: {
            type: String,
            default: ''
        },
        limit: {
            type: Number,
            default: 1
        },
        modal: {
            // require: false,
            type: Boolean,
            default: true
        }

    },
    model: {
        prop: 'data',
        event: 'change'
    },
    watch: {
        fileList: {
            handler(fileList) {
                this.$emit('change', fileList)
            },
            deep: true
        },
        data: {
            handler(val) {
                this.dialogImageUrl = val ? this.ImgPrefix + val : ''
            },
            deep: true
        }
    },
    data() {
        return {
            fileList: JSON.parse(JSON.stringify(this.data)),
            uploadUrl: process.env.VUE_APP_BASE_API + '/file/upload',
            headers: {
                '_ut': getToken()
            },
            imageDialogVisible: false,
            dialogImageUrl: '',
            ImgPrefix: process.env.VUE_APP_FILE_HOST_URL

        }
    },
    mounted() {
        if (this.data) {
            this.dialogImageUrl = this.ImgPrefix + this.data
        }
    },
    methods: {
        closeDialog() {
            this.imageDialogVisible = false
        },
        handleSuccess(res, file, fileList) {
            if (res.statusCode == 200) {
                if (res.data) {
                    // this.fileList.push(res.data);
                    this.dialogImageUrl = this.ImgPrefix + res.data.url
                    this.$emit('change', res.data.url)
                } else {
                    this.$message.error('上传图片成功，但是没有返回图片信息，请联系管理员')
                }
            } else {
                this.$message.error('图片上传失败')
            }
        },
        handleExceed(files, fileList) {
            this.$message.error('超出文件数量限制')
        },
        handleRemove(file, fileList) {
            this.fileList = fileList
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = process.env.VUE_APP_FILE_HOST_URL + file.url
            this.imageDialogVisible = true
        },
        clearImg() {
            this.dialogImageUrl = ''
            this.$emit('change', '')
        }
    }
}

</script>
<style lang="scss">
.upload-demo .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    .upload-masking {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, .5);
        opacity: 0;
    }
}

.upload-demo .el-upload:hover {
    border-color: #409EFF;

    .upload-masking {
        opacity: 1;
    }
}

.upload-demo-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.cover {
    width: 178px;
    height: 178px;
    display: block;
}
</style>