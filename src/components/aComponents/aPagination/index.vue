<!--
 * @Description: 
-->
<template>
    <div class="pagination-wrapper">
        每页显示
        <input class="pagination-input" type="text" v-model="listQuery.rows">
        条/共 {{ total }} 条记录 共 {{ pagecount }} 页
        <div class="pagination_box" @click="toFirst">
            <span class="pageFirst" />
        </div>
        <div class="pagination_box" @click="toPrev">
            <span class="pagePrev"></span>
        </div>
        第
        <input class="pagination-input" type="text" v-model="listQuery.page">
        页
        <div class="pagination_box" @click="toNext">
            <span class="pageNext" />
        </div>
        <div class="pagination_box" @click="toLast">
            <span class="pageLast" />
        </div>
        <div class="pagination_box" @click="search">
            <span style="color: #666;width: 20px;">GO</span>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        query: {
            type: Object,
            default: () => ({
                page: 1,
                rows: 10
            })
        },
        total: {
            type: Number,
            default: 0
        },
        getList: {
            type: Function
        },
    },
    data() {
        return {
            listQuery: this.query,
            pagecount: 0
        }
    },
    watch: {
        query(query) {
            this.listQuery = JSON.parse(JSON.stringify(query))
            this.pagecount = Math.ceil(this.total / this.listQuery.rows)
        },
        total() {
            this.pagecount = Math.ceil(this.total / this.listQuery.rows)
        },
    },
    methods: {
        //到第一页 
        toFirst() {
            this.listQuery.page = 1
            this.search()
        },
        // 到最后一页
        toLast() {
            this.listQuery.page = this.pagecount
            this.search()
        },
        // 下一页
        toNext() {
            if (this.listQuery.page != this.pagecount) {
                this.listQuery.page++
            }
            this.search()
        },
        toPrev() {
            if (this.listQuery.page != 1) {
                this.listQuery.page--
            }
            this.search()

        },

        // 刷新分页数据
        async search() {
            await this.getList()
            this.pagecount = Math.ceil(this.total / this.listQuery.rows)
        }
    }
}

</script>
<style scoped>
@import './index.scss'
</style>