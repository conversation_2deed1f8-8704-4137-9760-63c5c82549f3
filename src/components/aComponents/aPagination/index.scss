.pagination-wrapper {
    justify-content: flex-end;
    display: flex;
    align-items: center;
    margin: 8px 0;
    color: #666;
    font-size: 12px;
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    border-top: 1px solid rgb(222, 222, 222);


}

.pagination-input {
    outline: none;
    height: 30px;
    width: 30px;
    margin: 4px;
    padding: 4px;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    text-align: center;
}

.pagination_box {
    text-align: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 4px;
    background-color: #fff;
    cursor: pointer;
}

.pagination_box span {
    display: inline-block;
    vertical-align: top;
    height: 16px;
    width: 16px;
    line-height: 16px;

}

.pageFirst {
    background: url(../assets/control_icon.png) no-repeat;
    background-position: 0 -32px;
}

.pagePrev {
    background: url(../assets/control_icon.png) no-repeat;
    background-position: -16px -32px;
}

.pageNext {
    background: url(../assets/control_icon.png) no-repeat;
    background-position: -32px -32px;
}

.pageLast {
    background: url(../assets/control_icon.png) no-repeat;
    background-position: -48px -32px;
}