<!--
 * @Description: 按钮组件
-->
<template>
    <button @click="fn">
        <i v-if="icon" :class="icon"></i>
        <span>
            <slot>新建</slot>
        </span>
    </button>
</template>
<script>
export default {
    props: {
        icon: {
            type: String,
            // default: 'ico16'
            default: ''
        }
    },
    methods: {
        fn() {
            this.$emit('click')
        }
    }
}
</script>
<style scoped>
button {
    display: inline-block;
    padding: 2px 12px;
    background: #fff;
    border: 1px solid #DCDFE6;
    border-radius: 15px;
    cursor: pointer;
    height: 26px;
    color: rgb(102, 102, 102);
    font-size: 12px;
}

.blue {
    color: #409EFF;
}

.green {
    color: #67C23A;
}

i {
    font-size: 16px;
    /* margin: auto; */
    line-height: 26px;
}

.ico16 {
    overflow: hidden;
    display: inline-block;
    vertical-align: middle;
    height: 16px;
    width: 16px;
    line-height: 16px;
    background: url(../assets/icons.png) no-repeat;
    background-position: 0 0;
    cursor: pointer;
}

.edit {
    background-position: -96px -96px;
}

.delete {
    background-position: -112px -0px;
}
</style>
