<!--
 * @Description: 
-->
<template>
    <div class="input_wrapper">
        <input type="text" v-model="content" :placeholder="placeholder">
        <i class="el-icon-search" style="cursor: pointer;" @click="search" v-if="type == 'search'" />
    </div>
</template>
<script>
export default {
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        type: {
            type: String,
            default: ""
        },
        value: {
            type: String,
            default: ""
        },
        placeholder: {
            type: String,
            default: '在此输入'
        }
    },
    data() {
        return {
            content: this.value
        }
    },
    watch: {
        content(value) {
            this.$emit('change', value)
        }
    },
    methods: {
        search() {
            this.$emit('click')
        }
    }
}
</script>
<style scoped>
input {
    outline: none;
    border: none;
    font-size: 12px;
    height: 24px;
    margin: 0;
    text-align: left;
    width: 150px;
}

.input_wrapper {
    display: inline-block;
    background: #fff;
    padding: 0 10px;
    width: fit-content;
    border-radius: 15px;
    border: 1px solid #DCDFE6;
    height: 26px;
}

i {
    font-size: 14px;
}

input::-webkit-input-placeholder {
    color: #aab2bd;
    font-size: 12px;
}
</style>