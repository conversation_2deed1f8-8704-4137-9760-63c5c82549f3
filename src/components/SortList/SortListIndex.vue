<template>
  <div style="line-height:35px">
    <div class="cell" :key="index" v-for="(item,index) in list">
      <span @click="setCurrentItem(item)" :class="{label:true,selected:getSelected(item)}">
        {{item.label}}</span>
      <span class="caret-wrapper" v-if="!item.noCaret">
        <i :class="{selected:getAsc(item)}" @click="setCurrentItem(item,'asc')" class="sort-caret ascending"></i>
        <i :class="{selected:getDesc(item)}" @click="setCurrentItem(item,'desc')" class="sort-caret descending"></i>
      </span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'SortListIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        selectedList: [],
        currentItem: { label: '相关性', sortType: 'desc' }
      }
    },
    mounted() {
      let find = this.list.find(find => find.default)
      if (find) {
        this.setCurrentItem(find, null, true)
      }
    },
    methods: {
      getSelected(item) {
        return this.selectedList.find(find => {
          return find.label == item.label
        })
      },
      getAsc(item) {
        let find = this.selectedList.find(find => {
          return find.label == item.label && find.sortType == 'asc'
        })
        return find
      },
      getDesc(item) {
        let find = this.selectedList.find(find => {
          return find.label == item.label && find.sortType == 'desc'
        })
        return find
      },
      setCurrentItem(item, sortType, init) {
        sortType = sortType || (item.sortType)
        let { fieldName, label } = item
        if (!label || !sortType) {
          this.selectedList = []
          if (!init)
            return this.$emit('on-sort', [])
        }
        let find = this.selectedList.find(find => {
          return find.label == label
        })
        if (find) {
          this.selectedList = this.selectedList.filter(item => item != find)
          if (find.sortType == sortType) {

          } else {
            this.$set(find, 'sortType', sortType)
            this.selectedList.push(find)
          }
        } else {
          this.selectedList.push({ label, sortType, fieldName })
        }

        this.currentItem = { label, sortType }
        if (!init)
          this.$emit('on-sort', this.selectedList)
      }
    },
    computed: {},
    watch: {}
  }
</script>

<style lang="less" scoped>
  .name {
    font-size: 14px;
    display: inline-block;
    padding-right: 1em;
    margin: 0.25em 0;
    border-right: 1px solid #eee;
    line-height: 34px;
    color: #999;
    vertical-align: top;
  }
  .cell {
    font-size: 14px;
    position: relative;
    word-wrap: normal;
    vertical-align: top;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 34px;
    margin-left: 0.5em;

    &.divider {
      width: 1px;
      line-height: 34px;
      height: 20px;
      vertical-align: middle;
      background: #eee;
    }
  }
  .label {
    cursor: pointer;
    &.selected {
      color: #409eff;
    }
  }
  .caret-wrapper {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    height: 34px;
    width: 24px;
    vertical-align: middle;
    cursor: pointer;
    overflow: initial;
    position: relative;
    .sort-caret {
      width: 0;
      height: 0;
      border: 5px solid transparent;
      position: absolute;
      left: 7px;
      &.ascending {
        border-bottom-color: #c0c4cc;
        top: 5px;
        &.selected {
          border-bottom-color: #409eff;
        }
      }
      &.descending {
        border-top-color: #c0c4cc;
        bottom: 7px;
        &.selected {
          border-top-color: #409eff;
        }
      }
    }
  }
</style>
