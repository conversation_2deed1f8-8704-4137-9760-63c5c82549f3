<!--
 * @Description:排序按钮，点击切换升序降序后向父组件emit：sort事件
-->

<!--
 *                        ::
 *                       :;J7, :,                        ::;7:
 *                       ,ivYi, ,                       ;LLLFS:
 *                       :iv7Yi                       :7ri;j5PL
 *                      ,:ivYLvr                    ,ivrrirrY2X,
 *                      :;r@Wwz.7r:                :ivu@kexianli.
 *                     :iL7::,:::iiirii:ii;::::,,irvF7rvvLujL7ur
 *                    ri::,:,::i:iiiiiii:i:irrv177JX7rYXqZEkvv17
 *                 ;i:, , ::::iirrririi:i:::iiir2XXvii;L8OGJr71i
 *               :,, ,,:   ,::<EMAIL>:i:::j1jri7ZBOS7ivv,
 *                  ,::,    ::rv77iiiriii:iii:i::,<EMAIL>
 *              ,,      ,, ,:ir7ir::,:::i;ir:::i:i::rSGGYri712:
 *            :::  ,v7r:: ::rrv77:, ,, ,:i7rrii:::::, ir7ri7Lri
 *           ,     2OBBOi,iiir;r::        ,irriiii::,, ,iv7Luur:
 *         ,,     i78MBBi,:,:::,:,  :7FSL: ,iriii:::i::,,:rLqXv::
 *         :      iuMMP: :,:::,:ii;2GY7OBB0viiii:i:iii:i:::iJqL;::
 *        ,     ::::i   ,,,,, ::LuBBu BBBBBErii:i:i:i:i:i:i:r77ii
 *       ,       :       , ,,:::rruBZ1MBBqi, :,,,:::,::::::iiriri:
 *      ,               ,,,,::::i:  @arqiao.       ,:,, ,:::ii;i7:
 *     :,       rjujLYLi   ,,:::::,:::::::::,,   ,:i,:,,,,,::i:iii
 *     ::      BBBBBBBBB0,    ,,::: , ,:::::: ,      ,,,, ,,:::::::
 *     i,  ,  ,8BMMBBBBBBi     ,,:,,     ,,, , ,   , , , :,::ii::i::
 *     :      iZMOMOMBBM2::::::::::,,,,     ,,,,,,:,,,::::i:irr:i:::,
 *     i   ,,:;u0MBMOG1L:::i::::::  ,,,::,   ,,, ::::::i:i:iirii:i:i:
 *     :    ,iuUuuXUkFu7i:iii:i:::, :,:,: ::::::::i:i:::::iirr7iiri::
 *     :     :rk@Yizero.i:::::, ,:ii:::::::i:::::i::,::::iirrriiiri::,
 *      :      5BMBBBBBBSr:,::rv2kuii:::iii::,:i:,, , ,,:,:i@petermu.,
 *           , :r50EZ8MBBBBGOBBBZP7::::i::,:::::,: :,:,::i;rrririiii::
 *               :jujYY7LS0ujJL7r::,::i::,::::::::::::::iirirrrrrrr:ii:
 *            ,:  :@kevensun.:,:,,,::::i:i:::::,,::::::iir;ii;7v77;ii;i,
 *            ,,,     ,,:,::::::i:iiiii:i::::,, ::::iiiir@xingjief.r;7:i,
 *         , , ,,,:,,::::::::iiiiiiiiii:,:,:::::::::iiir;ri7vL77rrirri::
 *          :,, , ::::::::i:::i:::i:i::,,,,,:,::i:i:::iir;@Secbone.ii:::
 -->


<template>
  <div class="button-sort">
    <span :class="sort !== '' ? 'sorted' : ''" @click="handleSortChange()">{{ label }}</span>
    <span class="sort-icon-wrapper">
      <i class="sort-icon ascending" :class="sort === 'asc' ? 'ascending-selected' : ''"
        @click="handleSortChange('asc')" />
      <i class="sort-icon descending" :class="sort === 'desc' ? 'descending-selected' : ''"
        @click="handleSortChange('desc')" />
    </span>
  </div>
</template>

<script>
export default {
  name: 'ButtonSort',
  props: {
    label: {
      type: String,
      default: '排序按钮'
    }
  },
  data() {
    return {
      sort: ''
    }
  },
  methods: {
    handleSortChange(value) {
      if (value) {
        this.sortByIconButton(value)
      } else {
        this.sortByClickLabel()
      }
      this.$emit('sort', this.sort)
    },
    sortByClickLabel() {
      if (!this.sort) {
        this.sort = 'desc'
      } else if (this.sort === 'desc') {
        this.sort = 'asc'
      } else if (this.sort === 'asc') {
        this.sort = ''
      }
    },
    sortByIconButton(value) {
      if (this.sort === value) {
        this.sort = ''
      } else {
        this.sort = value
      }
    },
    reset() {
      this.sort = ''
      this.$emit('sort', this.sort)
    }
  }
}
</script>

<style lang="scss" scoped>
.button-sort {
  display: inline-block;
  margin-left: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;

  .sorted {
    color: #409eff;
  }

  .sort-icon-wrapper {
    display: inline-flex;
    vertical-align: middle;
    flex-direction: column;
    align-items: center;
    position: relative;
    height: 34px;
    width: 12px;

    .sort-icon {
      position: absolute;
      border: 5px solid transparent;
      height: 0;
      width: 0;
    }

    .ascending {
      border-bottom-color: #c0c4cc;
      top: 5px;
    }

    .ascending-selected {
      border-bottom-color: #409eff;
    }

    .descending {
      border-top-color: #c0c4cc;
      bottom: 7px;
    }

    .descending-selected {
      border-top-color: #409eff;
    }
  }
}
</style>
