export const textLength = 30;

export const areaLength = 500;

export const reg_phoneNum =  /^((1[3,5,8][0-9])|(14[5,7])|(17[0,6,7,8])|(19[7]))\d{8}$/;

export const reg_idCard =  /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

export const reg_email = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;

export const notNull = {
  label: '非空',
  value: 'notNull'
}

export const phone = {
  label: '手机号',
  value: 'phone'
}

export const idCard = {
  label: '身份证号',
  value: 'idCard'
}

export const email = {
  label: '邮箱',
  value: 'email'
}

export const regular = {
  label: '正则',
  value: 'regular'
}

export const max = {
  label: '字数不超过',
  value: 'max'
}

export const verifyList =[
  notNull,
  phone,
  idCard,
  email,
  regular,
  max
]
