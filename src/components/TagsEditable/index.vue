<template>
  <div class="wrapper">
    <span class="span-sub">标签: </span>
    <el-tag
      v-if="clueType"
      type="success"
      :effect='effect'
      size="small"
      disable-transitions
      style="margin: 0 5px 3px 0;position:relative;"
    >
      <span>{{ caseTypeInfo(clueType) }}</span>
      <el-popconfirm v-if="operate" :disabled="tagsDisabled"
        :title="'确认删除' + '标签: ' + caseTypeInfo(clueType) + ' ?'"
        icon="el-icon-info"
        icon-color="red"
        @confirm="handleTypeClose(row.id)"
      >
        <i class="el-icon-close icon-tag-close" slot="reference"/>
      </el-popconfirm>
    </el-tag>
    <template v-if="!clueType">
      <el-select size="mini" :label-in-value="true" :disabled="tagsDisabled"
                v-if="typeVisible" v-model="selType" placeholder="请选择">
        <el-option
          v-for="(item,index) in caseTypeDict"
          :key="index"
          :label="item.name"
          :value="item.code">
        </el-option>
      </el-select>
      <el-button class="button-new-tag_1" size="mini" v-if="typeVisible" type="success" :disabled="tagsDisabled"
                 @click="handleTypeSuccess" circle icon="el-icon-check"></el-button>
      <el-button class="button-new-tag_1" size="mini" v-if="typeVisible" type="danger" :disabled="tagsDisabled"
                 @click="handleTypeConfirm" circle icon="el-icon-close"></el-button>
      <el-button class="button-new-tag" v-if="!typeVisible && operate" size="mini" icon="el-icon-plus" :disabled="tagsDisabled"
                 @click="showType"></el-button>
    </template>
    <span class="span-sub">关键字: </span>
    <el-tag
      v-for="(tag, dex) in row.keywords"
      :key="tag.id"
      type="danger"
      :effect='effect'
      size="small"
      disable-transitions
      style="margin: 0 5px 3px 0;position:relative;"
    >
      <span>{{ tag.name }}</span>
      <el-popconfirm v-if="operate" :disabled="tagsDisabled"
        :title="'确认删除' + '关键字: ' + tag.name + ' ?'"
        icon="el-icon-info"
        icon-color="red"
        @confirm="handleClose(tag.id,dex)"
      >
        <i class="el-icon-close icon-tag-close" slot="reference"/>
      </el-popconfirm>
    </el-tag>
    <el-select size="mini" :label-in-value="true" :disabled="tagsDisabled"
               @change="getSelect"
               v-if="inputVisible" ref="keywords" v-model="value" default-first-option filterable allow-create placeholder="请选择">
      <el-option
        v-for="(item,index) in keywordsList"
        :key="index"
        :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
      <el-button class="button-new-tag_1" size="mini" v-if="inputVisible" type="success" :disabled="tagsDisabled"
                 @click="handleInputSuccess" circle icon="el-icon-check"></el-button>
      <el-button class="button-new-tag_1" size="mini" v-if="inputVisible" type="danger" :disabled="tagsDisabled"
                 @click="handleInputConfirm" circle icon="el-icon-close"></el-button>
      <el-button class="button-new-tag" v-if="!inputVisible && operate" size="mini" icon="el-icon-plus" :disabled="tagsDisabled"
                 @click="showInput"></el-button>
    <span class="span-sub" style="margin-left: 20px">领 域: </span>
    <el-tag
      v-for="(domain, index) in row.domains"
      :key="domain.id"
      :effect='effect'
      size="small"
      disable-transitions
      style="margin: 0 5px 3px 0;position:relative;"
    >
      <span>{{ domain.name }}</span>
      <el-popconfirm v-if="operate" :disabled="tagsDisabled"
        :title="'确认删除' + '领域: ' + domain.name + ' ?'"
        icon="el-icon-info"
        icon-color="red"
        @confirm="handleDomainClose(domain.id,index)"
      >
        <i class="el-icon-close icon-tag-close" slot="reference"/>
      </el-popconfirm>
    </el-tag>
<!--      <el-input-->
<!--        style="height: 28px"-->
<!--        :disabled="tagsDisabled"-->
<!--        class="input-new-tag"-->
<!--        v-model="inputValue"-->
<!--        ref="saveTagInput"-->
<!--        v-if="fieldVisible"-->
<!--        size="mini"-->
<!--        placeholder="请输入领域"-->
<!--      />-->
    <el-select size="mini" :label-in-value="true" :disabled="tagsDisabled"
               v-if="fieldVisible"
               ref="domains"
               v-model="inputValue"
               default-first-option filterable allow-create placeholder="请选择">
      <el-option
        v-for="(item,index) in domainsList"
        :key="index"
        :label="item.name"
        :value="item.name">
      </el-option>
    </el-select>
    <el-button class="button-new-tag_1" size="mini" v-if="fieldVisible" type="success" :disabled="tagsDisabled"
               @click="handleFieldEnter" circle icon="el-icon-check"></el-button>
    <el-button class="button-new-tag_1" size="mini" v-if="fieldVisible" type="danger" :disabled="tagsDisabled"
               @click="handleFieldConfirm" circle icon="el-icon-close"></el-button>
    <el-button class="button-new-tag" v-if="!fieldVisible && operate" size="mini" :disabled="tagsDisabled"
               @click.native.stop="showField" icon="el-icon-plus"></el-button>
    </div>
</template>
<script>
  export default {
    name: 'TagsEditable',
    props: {
      tagsDisabled:{
        type: Boolean ,
        default: false
      },
      operate:{
        type: Boolean ,
        default: false
      },
      row: {
        type: Object,
        default: () => {
          return []
        }
      },
      keywordsList: {
        type: Array,
        default: () => {
          return []
        }
      },
      domainsList: {
        type: Array,
        default: () => {
          return []
        }
      },
      caseTypeDict: {
        type: Array,
        default: () => {
          return []
        }
      },
      effect: {
        type: String,
        default: 'light'
      },
      clueType: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        typeVisible: false,
        inputVisible: false,
        fieldVisible: false,
        inputValue: '',
        value: '',
        label:'',
        selType: ''
      }
    },
    watch: {
      tags: {
        handler(val, oldVal){
        },
        deep: true
      }
    },
    computed: {
      caseTypeInfo() {
        return value => {
          let name = ''
          this.caseTypeDict.forEach(item => {
            if (item.code == value) {
              name = item.name
            }
          })
          return name
        }
      }
    },
    methods: {
      getSelect(val){
        let obj=this.keywordsList.find(item=>{
          return item.id == val;
        })
        this.label = obj.name;
      },
      handleTypeClose(id) {
        this.$emit('clueTypeDelete',id)
      },
      handleClose(id,index) {
        this.$emit('keywordDelete',id,index)
      },
      handleDomainClose(id,index){
        this.$emit('domainDelete',id,index)
      },
      showType() {
        this.typeVisible = true
      },
      showInput() {
        this.inputVisible = true
      },
      showField() {
        this.fieldVisible = true
      },
      handleTypeSuccess() {
        let data = {id: this.row.id, type: this.selType};
        if (data.type === ''){
          this.$message.warning("请选择标签")
          return
        }
        this.$emit('clueTypeAdd', data);
        this.handleTypeConfirm()
      },
      handleTypeConfirm() {
        this.typeVisible = false
        this.selType = '';
      },
      handleInputSuccess(){
        let data = {id: this.row.id, keywordId: this.value};
        if (this.label) {
          data.keywordName = this.label
        }
        if (data.keywordId === ''){
          this.$message.warning("请选择或输入关键字")
          return
        }
        this.$emit('keywordAdd', data);
        this.handleInputConfirm()
      },
      handleInputConfirm() {
        this.inputVisible = false
        this.value = '';
      },
      handleFieldEnter() {
        if (this.inputValue === ''){
          this.$message.warning("请选择或输入领域")
          return
        }
        this.$emit('domainAdd',{id:this.row.id,value:this.inputValue})
        this.handleFieldConfirm()
      },
      handleFieldConfirm() {
        this.fieldVisible = false
        this.inputValue = '';
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    margin-right: 70px;
    margin-bottom: 4px;
    //display: inline-block;
  }

  .span-sub {
    font-size: 14px;
    font-weight: 600;
  }

  .span-field {
    font-size: 14px;
    font-weight: 600;
  }

  .icon-tag-close:hover {
    border-radius: 50%;

    &:hover {
      background-color: #f86359;
      color: #fff;
    }
  }

  .button-new-tag {
    padding: 5px;
    transition: all .3s;
    background-color: transparent;

    &:hover {
      color: #6699f3;
      border-color: #6699f3;
    }

    &:active {
      color: #3d7ae4;
      border-color: #3d7ae4;
      box-shadow: 0 0 3px #6699f3;
    }
  }
  .button-new-tag_1 {
    padding: 5px;
    margin-left: 0px;
  }

  .button-new-tag-field {
    padding: 5px;
    transition: all .3s;
    background-color: transparent;

    &:hover {
      color: #6699f3;
      border-color: #6699f3;
    }

    &:active {
      color: #3d7ae4;
      border-color: #3d7ae4;
      box-shadow: 0 0 3px #6699f3;
    }
  }

  .input-new-tag {
    width: 100px;

    ::v-deep .el-input__inner {
      height: 24px;
      padding: 0 8px;
    }
  }
</style>
