import iconfont1 from '@/iconfont/iconfont.json'

const req = require.context('../../assets/icons/svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys()

const re = /\.\/(.*)\.svg/

const otherIcons = iconfont1.glyphs.map(
  icon =>{
    return{
      class: iconfont1.css_prefix_text + icon.font_class,
      name: icon.font_class
    }
  }
)
const icons = [
{id:3,class:'el-icon-delete-solid',name:'delete-solid'},
{id:4,class:'el-icon-delete',name:'delete'},
{id:5,class:'el-icon-s-tools',name:'s-tools'},
{id:6,class:'el-icon-setting',name:'setting'},
{id:7,class:'el-icon-user-solid',name:'user-solid'},
{id:8,class:'el-icon-user',name:'user'},
{id:9,class:'el-icon-phone',name:'phone'},
{id:10,class:'el-icon-phone-outline',name:'phone-outline'},
{id:11,class:'el-icon-more',name:'more'},
{id:12,class:'el-icon-more-outline',name:'more-outline'},
{id:13,class:'el-icon-star-on',name:'star-on'},
{id:14,class:'el-icon-star-off',name:'star-off'},
{id:15,class:'el-icon-s-goods',name:'s-goods'},
{id:16,class:'el-icon-goods',name:'goods'},
{id:17,class:'el-icon-warning',name:'warning'},
{id:18,class:'el-icon-warning-outline',name:'warning-outline'},
{id:19,class:'el-icon-question',name:'question'},
{id:20,class:'el-icon-info',name:'info'},
{id:21,class:'el-icon-remove',name:'remove'},
{id:22,class:'el-icon-circle-plus',name:'circle-plus'},
{id:23,class:'el-icon-success',name:'success'},
{id:24,class:'el-icon-error',name:'error'},
{id:25,class:'el-icon-zoom-in',name:'zoom-in'},
{id:26,class:'el-icon-zoom-out',name:'zoom-out'},
{id:27,class:'el-icon-remove-outline',name:'remove-outline'},
{id:28,class:'el-icon-circle-plus-outline',name:'circle-plus-outline'},
{id:29,class:'el-icon-circle-check',name:'circle-check'},
{id:30,class:'el-icon-circle-close',name:'circle-close'},
{id:31,class:'el-icon-s-help',name:'s-help'},
{id:32,class:'el-icon-help',name:'help'},
{id:33,class:'el-icon-minus',name:'minus'},
{id:34,class:'el-icon-plus',name:'plus'},
{id:35,class:'el-icon-check',name:'check'},
{id:36,class:'el-icon-close',name:'close'},
{id:37,class:'el-icon-picture',name:'picture'},
{id:38,class:'el-icon-picture-outline',name:'picture-outline'},
{id:39,class:'el-icon-picture-outline-round',name:'picture-outline-round'},
{id:40,class:'el-icon-upload',name:'upload'},
{id:41,class:'el-icon-upload2',name:'upload2'},
{id:42,class:'el-icon-download',name:'download'},
{id:43,class:'el-icon-camera-solid',name:'camera-solid'},
{id:44,class:'el-icon-camera',name:'camera'},
{id:45,class:'el-icon-video-camera-solid',name:'video-camera-solid'},
{id:46,class:'el-icon-video-camera',name:'video-camera'},
{id:47,class:'el-icon-message-solid',name:'message-solid'},
{id:48,class:'el-icon-bell',name:'bell'},
{id:49,class:'el-icon-s-cooperation',name:'s-cooperation'},
{id:50,class:'el-icon-s-order',name:'s-order'},
{id:51,class:'el-icon-s-platform',name:'s-platform'},
{id:52,class:'el-icon-s-fold',name:'s-fold'},
{id:53,class:'el-icon-s-unfold',name:'s-unfold'},
{id:54,class:'el-icon-s-operation',name:'s-operation'},
{id:55,class:'el-icon-s-promotion',name:'s-promotion'},
{id:56,class:'el-icon-s-home',name:'s-home'},
{id:57,class:'el-icon-s-release',name:'s-release'},
{id:58,class:'el-icon-s-ticket',name:'s-ticket'},
{id:59,class:'el-icon-s-management',name:'s-management'},
{id:60,class:'el-icon-s-open',name:'s-open'},
{id:61,class:'el-icon-s-shop',name:'s-shop'},
{id:62,class:'el-icon-s-marketing',name:'s-marketing'},
{id:63,class:'el-icon-s-flag',name:'s-flag'},
{id:64,class:'el-icon-s-comment',name:'s-comment'},
{id:65,class:'el-icon-s-finance',name:'s-finance'},
{id:66,class:'el-icon-s-claim',name:'s-claim'},
{id:67,class:'el-icon-s-custom',name:'s-custom'},
{id:68,class:'el-icon-s-opportunity',name:'s-opportunity'},
{id:69,class:'el-icon-s-data',name:'s-data'},
{id:70,class:'el-icon-s-check',name:'s-check'},
{id:71,class:'el-icon-s-grid',name:'s-grid'},
{id:72,class:'el-icon-menu',name:'menu'},
{id:73,class:'el-icon-share',name:'share'},
{id:74,class:'el-icon-d-caret',name:'d-caret'},
{id:75,class:'el-icon-caret-left',name:'caret-left'},
{id:76,class:'el-icon-caret-right',name:'caret-right'},
{id:77,class:'el-icon-caret-bottom',name:'caret-bottom'},
{id:78,class:'el-icon-caret-top',name:'caret-top'},
{id:79,class:'el-icon-bottom-left',name:'bottom-left'},
{id:80,class:'el-icon-bottom-right',name:'bottom-right'},
{id:81,class:'el-icon-back',name:'back'},
{id:82,class:'el-icon-right',name:'right'},
{id:83,class:'el-icon-bottom',name:'bottom'},
{id:84,class:'el-icon-top',name:'top'},
{id:85,class:'el-icon-top-left',name:'top-left'},
{id:86,class:'el-icon-top-right',name:'top-right'},
{id:87,class:'el-icon-arrow-left',name:'arrow-left'},
{id:88,class:'el-icon-arrow-right',name:'arrow-right'},
{id:89,class:'el-icon-arrow-down',name:'arrow-down'},
{id:90,class:'el-icon-arrow-up',name:'arrow-up'},
{id:91,class:'el-icon-d-arrow-left',name:'d-arrow-left'},
{id:92,class:'el-icon-d-arrow-right',name:'d-arrow-right'},
{id:93,class:'el-icon-video-pause',name:'video-pause'},
{id:94,class:'el-icon-video-play',name:'video-play'},
{id:95,class:'el-icon-refresh',name:'refresh'},
{id:96,class:'el-icon-refresh-right',name:'refresh-right'},
{id:97,class:'el-icon-refresh-left',name:'refresh-left'},
{id:98,class:'el-icon-finished',name:'finished'},
{id:99,class:'el-icon-sort',name:'sort'},
{id:100,class:'el-icon-sort-up',name:'sort-up'},
{id:101,class:'el-icon-sort-down',name:'sort-down'},
{id:102,class:'el-icon-rank',name:'rank'},
{id:104,class:'el-icon-view',name:'view'},
{id:105,class:'el-icon-c-scale-to-original',name:'c-scale-to-original'},
{id:106,class:'el-icon-date',name:'date'},
{id:107,class:'el-icon-edit',name:'edit'},
{id:108,class:'el-icon-edit-outline',name:'edit-outline'},
{id:109,class:'el-icon-folder',name:'folder'},
{id:110,class:'el-icon-folder-opened',name:'folder-opened'},
{id:111,class:'el-icon-folder-add',name:'folder-add'},
{id:112,class:'el-icon-folder-remove',name:'folder-remove'},
{id:113,class:'el-icon-folder-delete',name:'folder-delete'},
{id:114,class:'el-icon-folder-checked',name:'folder-checked'},
{id:115,class:'el-icon-tickets',name:'tickets'},
{id:116,class:'el-icon-document-remove',name:'document-remove'},
{id:117,class:'el-icon-document-delete',name:'document-delete'},
{id:118,class:'el-icon-document-copy',name:'document-copy'},
{id:119,class:'el-icon-document-checked',name:'document-checked'},
{id:120,class:'el-icon-document',name:'document'},
{id:121,class:'el-icon-document-add',name:'document-add'},
{id:122,class:'el-icon-printer',name:'printer'},
{id:123,class:'el-icon-paperclip',name:'paperclip'},
{id:124,class:'el-icon-takeaway-box',name:'takeaway-box'},
{id:125,class:'el-icon-search',name:'search'},
{id:126,class:'el-icon-monitor',name:'monitor'},
{id:127,class:'el-icon-attract',name:'attract'},
{id:128,class:'el-icon-mobile',name:'mobile'},
{id:129,class:'el-icon-scissors',name:'scissors'},
{id:130,class:'el-icon-umbrella',name:'umbrella'},
{id:131,class:'el-icon-headset',name:'headset'},
{id:132,class:'el-icon-brush',name:'brush'},
{id:133,class:'el-icon-mouse',name:'mouse'},
{id:134,class:'el-icon-coordinate',name:'coordinate'},
{id:135,class:'el-icon-magic-stick',name:'magic-stick'},
{id:136,class:'el-icon-reading',name:'reading'},
{id:137,class:'el-icon-data-line',name:'data-line'},
{id:138,class:'el-icon-data-board',name:'data-board'},
{id:139,class:'el-icon-pie-chart',name:'pie-chart'},
{id:140,class:'el-icon-data-analysis',name:'data-analysis'},
{id:141,class:'el-icon-collection-tag',name:'collection-tag'},
{id:142,class:'el-icon-film',name:'film'},
{id:143,class:'el-icon-suitcase',name:'suitcase'},
{id:144,class:'el-icon-suitcase-1',name:'suitcase-1'},
{id:145,class:'el-icon-receiving',name:'receiving'},
{id:146,class:'el-icon-collection',name:'collection'},
{id:147,class:'el-icon-files',name:'files'},
{id:148,class:'el-icon-notebook-1',name:'notebook-1'},
{id:149,class:'el-icon-notebook-2',name:'notebook-2'},
{id:150,class:'el-icon-toilet-paper',name:'toilet-paper'},
{id:151,class:'el-icon-office-building',name:'office-building'},
{id:152,class:'el-icon-school',name:'school'},
{id:153,class:'el-icon-table-lamp',name:'table-lamp'},
{id:154,class:'el-icon-house',name:'house'},
{id:155,class:'el-icon-no-smoking',name:'no-smoking'},
{id:156,class:'el-icon-smoking',name:'smoking'},
{id:157,class:'el-icon-shopping-cart-full',name:'shopping-cart-full'},
{id:158,class:'el-icon-shopping-cart-1',name:'shopping-cart-1'},
{id:159,class:'el-icon-shopping-cart-2',name:'shopping-cart-2'},
{id:160,class:'el-icon-shopping-bag-1',name:'shopping-bag-1'},
{id:161,class:'el-icon-shopping-bag-2',name:'shopping-bag-2'},
{id:162,class:'el-icon-sold-out',name:'sold-out'},
{id:163,class:'el-icon-sell',name:'sell'},
{id:164,class:'el-icon-present',name:'present'},
{id:165,class:'el-icon-box',name:'box'},
{id:166,class:'el-icon-bank-card',name:'bank-card'},
{id:167,class:'el-icon-money',name:'money'},
{id:168,class:'el-icon-coin',name:'coin'},
{id:169,class:'el-icon-wallet',name:'wallet'},
{id:170,class:'el-icon-discount',name:'discount'},
{id:171,class:'el-icon-price-tag',name:'price-tag'},
{id:172,class:'el-icon-news',name:'news'},
{id:173,class:'el-icon-guide',name:'guide'},
{id:174,class:'el-icon-male',name:'male'},
{id:175,class:'el-icon-female',name:'female'},
{id:176,class:'el-icon-thumb',name:'thumb'},
{id:177,class:'el-icon-cpu',name:'cpu'},
{id:178,class:'el-icon-link',name:'link'},
{id:179,class:'el-icon-connection',name:'connection'},
{id:180,class:'el-icon-open',name:'open'},
{id:181,class:'el-icon-turn-off',name:'turn-off'},
{id:182,class:'el-icon-set-up',name:'set-up'},
{id:183,class:'el-icon-chat-round',name:'chat-round'},
{id:184,class:'el-icon-chat-line-round',name:'chat-line-round'},
{id:185,class:'el-icon-chat-square',name:'chat-square'},
{id:186,class:'el-icon-chat-dot-round',name:'chat-dot-round'},
{id:187,class:'el-icon-chat-dot-square',name:'chat-dot-square'},
{id:188,class:'el-icon-chat-line-square',name:'chat-line-square'},
{id:189,class:'el-icon-message',name:'message'},
{id:190,class:'el-icon-postcard',name:'postcard'},
{id:191,class:'el-icon-position',name:'position'},
{id:192,class:'el-icon-turn-off-microphone',name:'turn-off-microphone'},
{id:193,class:'el-icon-microphone',name:'microphone'},
{id:194,class:'el-icon-close-notification',name:'close-notification'},
{id:195,class:'el-icon-bangzhu',name:'bangzhu'},
{id:196,class:'el-icon-time',name:'time'},
{id:197,class:'el-icon-odometer',name:'odometer'},
{id:198,class:'el-icon-crop',name:'crop'},
{id:199,class:'el-icon-aim',name:'aim'},
{id:200,class:'el-icon-switch-button',name:'switch-button'},
{id:201,class:'el-icon-full-screen',name:'full-screen'},
{id:202,class:'el-icon-copy-document',name:'copy-document'},
{id:203,class:'el-icon-mic',name:'mic'},
{id:204,class:'el-icon-stopwatch',name:'stopwatch'},
{id:205,class:'el-icon-medal-1',name:'medal-1'},
{id:206,class:'el-icon-medal',name:'medal'},
{id:207,class:'el-icon-trophy',name:'trophy'},
{id:208,class:'el-icon-trophy-1',name:'trophy-1'},
{id:209,class:'el-icon-first-aid-kit',name:'first-aid-kit'},
{id:210,class:'el-icon-discover',name:'discover'},
{id:211,class:'el-icon-place',name:'place'},
{id:212,class:'el-icon-location',name:'location'},
{id:213,class:'el-icon-location-outline',name:'location-outline'},
{id:214,class:'el-icon-location-information',name:'location-information'},
{id:215,class:'el-icon-add-location',name:'add-location'},
{id:216,class:'el-icon-delete-location',name:'delete-location'},
{id:217,class:'el-icon-map-location',name:'map-location'},
{id:218,class:'el-icon-alarm-clock',name:'alarm-clock'},
{id:219,class:'el-icon-timer',name:'timer'},
{id:220,class:'el-icon-watch-1',name:'watch-1'},
{id:221,class:'el-icon-watch',name:'watch'},
{id:222,class:'el-icon-lock',name:'lock'},
{id:223,class:'el-icon-unlock',name:'unlock'},
{id:224,class:'el-icon-key',name:'key'},
{id:225,class:'el-icon-service',name:'service'},
{id:226,class:'el-icon-mobile-phone',name:'mobile-phone'},
{id:227,class:'el-icon-bicycle',name:'bicycle'},
{id:228,class:'el-icon-truck',name:'truck'},
{id:229,class:'el-icon-ship',name:'ship'},
{id:230,class:'el-icon-basketball',name:'basketball'},
{id:231,class:'el-icon-football',name:'football'},
{id:232,class:'el-icon-soccer',name:'soccer'},
{id:233,class:'el-icon-baseball',name:'baseball'},
{id:234,class:'el-icon-wind-power',name:'wind-power'},
{id:235,class:'el-icon-light-rain',name:'light-rain'},
{id:236,class:'el-icon-lightning',name:'lightning'},
{id:237,class:'el-icon-heavy-rain',name:'heavy-rain'},
{id:238,class:'el-icon-sunrise',name:'sunrise'},
{id:239,class:'el-icon-sunrise-1',name:'sunrise-1'},
{id:240,class:'el-icon-sunset',name:'sunset'},
{id:241,class:'el-icon-sunny',name:'sunny'},
{id:242,class:'el-icon-cloudy',name:'cloudy'},
{id:243,class:'el-icon-partly-cloudy',name:'partly-cloudy'},
{id:244,class:'el-icon-cloudy-and-sunny',name:'cloudy-and-sunny'},
{id:245,class:'el-icon-moon',name:'moon'},
{id:246,class:'el-icon-moon-night',name:'moon-night'},
{id:247,class:'el-icon-dish',name:'dish'},
{id:248,class:'el-icon-dish-1',name:'dish-1'},
{id:249,class:'el-icon-food',name:'food'},
{id:250,class:'el-icon-chicken',name:'chicken'},
{id:251,class:'el-icon-fork-spoon',name:'fork-spoon'},
{id:252,class:'el-icon-knife-fork',name:'knife-fork'},
{id:253,class:'el-icon-burger',name:'burger'},
{id:254,class:'el-icon-tableware',name:'tableware'},
{id:255,class:'el-icon-sugar',name:'sugar'},
{id:256,class:'el-icon-dessert',name:'dessert'},
{id:257,class:'el-icon-ice-cream',name:'ice-cream'},
{id:258,class:'el-icon-hot-water',name:'hot-water'},
{id:259,class:'el-icon-water-cup',name:'water-cup'},
{id:260,class:'el-icon-coffee-cup',name:'coffee-cup'},
{id:261,class:'el-icon-cold-drink',name:'cold-drink'},
{id:262,class:'el-icon-goblet',name:'goblet'},
{id:263,class:'el-icon-goblet-full',name:'goblet-full'},
{id:264,class:'el-icon-goblet-square',name:'goblet-square'},
{id:265,class:'el-icon-goblet-square-full',name:'goblet-square-full'},
{id:266,class:'el-icon-refrigerator',name:'refrigerator'},
{id:267,class:'el-icon-grape',name:'grape'},
{id:268,class:'el-icon-watermelon',name:'watermelon'},
{id:269,class:'el-icon-cherry',name:'cherry'},
{id:270,class:'el-icon-apple',name:'apple'},
{id:271,class:'el-icon-pear',name:'pear'},
{id:272,class:'el-icon-orange',name:'orange'},
{id:273,class:'el-icon-coffee',name:'coffee'},
{id:274,class:'el-icon-ice-tea',name:'ice-tea'},
{id:275,class:'el-icon-ice-drink',name:'ice-drink'},
{id:276,class:'el-icon-milk-tea',name:'milk-tea'},
{id:277,class:'el-icon-potato-strips',name:'potato-strips'},
{id:278,class:'el-icon-lollipop',name:'lollipop'},
{id:279,class:'el-icon-ice-cream-square',name:'ice-cream-square'},
{id:280,class:'el-icon-ice-cream-round',name:'ice-cream-round'},
]


export default icons.concat(otherIcons)
