<!--
 * @Description: 
-->
<!--
 * @Description: 图标选择器
-->





<!-- <AUTHOR> -->
<template>
  <div class="icon-body">
    <el-input v-model="name" style="position: relative;" clearable placeholder="请输入图标名称" @clear="filterIcons"
      @input.native="filterIcons">
      <i slot="suffix" class="el-icon-search el-input__icon" />
    </el-input>
    <i class="ali-icon-shouye"> </i>
    <div class="icon-list">
      <div v-for="(item, index) in iconList" :key="index" @click="selectedIcon(item.class)">
        <i slot="prefix" :class="item.class"><span>{{ item.name }}</span></i>
      </div>
    </div>
  </div>
</template>

<script>
// import icons from './requireIcons'
import icons from './requireIcons-el'
export default {
  name: 'IconSelect',
  data() {
    return {
      name: '',
      iconList: icons
    }
  },
  methods: {
    filterIcons() {
      console.log(this.iconList)
      this.iconList = icons
      if (this.name) {
        this.iconList = this.iconList.filter(item => item.class.includes(this.name))
      }
    },
    selectedIcon(name) {
      this.$emit('selected', name)
      document.body.click()
    },
    reset() {
      this.name = ''
      this.iconList = icons
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.icon-body {
  width: 100%;
  padding: 10px;

  .icon-list {
    height: 200px;
    overflow-y: scroll;

    div {
      height: 30px;
      line-height: 30px;
      cursor: pointer;
      width: 33%;
      float: left;
    }

    span {
      font-weight: normal;
      font-size: 14px;
      display: inline-block;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
      margin-left: 10px;
      max-width: 90px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
