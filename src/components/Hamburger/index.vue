<!--
 * @Description: 
-->
<template>
  <div style="padding: 0 15px;" @click="toggleClick">
    <!-- <i class="hamburger el-icon-d-arrow-left " :class="{ 'is-active': isActive }">
                    <icon></icon>
                  </i> -->
    <i class="el-icon-s-unfold hamburger" v-if="isActive" />
    <i class="el-icon-s-fold hamburger" v-else />
    <!-- <svg-icon icon-class="indent" :class="{'is-active':isActive}" class="hamburger" /> -->


  </div>
</template>

<script>
export default {
  name: 'Hamburger',
  props: {
    isActive: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleClick() {
      this.$emit('toggleClick')
    }
  }
}
</script>

<style scoped>
.hamburger {
  display: inline-block;
  vertical-align: middle;
  font-size: 26px !important;
  color: #777;
  /*color: #08ff25;*/
  transform: rotate(180deg);
}

.hamburger.is-active {
  transform: rotate(0deg);
}
</style>
