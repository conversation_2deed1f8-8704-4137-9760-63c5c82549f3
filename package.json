{"name": "vue-element-admin", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve --open", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "analyz": "NODE_ENV=production npm_config_report=true npm run build"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@logicflow/core": "^1.2.11", "@logicflow/extension": "^1.2.11", "@riophae/vue-treeselect": "^0.4.0", "@tinymce/tinymce-vue": "^3.0.1", "@turf/turf": "^7.2.0", "axios": "^0.22.0", "clipboard": "2.0.4", "docx": "^9.3.0", "e-icon-picker": "^1.1.7", "echarts": "^4.9.0", "element-resize-detector": "^1.2.4", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "jquery": "^3.7.1", "js-base64": "^3.7.7", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "jsencrypt": "^3.2.1", "md5": "^2.3.0", "moment": "^2.29.3", "nprogress": "0.2.0", "overlayscrollbars": "^1.13.1", "path-to-regexp": "2.4.0", "sheetjs-style": "^0.15.8", "spark-md5": "^3.0.2", "uuid": "^9.0.0", "vue": "2.6.13", "vue-count-to": "1.0.13", "vue-router": "3.0.2", "vue-scale-resize": "^0.1.4", "vue-scroller": "^2.2.4", "vue-uuid": "^3.0.0", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.8.8", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "less": "^4.1.2", "less-loader": "^4.0.5", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.9.0", "plop": "^2.7.4", "postcss": "^7.0.39", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "^4.3.0", "svgo": "1.2.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue-template-compiler": "2.6.13", "vue2-ace-editor": "0.0.15", "vue_qrcodes": "^1.1.3"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "chrome 55"]}